#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام النسخ الاحتياطي المتقدم
Advanced Backup and Restore System
"""

import os
import shutil
import sqlite3
import json
import zipfile
from datetime import datetime, timedelta
import hashlib
from database import VacationDatabase

class BackupSystem:
    def __init__(self, db_path='vacation_system.db'):
        self.db_path = db_path
        self.backup_dir = 'backups'
        self.config_file = 'backup_config.json'
        self.init_backup_system()
    
    def init_backup_system(self):
        """تهيئة نظام النسخ الاحتياطي"""
        # إنشاء مجلد النسخ الاحتياطية
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
        
        # إنشاء ملف الإعدادات الافتراضي
        if not os.path.exists(self.config_file):
            default_config = {
                'auto_backup_enabled': True,
                'backup_interval_hours': 24,
                'max_backups_to_keep': 30,
                'compress_backups': True,
                'include_files': [
                    'vacation_system.db',
                    'notifications.db',
                    'نموذج_الرصيد_الابتدائي.xlsx'
                ],
                'last_backup_time': None,
                'backup_location': self.backup_dir
            }
            self.save_config(default_config)
    
    def load_config(self):
        """تحميل إعدادات النسخ الاحتياطي"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return self.init_backup_system()
    
    def save_config(self, config):
        """حفظ إعدادات النسخ الاحتياطي"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def create_backup(self, backup_type='manual', description=''):
        """إنشاء نسخة احتياطية"""
        config = self.load_config()
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"backup_{backup_type}_{timestamp}"
        
        if config.get('compress_backups', True):
            backup_file = os.path.join(self.backup_dir, f"{backup_name}.zip")
            return self._create_compressed_backup(backup_file, config, description)
        else:
            backup_folder = os.path.join(self.backup_dir, backup_name)
            return self._create_folder_backup(backup_folder, config, description)
    
    def _create_compressed_backup(self, backup_file, config, description):
        """إنشاء نسخة احتياطية مضغوطة"""
        try:
            with zipfile.ZipFile(backup_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # إضافة الملفات المحددة في الإعدادات
                for file_path in config.get('include_files', []):
                    if os.path.exists(file_path):
                        zipf.write(file_path, os.path.basename(file_path))
                
                # إضافة ملفات إضافية مهمة
                additional_files = [
                    'requirements.txt',
                    'database.py',
                    'advanced_reports.py',
                    'notification_system.py',
                    'backup_config.json'
                ]
                
                for file_path in additional_files:
                    if os.path.exists(file_path):
                        zipf.write(file_path, f"system/{os.path.basename(file_path)}")
                
                # إضافة معلومات النسخة الاحتياطية
                backup_info = {
                    'created_at': datetime.now().isoformat(),
                    'type': 'compressed',
                    'description': description,
                    'files_count': len(zipf.namelist()),
                    'database_size': os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
                    'version': '1.0'
                }
                
                zipf.writestr('backup_info.json', json.dumps(backup_info, ensure_ascii=False, indent=2))
            
            # حساب checksum للتحقق من سلامة النسخة
            checksum = self._calculate_file_checksum(backup_file)
            
            # تحديث إعدادات آخر نسخة احتياطية
            config['last_backup_time'] = datetime.now().isoformat()
            self.save_config(config)
            
            # تنظيف النسخ القديمة
            self._cleanup_old_backups(config)
            
            backup_size = os.path.getsize(backup_file)
            return True, f"تم إنشاء النسخة الاحتياطية: {backup_file} ({backup_size:,} بايت، checksum: {checksum[:8]})"
            
        except Exception as e:
            return False, f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
    
    def _create_folder_backup(self, backup_folder, config, description):
        """إنشاء نسخة احتياطية في مجلد"""
        try:
            os.makedirs(backup_folder, exist_ok=True)
            
            files_copied = 0
            total_size = 0
            
            # نسخ الملفات المحددة
            for file_path in config.get('include_files', []):
                if os.path.exists(file_path):
                    dest_path = os.path.join(backup_folder, os.path.basename(file_path))
                    shutil.copy2(file_path, dest_path)
                    files_copied += 1
                    total_size += os.path.getsize(dest_path)
            
            # إنشاء ملف معلومات النسخة الاحتياطية
            backup_info = {
                'created_at': datetime.now().isoformat(),
                'type': 'folder',
                'description': description,
                'files_count': files_copied,
                'total_size': total_size,
                'version': '1.0'
            }
            
            info_file = os.path.join(backup_folder, 'backup_info.json')
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=2)
            
            # تحديث الإعدادات
            config['last_backup_time'] = datetime.now().isoformat()
            self.save_config(config)
            
            # تنظيف النسخ القديمة
            self._cleanup_old_backups(config)
            
            return True, f"تم إنشاء النسخة الاحتياطية: {backup_folder} ({files_copied} ملف، {total_size:,} بايت)"
            
        except Exception as e:
            return False, f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
    
    def list_backups(self):
        """عرض قائمة النسخ الاحتياطية"""
        backups = []
        
        if not os.path.exists(self.backup_dir):
            return backups
        
        for item in os.listdir(self.backup_dir):
            item_path = os.path.join(self.backup_dir, item)
            
            if item.endswith('.zip'):
                # نسخة مضغوطة
                backup_info = self._get_compressed_backup_info(item_path)
                if backup_info:
                    backup_info['path'] = item_path
                    backup_info['name'] = item
                    backup_info['size'] = os.path.getsize(item_path)
                    backups.append(backup_info)
            
            elif os.path.isdir(item_path) and item.startswith('backup_'):
                # نسخة مجلد
                backup_info = self._get_folder_backup_info(item_path)
                if backup_info:
                    backup_info['path'] = item_path
                    backup_info['name'] = item
                    backup_info['size'] = self._get_folder_size(item_path)
                    backups.append(backup_info)
        
        # ترتيب حسب التاريخ (الأحدث أولاً)
        backups.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        return backups
    
    def _get_compressed_backup_info(self, zip_path):
        """الحصول على معلومات النسخة المضغوطة"""
        try:
            with zipfile.ZipFile(zip_path, 'r') as zipf:
                if 'backup_info.json' in zipf.namelist():
                    info_data = zipf.read('backup_info.json')
                    return json.loads(info_data.decode('utf-8'))
                else:
                    # نسخة قديمة بدون معلومات
                    return {
                        'created_at': datetime.fromtimestamp(os.path.getctime(zip_path)).isoformat(),
                        'type': 'compressed',
                        'description': 'نسخة قديمة',
                        'files_count': len(zipf.namelist()),
                        'version': 'unknown'
                    }
        except Exception:
            return None
    
    def _get_folder_backup_info(self, folder_path):
        """الحصول على معلومات نسخة المجلد"""
        info_file = os.path.join(folder_path, 'backup_info.json')
        try:
            if os.path.exists(info_file):
                with open(info_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # نسخة قديمة بدون معلومات
                return {
                    'created_at': datetime.fromtimestamp(os.path.getctime(folder_path)).isoformat(),
                    'type': 'folder',
                    'description': 'نسخة قديمة',
                    'files_count': len(os.listdir(folder_path)),
                    'version': 'unknown'
                }
        except Exception:
            return None
    
    def restore_backup(self, backup_path, restore_location='.'):
        """استعادة نسخة احتياطية"""
        if not os.path.exists(backup_path):
            return False, "النسخة الاحتياطية غير موجودة"
        
        try:
            if backup_path.endswith('.zip'):
                return self._restore_compressed_backup(backup_path, restore_location)
            elif os.path.isdir(backup_path):
                return self._restore_folder_backup(backup_path, restore_location)
            else:
                return False, "نوع النسخة الاحتياطية غير مدعوم"
                
        except Exception as e:
            return False, f"خطأ في استعادة النسخة الاحتياطية: {str(e)}"
    
    def _restore_compressed_backup(self, zip_path, restore_location):
        """استعادة نسخة مضغوطة"""
        files_restored = 0
        
        with zipfile.ZipFile(zip_path, 'r') as zipf:
            for file_info in zipf.filelist:
                if file_info.filename == 'backup_info.json':
                    continue  # تخطي ملف المعلومات
                
                if file_info.filename.startswith('system/'):
                    # ملفات النظام - استعادة اختيارية
                    continue
                
                # استعادة الملف
                source = zipf.open(file_info)
                target_path = os.path.join(restore_location, file_info.filename)
                
                # إنشاء المجلدات إذا لزم الأمر
                os.makedirs(os.path.dirname(target_path), exist_ok=True)
                
                with open(target_path, 'wb') as target:
                    shutil.copyfileobj(source, target)
                
                files_restored += 1
        
        return True, f"تم استعادة {files_restored} ملف من النسخة الاحتياطية"
    
    def _restore_folder_backup(self, folder_path, restore_location):
        """استعادة نسخة مجلد"""
        files_restored = 0
        
        for item in os.listdir(folder_path):
            if item == 'backup_info.json':
                continue  # تخطي ملف المعلومات
            
            source_path = os.path.join(folder_path, item)
            target_path = os.path.join(restore_location, item)
            
            if os.path.isfile(source_path):
                shutil.copy2(source_path, target_path)
                files_restored += 1
        
        return True, f"تم استعادة {files_restored} ملف من النسخة الاحتياطية"
    
    def delete_backup(self, backup_path):
        """حذف نسخة احتياطية"""
        try:
            if os.path.isfile(backup_path):
                os.remove(backup_path)
            elif os.path.isdir(backup_path):
                shutil.rmtree(backup_path)
            else:
                return False, "النسخة الاحتياطية غير موجودة"
            
            return True, "تم حذف النسخة الاحتياطية بنجاح"
            
        except Exception as e:
            return False, f"خطأ في حذف النسخة الاحتياطية: {str(e)}"
    
    def _cleanup_old_backups(self, config):
        """تنظيف النسخ الاحتياطية القديمة"""
        max_backups = config.get('max_backups_to_keep', 30)
        backups = self.list_backups()
        
        if len(backups) > max_backups:
            # حذف النسخ الزائدة (الأقدم)
            backups_to_delete = backups[max_backups:]
            
            for backup in backups_to_delete:
                try:
                    if os.path.isfile(backup['path']):
                        os.remove(backup['path'])
                    elif os.path.isdir(backup['path']):
                        shutil.rmtree(backup['path'])
                except Exception:
                    pass  # تجاهل أخطاء الحذف
    
    def _calculate_file_checksum(self, file_path):
        """حساب checksum للملف"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _get_folder_size(self, folder_path):
        """حساب حجم المجلد"""
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(folder_path):
            for filename in filenames:
                file_path = os.path.join(dirpath, filename)
                if os.path.exists(file_path):
                    total_size += os.path.getsize(file_path)
        return total_size
    
    def check_auto_backup_needed(self):
        """التحقق من الحاجة لنسخة احتياطية تلقائية"""
        config = self.load_config()
        
        if not config.get('auto_backup_enabled', True):
            return False
        
        last_backup_time = config.get('last_backup_time')
        if not last_backup_time:
            return True  # لم يتم إنشاء نسخة احتياطية من قبل
        
        last_backup = datetime.fromisoformat(last_backup_time)
        interval_hours = config.get('backup_interval_hours', 24)
        
        time_since_backup = datetime.now() - last_backup
        return time_since_backup.total_seconds() > (interval_hours * 3600)
    
    def run_auto_backup(self):
        """تشغيل النسخ الاحتياطي التلقائي"""
        if self.check_auto_backup_needed():
            return self.create_backup('auto', 'نسخة احتياطية تلقائية')
        return False, "لا حاجة لنسخة احتياطية الآن"
    
    def get_backup_statistics(self):
        """الحصول على إحصائيات النسخ الاحتياطية"""
        backups = self.list_backups()
        
        if not backups:
            return {
                'total_backups': 0,
                'total_size': 0,
                'latest_backup': None,
                'oldest_backup': None
            }
        
        total_size = sum(backup.get('size', 0) for backup in backups)
        latest_backup = backups[0] if backups else None
        oldest_backup = backups[-1] if backups else None
        
        return {
            'total_backups': len(backups),
            'total_size': total_size,
            'latest_backup': latest_backup,
            'oldest_backup': oldest_backup,
            'compressed_backups': len([b for b in backups if b.get('type') == 'compressed']),
            'folder_backups': len([b for b in backups if b.get('type') == 'folder'])
        }

    def verify_backup_integrity(self, backup_path):
        """التحقق من سلامة النسخة الاحتياطية"""
        try:
            if backup_path.endswith('.zip'):
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    # اختبار سلامة الملف المضغوط
                    bad_file = zipf.testzip()
                    if bad_file:
                        return False, f"ملف تالف في النسخة الاحتياطية: {bad_file}"

                    # التحقق من وجود الملفات الأساسية
                    required_files = ['vacation_system.db']
                    missing_files = []

                    for required_file in required_files:
                        if required_file not in zipf.namelist():
                            missing_files.append(required_file)

                    if missing_files:
                        return False, f"ملفات مفقودة: {', '.join(missing_files)}"

                    return True, "النسخة الاحتياطية سليمة"

            elif os.path.isdir(backup_path):
                # التحقق من نسخة المجلد
                required_files = ['vacation_system.db']
                missing_files = []

                for required_file in required_files:
                    file_path = os.path.join(backup_path, required_file)
                    if not os.path.exists(file_path):
                        missing_files.append(required_file)

                if missing_files:
                    return False, f"ملفات مفقودة: {', '.join(missing_files)}"

                return True, "النسخة الاحتياطية سليمة"

            else:
                return False, "نوع النسخة الاحتياطية غير مدعوم"

        except Exception as e:
            return False, f"خطأ في التحقق من سلامة النسخة: {str(e)}"

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع نهائي لنظام إدارة الإجازات
"""

def quick_test():
    """اختبار سريع للتأكد من جاهزية النظام"""
    
    print("🔥 اختبار سريع نهائي لنظام إدارة الإجازات")
    print("=" * 60)
    
    results = {}
    
    # 1. اختبار استيراد المكتبات الأساسية
    try:
        import pandas
        import openpyxl
        from PyQt5.QtWidgets import QApplication
        import sqlite3
        print("✅ جميع المكتبات متوفرة")
        results['libraries'] = True
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        results['libraries'] = False
    
    # 2. اختبار وجود الملفات الأساسية
    import os
    essential_files = [
        'database.py',
        'main_window.py', 
        'main.py',
        'run_app.py',
        'نموذج_الرصيد_الابتدائي.xlsx'
    ]
    
    missing_files = []
    for file in essential_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if not missing_files:
        print("✅ جميع الملفات الأساسية موجودة")
        results['files'] = True
    else:
        print(f"❌ ملفات مفقودة: {missing_files}")
        results['files'] = False
    
    # 3. اختبار قاعدة البيانات
    try:
        from database import VacationDatabase
        db = VacationDatabase('test_quick.db')
        
        # اختبار سريع للعمليات
        success1, _ = db.add_daily_request('اختبار', '123', 'موظف', 'قسم', 'سنوية', '2024-07-10', 3)
        success2, _ = db.add_hourly_request('اختبار', '2024-07-10', 8)
        
        if success1 and success2:
            print("✅ قاعدة البيانات تعمل بشكل صحيح")
            results['database'] = True
        else:
            print("❌ مشكلة في قاعدة البيانات")
            results['database'] = False
            
        # تنظيف
        os.remove('test_quick.db')
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        results['database'] = False
    
    # 4. اختبار ملف Excel
    try:
        import pandas as pd
        df = pd.read_excel('نموذج_الرصيد_الابتدائي.xlsx')
        
        required_columns = ['الاسم واللقب', 'الرتبة', 'عدد الأيام', 'التاريخ']
        if all(col in df.columns for col in required_columns):
            print("✅ ملف Excel صالح ويحتوي على البيانات المطلوبة")
            results['excel'] = True
        else:
            print("❌ ملف Excel لا يحتوي على الأعمدة المطلوبة")
            results['excel'] = False
            
    except Exception as e:
        print(f"❌ خطأ في ملف Excel: {e}")
        results['excel'] = False
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("📊 النتائج النهائية:")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        test_arabic = {
            'libraries': 'المكتبات',
            'files': 'الملفات',
            'database': 'قاعدة البيانات', 
            'excel': 'ملف Excel'
        }
        print(f"  {test_arabic[test_name]}: {status}")
    
    print(f"\nالمجموع: {passed}/{total}")
    
    if passed == total:
        print("\n🎉🎉🎉 تهانينا! النظام جاهز للعمل بنسبة 100% 🎉🎉🎉")
        print("\n🚀 يمكنك الآن تشغيل البرنامج:")
        print("   🖱️  انقر نقراً مزدوجاً على: تشغيل_البرنامج.bat")
        print("   💻 أو من سطر الأوامر: python run_app.py")
        print("\n📋 بيانات الدخول:")
        print("   👤 اسم المستخدم: admin")
        print("   🔐 كلمة المرور: admin123")
        return True
    else:
        print(f"\n⚠️ يوجد {total - passed} مشكلة تحتاج إلى حل")
        print("🔧 شغّل: python setup.py لإعادة الإعداد")
        return False

if __name__ == '__main__':
    quick_test()
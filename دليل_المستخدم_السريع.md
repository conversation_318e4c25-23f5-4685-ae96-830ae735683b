# دليل المستخدم السريع - نظام إدارة الإجازات

## 🚀 البدء السريع

### 1. التثبيت والإعداد
```bash
# تشغيل ملف الإعداد
python setup.py
```

### 2. تشغيل البرنامج
- **الطريقة السهلة:** انقر نقراً مزدوجاً على `تشغيل_البرنامج.bat`
- **من سطر الأوامر:** `python run_app.py`

### 3. تسجيل الدخول
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

---

## 📋 العمليات الأساسية

### 1. استيراد الرصيد الابتدائي
1. انقر على "📥 استيراد رصيد"
2. اختر ملف Excel (استخدم الملف النموذجي: `نموذج_الرصيد_الابتدائي.xlsx`)
3. انقر "فتح"

### 2. إضافة طلب إجازة يومية
1. انقر على "📝 طلب إجازة يومية"
2. املأ البيانات:
   - الاسم واللقب
   - رقم القيد
   - الوظيفة
   - الفوج
   - نوع الإجازة
   - تاريخ الخروج
   - عدد الأيام
3. انقر "حفظ"

### 3. إضافة طلب إجازة ساعية
1. انقر على "⏱️ طلب إجازة ساعية"
2. املأ البيانات:
   - الاسم واللقب
   - تاريخ الاستفادة
   - عدد الساعات
3. سيتم حساب المعادل بالأيام تلقائياً
4. انقر "حفظ"

### 4. إدراج إجازة إضافية
1. انقر على "➕ إدراج إجازات"
2. املأ البيانات:
   - الاسم واللقب
   - التاريخ
   - عدد الأيام
   - السبب
3. انقر "حفظ"

### 5. البحث عن موظف
1. انقر على "🔍 استعلام"
2. أدخل اسم الموظف
3. انقر "بحث"
4. ستظهر معلومات الرصيد

### 6. توليد التقارير
1. انقر على "📊 التقارير"
2. اختر الموظف من القائمة
3. انقر "توليد التقرير"
4. يمكنك طباعة التقرير

### 7. إدارة الطلبات
- **للتعديل:** انقر على "✏️ تعديل الطلبات"
- **للحذف:** انقر على "🗑️ حذف الطلبات"

---

## ⚙️ معادلات النظام

### حساب الإجازة الساعية
```
عدد الأيام = (عدد الساعات × 3) ÷ 24
```

### حساب الرصيد الصافي
```
الرصيد الصافي = الرصيد الابتدائي + الإجازات المدرجة - الإجازات المستفادة
```

---

## 📊 أنواع الإجازات المدعومة

- **إجازة سنوية:** الإجازة العادية السنوية
- **إجازة مرضية:** إجازة لأسباب صحية
- **إجازة طارئة:** إجازة لظروف طارئة
- **إجازة أمومة:** إجازة الولادة والأمومة
- **أخرى:** أنواع إجازات أخرى

---

## 🗂️ تنسيق ملف Excel

يجب أن يحتوي ملف Excel على الأعمدة التالية:

| الاسم واللقب | الرتبة | عدد الأيام | التاريخ |
|-------------|--------|------------|---------|
| محمد أحمد | موظف | 30 | 2024-01-01 |
| فاطمة علي | مشرفة | 25 | 2024-01-01 |

---

## ❗ نصائح مهمة

### ✅ افعل
- احفظ نسخة احتياطية من قاعدة البيانات دورياً
- تأكد من صحة أسماء الموظفين عند الإدخال
- راجع التقارير دورياً للتأكد من دقة البيانات

### ❌ تجنب
- حذف ملف قاعدة البيانات `vacation_system.db`
- تعديل أسماء الأعمدة في ملف Excel
- إدخال أرقام سالبة لعدد الأيام

---

## 🔧 حل المشاكل الشائعة

### لا يعمل البرنامج
1. تأكد من تثبيت Python
2. شغّل `python setup.py`
3. شغّل `python test_app.py` للاختبار

### خطأ في استيراد ملف Excel
1. تأكد من وجود الأعمدة المطلوبة
2. تأكد من تنسيق التواريخ (YYYY-MM-DD)
3. تأكد من عدم وجود صفوف فارغة

### لا تظهر بيانات الموظف
1. تأكد من صحة كتابة الاسم
2. تأكد من استيراد الرصيد الابتدائي
3. راجع إملاء الاسم في جميع المعاملات

---

## 📞 الدعم

للمساعدة أو الإبلاغ عن مشاكل:
- راجع ملف `README.md` للتفاصيل الكاملة
- شغّل `python test_app.py` لاختبار النظام
- تأكد من تحديث المكتبات

---
🏢 نظام إدارة الإجازات - دليل سريع
==========================================

🚀 التشغيل:
-----------
python تشغيل_النظام_البسيط.py
أو
python نظام_قائمة_الأسماء_المحسّن.py

🔑 بيانات الدخول:
------------------
المستخدم: admin
كلمة المرور: admin123

✅ المزايا الجديدة:
-------------------
1. مقاسات نوافذ محسّنة ومناسبة
2. قائمة منسدلة للأسماء مع زر إضافة ➕
3. إدراج يدوي لعدد الأيام والساعات
4. توجيه عربي كامل (RTL)

👥 الأسماء الافتراضية:
----------------------
• أحمد محمد علي
• فاطمة أحمد خالد  
• محمد خالد عبدالله
• نور سالم محمد
• علي حسن أحمد
• مريم عبدالله سعد

➕ إضافة أسماء جديدة:
---------------------
1. اضغط زر ➕ بجانب قائمة الأسماء
2. أدخل الاسم الجديد
3. سيُضاف تلقائياً ويُحفظ

📝 تقديم طلب إجازة يومية:
---------------------------
👤 اختر الموظف من القائمة
📅 من: 2024-12-01 (يدوي)
📅 إلى: 2024-12-05 (يدوي)  
📊 الأيام: 5 يوم (يدوي بـ SpinBox)
📋 النوع: إجازة اعتيادية (يدوي)
📝 السبب: استراحة
📄 ملاحظة: اختياري

⏱️ تقديم طلب إجازة ساعية:
---------------------------
👤 اختر الموظف من القائمة
📅 التاريخ: 2024-12-01 (يدوي)
🕘 من: 09:00 (يدوي)
🕐 إلى: 11:00 (يدوي)
⏰ الساعات: 2 ساعة (يدوي بـ SpinBox)
📋 النوع: موعد طبي (يدوي)
📝 السبب: فحص

📊 عرض الطلبات:
---------------
اضغط "📋 عرض الطلبات" من القائمة الرئيسية

💾 ملفات البيانات:
------------------
• employees_list.json - قائمة الأسماء
• vacation_with_dropdown.json - الطلبات المحفوظة

🎯 نصائح:
----------
• استخدم تنسيق YYYY-MM-DD للتواريخ
• استخدم تنسيق HH:MM للأوقات
• اضغط ➕ لإضافة موظفين جدد
• النظام يحفظ تلقائياً

🚨 حل المشاكل:
--------------
• إذا كانت القائمة فارغة: احذف employees_list.json وأعد التشغيل
• إذا لم تظهر الطلبات: تأكد من ملف vacation_with_dropdown.json
• إذا فشل التشغيل: تأكد من تثبيت PyQt5

📞 للمساعدة:
-------------
راجع هذا الدليل أو جرب التشغيل من جديد
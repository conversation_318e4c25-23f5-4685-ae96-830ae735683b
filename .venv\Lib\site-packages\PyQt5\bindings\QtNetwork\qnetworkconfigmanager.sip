// qnetworkconfigmanager.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QNetworkConfigurationManager : public QObject
{
%TypeHeaderCode
#include <qnetworkconfigmanager.h>
%End

public:
    enum Capability
    {
        CanStartAndStopInterfaces,
        DirectConnectionRouting,
        SystemSessionSupport,
        ApplicationLevelRoaming,
        ForcedRoaming,
        DataStatistics,
        NetworkSessionRequired,
    };

    typedef QFlags<QNetworkConfigurationManager::Capability> Capabilities;
    explicit QNetworkConfigurationManager(QObject *parent /TransferThis/ = 0);
    virtual ~QNetworkConfigurationManager();
    QNetworkConfigurationManager::Capabilities capabilities() const;
    QNetworkConfiguration defaultConfiguration() const;
    QList<QNetworkConfiguration> allConfigurations(QNetworkConfiguration::StateFlags flags = QNetworkConfiguration::StateFlags()) const;
    QNetworkConfiguration configurationFromIdentifier(const QString &identifier) const;
    void updateConfigurations();
    bool isOnline() const;

signals:
    void configurationAdded(const QNetworkConfiguration &config);
    void configurationRemoved(const QNetworkConfiguration &config);
    void configurationChanged(const QNetworkConfiguration &config);
    void onlineStateChanged(bool isOnline);
    void updateCompleted();
};

QFlags<QNetworkConfigurationManager::Capability> operator|(QNetworkConfigurationManager::Capability f1, QFlags<QNetworkConfigurationManager::Capability> f2);
QFlags<QNetworkConfigurationManager::Capability> operator|(QNetworkConfigurationManager::Capability f1, QNetworkConfigurationManager::Capability f2);

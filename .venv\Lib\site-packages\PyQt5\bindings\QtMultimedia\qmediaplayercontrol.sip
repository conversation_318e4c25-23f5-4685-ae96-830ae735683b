// qmediaplayercontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMediaPlayerControl : public QMediaControl
{
%TypeHeaderCode
#include <qmediaplayercontrol.h>
%End

public:
    virtual ~QMediaPlayerControl();
    virtual QMediaPlayer::State state() const = 0;
    virtual QMediaPlayer::MediaStatus mediaStatus() const = 0;
    virtual qint64 duration() const = 0;
    virtual qint64 position() const = 0;
    virtual void setPosition(qint64 position) = 0;
    virtual int volume() const = 0;
    virtual void setVolume(int volume) = 0;
    virtual bool isMuted() const = 0;
    virtual void setMuted(bool mute) = 0;
    virtual int bufferStatus() const = 0;
    virtual bool isAudioAvailable() const = 0;
    virtual bool isVideoAvailable() const = 0;
    virtual bool isSeekable() const = 0;
    virtual QMediaTimeRange availablePlaybackRanges() const = 0;
    virtual qreal playbackRate() const = 0;
    virtual void setPlaybackRate(qreal rate) = 0;
    virtual QMediaContent media() const = 0;
    virtual const QIODevice *mediaStream() const = 0;
    virtual void setMedia(const QMediaContent &media, QIODevice *stream) = 0;
    virtual void play() = 0;
    virtual void pause() = 0;
    virtual void stop() = 0;

signals:
    void mediaChanged(const QMediaContent &content);
    void durationChanged(qint64 duration);
    void positionChanged(qint64 position);
    void stateChanged(QMediaPlayer::State newState);
    void mediaStatusChanged(QMediaPlayer::MediaStatus status);
    void volumeChanged(int volume);
    void mutedChanged(bool mute);
    void audioAvailableChanged(bool audioAvailable);
    void videoAvailableChanged(bool videoAvailable);
    void bufferStatusChanged(int percentFilled);
    void seekableChanged(bool seekable);
    void availablePlaybackRangesChanged(const QMediaTimeRange &ranges);
    void playbackRateChanged(qreal rate);
    void error(int error, const QString &errorString);

protected:
    explicit QMediaPlayerControl(QObject *parent /TransferThis/ = 0);
};

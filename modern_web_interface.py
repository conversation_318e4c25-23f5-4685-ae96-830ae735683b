#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة الويب الحديثة لنظام إدارة الإجازات
"""

import os
import sys
import json
import sqlite3
from datetime import datetime
from flask import Flask, render_template_string, request, jsonify, session, redirect, url_for
import threading

class VacationWebApp:
    """تطبيق الويب لنظام إدارة الإجازات"""
    
    def __init__(self, db_path='vacation_system.db'):
        self.app = Flask(__name__)
        self.app.secret_key = 'vacation_system_secret_key_2024'
        self.db_path = db_path
        self.setup_routes()
        
    def setup_routes(self):
        """إعداد المسارات"""
        
        @self.app.route('/')
        def index():
            """الصفحة الرئيسية"""
            if 'user_id' not in session:
                return redirect(url_for('login'))
            return render_template_string(self.get_dashboard_template())
            
        @self.app.route('/login', methods=['GET', 'POST'])
        def login():
            """صفحة تسجيل الدخول"""
            if request.method == 'POST':
                username = request.form['username']
                password = request.form['password']
                
                # التحقق من المستخدم (مبسط)
                if username == 'admin' and password == 'admin123':
                    session['user_id'] = 1
                    session['username'] = username
                    session['full_name'] = 'مدير النظام'
                    return redirect(url_for('index'))
                else:
                    return render_template_string(self.get_login_template(), error='بيانات غير صحيحة')
                    
            return render_template_string(self.get_login_template())
            
        @self.app.route('/logout')
        def logout():
            """تسجيل الخروج"""
            session.clear()
            return redirect(url_for('login'))
            
        @self.app.route('/requests')
        def requests():
            """صفحة الطلبات"""
            if 'user_id' not in session:
                return redirect(url_for('login'))
            return render_template_string(self.get_requests_template())
            
        @self.app.route('/api/requests')
        def api_requests():
            """API الطلبات"""
            # بيانات وهمية
            sample_requests = [
                {
                    'id': 1,
                    'employee_name': 'أحمد محمد',
                    'type': 'يومية',
                    'days': 3,
                    'start_date': '2024-01-15',
                    'status': 'معلق',
                    'department': 'التقنية'
                },
                {
                    'id': 2,
                    'employee_name': 'فاطمة علي',
                    'type': 'ساعية',
                    'days': 0.5,
                    'start_date': '2024-01-16',
                    'status': 'مقبول',
                    'department': 'المالية'
                },
                {
                    'id': 3,
                    'employee_name': 'محمد أحمد',
                    'type': 'إضافية',
                    'days': 1,
                    'start_date': '2024-01-17',
                    'status': 'مرفوض',
                    'department': 'التسويق'
                }
            ]
            return jsonify(sample_requests)
            
        @self.app.route('/api/stats')
        def api_stats():
            """API الإحصائيات"""
            stats = {
                'total_requests': 245,
                'pending_requests': 22,
                'approved_requests': 213,
                'rejected_requests': 10,
                'total_employees': 156,
                'active_employees': 134
            }
            return jsonify(stats)
            
        @self.app.route('/new_request', methods=['GET', 'POST'])
        def new_request():
            """طلب إجازة جديد"""
            if 'user_id' not in session:
                return redirect(url_for('login'))
                
            if request.method == 'POST':
                # معالجة الطلب الجديد
                request_data = {
                    'type': request.form['type'],
                    'days': request.form['days'],
                    'start_date': request.form['start_date'],
                    'reason': request.form['reason']
                }
                # هنا سيتم حفظ الطلب في قاعدة البيانات
                return jsonify({'success': True, 'message': 'تم تقديم الطلب بنجاح'})
                
            return render_template_string(self.get_new_request_template())
            
    def get_login_template(self):
        """قالب صفحة تسجيل الدخول"""
        return '''
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>🔐 تسجيل الدخول - نظام إدارة الإجازات</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    direction: rtl;
                }
                
                .login-container {
                    background: white;
                    padding: 40px;
                    border-radius: 20px;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                    width: 100%;
                    max-width: 400px;
                    text-align: center;
                }
                
                .logo {
                    font-size: 48px;
                    margin-bottom: 20px;
                }
                
                .title {
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 10px;
                }
                
                .subtitle {
                    color: #7f8c8d;
                    margin-bottom: 30px;
                }
                
                .form-group {
                    margin-bottom: 20px;
                    text-align: right;
                }
                
                .form-group label {
                    display: block;
                    margin-bottom: 5px;
                    font-weight: bold;
                    color: #2c3e50;
                }
                
                .form-group input {
                    width: 100%;
                    padding: 12px;
                    border: 2px solid #e0e0e0;
                    border-radius: 10px;
                    font-size: 16px;
                    transition: border-color 0.3s;
                }
                
                .form-group input:focus {
                    outline: none;
                    border-color: #667eea;
                }
                
                .login-btn {
                    width: 100%;
                    padding: 12px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    font-size: 16px;
                    font-weight: bold;
                    cursor: pointer;
                    transition: transform 0.2s;
                }
                
                .login-btn:hover {
                    transform: translateY(-2px);
                }
                
                .error {
                    background: #fee;
                    color: #e74c3c;
                    padding: 10px;
                    border-radius: 8px;
                    margin-bottom: 20px;
                    border: 1px solid #fcc;
                }
                
                .demo-info {
                    margin-top: 20px;
                    padding: 15px;
                    background: #f8f9fa;
                    border-radius: 8px;
                    font-size: 14px;
                    color: #6c757d;
                }
            </style>
        </head>
        <body>
            <div class="login-container">
                <div class="logo">🏢</div>
                <h1 class="title">نظام إدارة الإجازات</h1>
                <p class="subtitle">الواجهة الويب الحديثة</p>
                
                {% if error %}
                <div class="error">❌ {{ error }}</div>
                {% endif %}
                
                <form method="POST">
                    <div class="form-group">
                        <label for="username">اسم المستخدم:</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">كلمة المرور:</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    
                    <button type="submit" class="login-btn">🔐 تسجيل الدخول</button>
                </form>
                
                <div class="demo-info">
                    <strong>بيانات التجربة:</strong><br>
                    المستخدم: admin<br>
                    كلمة المرور: admin123
                </div>
            </div>
        </body>
        </html>
        '''
        
    def get_dashboard_template(self):
        """قالب لوحة التحكم"""
        return '''
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>📊 لوحة التحكم - نظام إدارة الإجازات</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: #f8f9fa;
                    direction: rtl;
                }
                
                .header {
                    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
                    color: white;
                    padding: 20px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                
                .header-content {
                    max-width: 1200px;
                    margin: 0 auto;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .header h1 {
                    font-size: 24px;
                    font-weight: bold;
                }
                
                .user-info {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                }
                
                .logout-btn {
                    background: rgba(255,255,255,0.2);
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    text-decoration: none;
                }
                
                .logout-btn:hover {
                    background: rgba(255,255,255,0.3);
                }
                
                .main-content {
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 30px 20px;
                }
                
                .stats-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 20px;
                    margin-bottom: 30px;
                }
                
                .stat-card {
                    background: white;
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                    text-align: center;
                }
                
                .stat-card.primary { border-top: 4px solid #3498db; }
                .stat-card.success { border-top: 4px solid #27ae60; }
                .stat-card.warning { border-top: 4px solid #f39c12; }
                .stat-card.danger { border-top: 4px solid #e74c3c; }
                
                .stat-icon {
                    font-size: 32px;
                    margin-bottom: 10px;
                }
                
                .stat-value {
                    font-size: 28px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 5px;
                }
                
                .stat-label {
                    color: #7f8c8d;
                    font-size: 14px;
                }
                
                .navigation {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 20px;
                    margin-top: 30px;
                }
                
                .nav-card {
                    background: white;
                    padding: 25px;
                    border-radius: 12px;
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                    text-decoration: none;
                    color: inherit;
                    transition: transform 0.2s;
                }
                
                .nav-card:hover {
                    transform: translateY(-5px);
                }
                
                .nav-card-icon {
                    font-size: 48px;
                    margin-bottom: 15px;
                    text-align: center;
                }
                
                .nav-card-title {
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 10px;
                }
                
                .nav-card-desc {
                    color: #7f8c8d;
                    font-size: 14px;
                    line-height: 1.5;
                }
            </style>
        </head>
        <body>
            <header class="header">
                <div class="header-content">
                    <h1>🏢 نظام إدارة الإجازات - لوحة التحكم</h1>
                    <div class="user-info">
                        <span>👤 {{ session.full_name }}</span>
                        <a href="{{ url_for('logout') }}" class="logout-btn">🚪 خروج</a>
                    </div>
                </div>
            </header>
            
            <main class="main-content">
                <div class="stats-grid" id="stats-grid">
                    <!-- سيتم تحميل الإحصائيات هنا -->
                </div>
                
                <div class="navigation">
                    <a href="{{ url_for('requests') }}" class="nav-card">
                        <div class="nav-card-icon">📝</div>
                        <div class="nav-card-title">إدارة الطلبات</div>
                        <div class="nav-card-desc">عرض وإدارة طلبات الإجازات المقدمة من الموظفين</div>
                    </a>
                    
                    <a href="{{ url_for('new_request') }}" class="nav-card">
                        <div class="nav-card-icon">➕</div>
                        <div class="nav-card-title">طلب إجازة جديد</div>
                        <div class="nav-card-desc">تقديم طلب إجازة جديد مع إمكانية اختيار النوع والمدة</div>
                    </a>
                    
                    <a href="#" class="nav-card">
                        <div class="nav-card-icon">📊</div>
                        <div class="nav-card-title">التقارير والإحصائيات</div>
                        <div class="nav-card-desc">عرض تقارير مفصلة وإحصائيات شاملة للنظام</div>
                    </a>
                    
                    <a href="#" class="nav-card">
                        <div class="nav-card-icon">👥</div>
                        <div class="nav-card-title">إدارة الموظفين</div>
                        <div class="nav-card-desc">إدارة بيانات الموظفين وأرصدة الإجازات</div>
                    </a>
                    
                    <a href="#" class="nav-card">
                        <div class="nav-card-icon">⚙️</div>
                        <div class="nav-card-title">إعدادات النظام</div>
                        <div class="nav-card-desc">تكوين إعدادات النظام والسياسات</div>
                    </a>
                    
                    <a href="#" class="nav-card">
                        <div class="nav-card-icon">🔔</div>
                        <div class="nav-card-title">الإشعارات</div>
                        <div class="nav-card-desc">إدارة الإشعارات والتنبيهات</div>
                    </a>
                </div>
            </main>
            
            <script>
                // تحميل الإحصائيات
                fetch('/api/stats')
                    .then(response => response.json())
                    .then(data => {
                        const statsGrid = document.getElementById('stats-grid');
                        statsGrid.innerHTML = `
                            <div class="stat-card primary">
                                <div class="stat-icon">📝</div>
                                <div class="stat-value">${data.total_requests}</div>
                                <div class="stat-label">إجمالي الطلبات</div>
                            </div>
                            
                            <div class="stat-card warning">
                                <div class="stat-icon">⏳</div>
                                <div class="stat-value">${data.pending_requests}</div>
                                <div class="stat-label">طلبات معلقة</div>
                            </div>
                            
                            <div class="stat-card success">
                                <div class="stat-icon">✅</div>
                                <div class="stat-value">${data.approved_requests}</div>
                                <div class="stat-label">طلبات مقبولة</div>
                            </div>
                            
                            <div class="stat-card danger">
                                <div class="stat-icon">❌</div>
                                <div class="stat-value">${data.rejected_requests}</div>
                                <div class="stat-label">طلبات مرفوضة</div>
                            </div>
                            
                            <div class="stat-card primary">
                                <div class="stat-icon">👥</div>
                                <div class="stat-value">${data.total_employees}</div>
                                <div class="stat-label">إجمالي الموظفين</div>
                            </div>
                            
                            <div class="stat-card success">
                                <div class="stat-icon">🟢</div>
                                <div class="stat-value">${data.active_employees}</div>
                                <div class="stat-label">موظفين نشطين</div>
                            </div>
                        `;
                    })
                    .catch(error => console.error('خطأ في تحميل الإحصائيات:', error));
            </script>
        </body>
        </html>
        '''
        
    def get_requests_template(self):
        """قالب صفحة الطلبات"""
        return '''
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>📝 إدارة الطلبات - نظام إدارة الإجازات</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: #f8f9fa;
                    direction: rtl;
                }
                
                .header {
                    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
                    color: white;
                    padding: 20px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                
                .header-content {
                    max-width: 1200px;
                    margin: 0 auto;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .back-btn {
                    background: rgba(255,255,255,0.2);
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    text-decoration: none;
                }
                
                .main-content {
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 30px 20px;
                }
                
                .filters {
                    background: white;
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                    margin-bottom: 20px;
                    display: flex;
                    gap: 15px;
                    align-items: center;
                    flex-wrap: wrap;
                }
                
                .filter-group {
                    display: flex;
                    flex-direction: column;
                    gap: 5px;
                }
                
                .filter-group label {
                    font-weight: bold;
                    color: #2c3e50;
                    font-size: 14px;
                }
                
                .filter-group select, .filter-group input {
                    padding: 8px;
                    border: 2px solid #e0e0e0;
                    border-radius: 6px;
                    min-width: 150px;
                }
                
                .requests-table {
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                    overflow: hidden;
                }
                
                .table-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 20px;
                    font-size: 18px;
                    font-weight: bold;
                }
                
                .table-content {
                    overflow-x: auto;
                }
                
                table {
                    width: 100%;
                    border-collapse: collapse;
                }
                
                th, td {
                    padding: 12px;
                    text-align: center;
                    border-bottom: 1px solid #e0e0e0;
                }
                
                th {
                    background: #f8f9fa;
                    font-weight: bold;
                    color: #2c3e50;
                }
                
                tr:hover {
                    background: #f8f9fa;
                }
                
                .status-badge {
                    padding: 4px 12px;
                    border-radius: 20px;
                    font-size: 12px;
                    font-weight: bold;
                }
                
                .status-pending {
                    background: #fff3cd;
                    color: #856404;
                }
                
                .status-approved {
                    background: #d4edda;
                    color: #155724;
                }
                
                .status-rejected {
                    background: #f8d7da;
                    color: #721c24;
                }
                
                .action-btn {
                    padding: 6px 12px;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                    margin: 0 2px;
                }
                
                .approve-btn {
                    background: #28a745;
                    color: white;
                }
                
                .reject-btn {
                    background: #dc3545;
                    color: white;
                }
                
                .edit-btn {
                    background: #007bff;
                    color: white;
                }
                
                .loading {
                    text-align: center;
                    padding: 40px;
                    color: #6c757d;
                }
            </style>
        </head>
        <body>
            <header class="header">
                <div class="header-content">
                    <h1>📝 إدارة طلبات الإجازات</h1>
                    <a href="{{ url_for('index') }}" class="back-btn">🏠 العودة للرئيسية</a>
                </div>
            </header>
            
            <main class="main-content">
                <div class="filters">
                    <div class="filter-group">
                        <label>الحالة:</label>
                        <select id="status-filter">
                            <option value="">جميع الحالات</option>
                            <option value="معلق">معلق</option>
                            <option value="مقبول">مقبول</option>
                            <option value="مرفوض">مرفوض</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>النوع:</label>
                        <select id="type-filter">
                            <option value="">جميع الأنواع</option>
                            <option value="يومية">يومية</option>
                            <option value="ساعية">ساعية</option>
                            <option value="إضافية">إضافية</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>القسم:</label>
                        <select id="department-filter">
                            <option value="">جميع الأقسام</option>
                            <option value="التقنية">التقنية</option>
                            <option value="المالية">المالية</option>
                            <option value="التسويق">التسويق</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>البحث:</label>
                        <input type="text" id="search-input" placeholder="ابحث باسم الموظف...">
                    </div>
                </div>
                
                <div class="requests-table">
                    <div class="table-header">
                        📋 قائمة طلبات الإجازات
                    </div>
                    <div class="table-content">
                        <div id="loading" class="loading">🔄 جاري تحميل الطلبات...</div>
                        <table id="requests-table" style="display: none;">
                            <thead>
                                <tr>
                                    <th>المعرف</th>
                                    <th>اسم الموظف</th>
                                    <th>القسم</th>
                                    <th>نوع الإجازة</th>
                                    <th>عدد الأيام</th>
                                    <th>تاريخ البداية</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="requests-tbody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
            
            <script>
                let allRequests = [];
                
                // تحميل الطلبات
                fetch('/api/requests')
                    .then(response => response.json())
                    .then(data => {
                        allRequests = data;
                        displayRequests(data);
                        document.getElementById('loading').style.display = 'none';
                        document.getElementById('requests-table').style.display = 'table';
                    })
                    .catch(error => {
                        console.error('خطأ في تحميل الطلبات:', error);
                        document.getElementById('loading').innerHTML = '❌ خطأ في تحميل البيانات';
                    });
                
                function displayRequests(requests) {
                    const tbody = document.getElementById('requests-tbody');
                    tbody.innerHTML = '';
                    
                    requests.forEach(request => {
                        const statusClass = request.status === 'معلق' ? 'status-pending' : 
                                          request.status === 'مقبول' ? 'status-approved' : 'status-rejected';
                        
                        const row = `
                            <tr>
                                <td>${request.id}</td>
                                <td>${request.employee_name}</td>
                                <td>${request.department}</td>
                                <td>${request.type}</td>
                                <td>${request.days}</td>
                                <td>${request.start_date}</td>
                                <td><span class="status-badge ${statusClass}">${request.status}</span></td>
                                <td>
                                    ${request.status === 'معلق' ? 
                                        `<button class="action-btn approve-btn" onclick="approveRequest(${request.id})">✅ قبول</button>
                                         <button class="action-btn reject-btn" onclick="rejectRequest(${request.id})">❌ رفض</button>` : ''}
                                    <button class="action-btn edit-btn" onclick="editRequest(${request.id})">✏️ تعديل</button>
                                </td>
                            </tr>
                        `;
                        tbody.innerHTML += row;
                    });
                }
                
                // تصفية الطلبات
                function filterRequests() {
                    const statusFilter = document.getElementById('status-filter').value;
                    const typeFilter = document.getElementById('type-filter').value;
                    const departmentFilter = document.getElementById('department-filter').value;
                    const searchText = document.getElementById('search-input').value.toLowerCase();
                    
                    const filtered = allRequests.filter(request => {
                        return (!statusFilter || request.status === statusFilter) &&
                               (!typeFilter || request.type === typeFilter) &&
                               (!departmentFilter || request.department === departmentFilter) &&
                               (!searchText || request.employee_name.toLowerCase().includes(searchText));
                    });
                    
                    displayRequests(filtered);
                }
                
                // ربط المرشحات
                document.getElementById('status-filter').addEventListener('change', filterRequests);
                document.getElementById('type-filter').addEventListener('change', filterRequests);
                document.getElementById('department-filter').addEventListener('change', filterRequests);
                document.getElementById('search-input').addEventListener('input', filterRequests);
                
                // وظائف الإجراءات
                function approveRequest(id) {
                    if (confirm('هل تريد الموافقة على هذا الطلب؟')) {
                        alert(`تم قبول الطلب #${id}`);
                        // هنا سيتم إرسال طلب API للموافقة
                    }
                }
                
                function rejectRequest(id) {
                    if (confirm('هل تريد رفض هذا الطلب؟')) {
                        alert(`تم رفض الطلب #${id}`);
                        // هنا سيتم إرسال طلب API للرفض
                    }
                }
                
                function editRequest(id) {
                    alert(`تعديل الطلب #${id}`);
                    // هنا سيتم فتح نافذة التعديل
                }
            </script>
        </body>
        </html>
        '''
        
    def get_new_request_template(self):
        """قالب طلب إجازة جديد"""
        return '''
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>➕ طلب إجازة جديد - نظام إدارة الإجازات</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: #f8f9fa;
                    direction: rtl;
                }
                
                .header {
                    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
                    color: white;
                    padding: 20px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                
                .header-content {
                    max-width: 800px;
                    margin: 0 auto;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .back-btn {
                    background: rgba(255,255,255,0.2);
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    text-decoration: none;
                }
                
                .main-content {
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 30px 20px;
                }
                
                .form-container {
                    background: white;
                    padding: 30px;
                    border-radius: 12px;
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                }
                
                .form-header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                
                .form-header h2 {
                    color: #2c3e50;
                    font-size: 24px;
                    margin-bottom: 10px;
                }
                
                .form-header p {
                    color: #7f8c8d;
                }
                
                .form-group {
                    margin-bottom: 20px;
                }
                
                .form-group label {
                    display: block;
                    margin-bottom: 8px;
                    font-weight: bold;
                    color: #2c3e50;
                }
                
                .form-group input,
                .form-group select,
                .form-group textarea {
                    width: 100%;
                    padding: 12px;
                    border: 2px solid #e0e0e0;
                    border-radius: 8px;
                    font-size: 14px;
                    transition: border-color 0.3s;
                }
                
                .form-group input:focus,
                .form-group select:focus,
                .form-group textarea:focus {
                    outline: none;
                    border-color: #3498db;
                }
                
                .form-row {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                }
                
                .vacation-types {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 15px;
                    margin-top: 10px;
                }
                
                .vacation-type {
                    border: 2px solid #e0e0e0;
                    border-radius: 8px;
                    padding: 15px;
                    cursor: pointer;
                    transition: all 0.3s;
                    text-align: center;
                }
                
                .vacation-type:hover {
                    border-color: #3498db;
                    background: #f8f9fa;
                }
                
                .vacation-type.selected {
                    border-color: #3498db;
                    background: #e3f2fd;
                }
                
                .vacation-type input[type="radio"] {
                    display: none;
                }
                
                .vacation-type-icon {
                    font-size: 24px;
                    margin-bottom: 5px;
                }
                
                .vacation-type-title {
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 5px;
                }
                
                .vacation-type-desc {
                    font-size: 12px;
                    color: #7f8c8d;
                }
                
                .submit-btn {
                    width: 100%;
                    padding: 15px;
                    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: bold;
                    cursor: pointer;
                    transition: transform 0.2s;
                }
                
                .submit-btn:hover {
                    transform: translateY(-2px);
                }
                
                .form-note {
                    background: #e8f4fd;
                    border: 1px solid #bee5eb;
                    border-radius: 8px;
                    padding: 15px;
                    margin-bottom: 20px;
                    color: #0c5460;
                    font-size: 14px;
                }
                
                .balance-info {
                    background: #d4edda;
                    border: 1px solid #c3e6cb;
                    border-radius: 8px;
                    padding: 15px;
                    margin-bottom: 20px;
                    text-align: center;
                }
                
                .balance-info .balance-number {
                    font-size: 24px;
                    font-weight: bold;
                    color: #155724;
                }
            </style>
        </head>
        <body>
            <header class="header">
                <div class="header-content">
                    <h1>➕ تقديم طلب إجازة جديد</h1>
                    <a href="{{ url_for('index') }}" class="back-btn">🏠 العودة للرئيسية</a>
                </div>
            </header>
            
            <main class="main-content">
                <div class="form-container">
                    <div class="form-header">
                        <h2>📝 نموذج طلب إجازة</h2>
                        <p>املأ البيانات التالية لتقديم طلب إجازة جديد</p>
                    </div>
                    
                    <div class="balance-info">
                        <div>💰 رصيدك الحالي</div>
                        <div class="balance-number">25.5 يوم</div>
                    </div>
                    
                    <div class="form-note">
                        ℹ️ <strong>ملاحظة:</strong> يرجى التأكد من صحة البيانات قبل التقديم. سيتم مراجعة الطلب من قبل المشرف المباشر.
                    </div>
                    
                    <form id="vacation-form">
                        <div class="form-group">
                            <label>نوع الإجازة:</label>
                            <div class="vacation-types">
                                <label class="vacation-type" for="daily">
                                    <input type="radio" id="daily" name="type" value="يومية">
                                    <div class="vacation-type-icon">📅</div>
                                    <div class="vacation-type-title">إجازة يومية</div>
                                    <div class="vacation-type-desc">إجازة كاملة لأيام متتالية</div>
                                </label>
                                
                                <label class="vacation-type" for="hourly">
                                    <input type="radio" id="hourly" name="type" value="ساعية">
                                    <div class="vacation-type-icon">⏰</div>
                                    <div class="vacation-type-title">إجازة ساعية</div>
                                    <div class="vacation-type-desc">إجازة لساعات محددة</div>
                                </label>
                                
                                <label class="vacation-type" for="additional">
                                    <input type="radio" id="additional" name="type" value="إضافية">
                                    <div class="vacation-type-icon">➕</div>
                                    <div class="vacation-type-title">إجازة إضافية</div>
                                    <div class="vacation-type-desc">إجازة خارج الرصيد</div>
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="days">عدد الأيام/الساعات:</label>
                                <input type="number" id="days" name="days" min="0.5" step="0.5" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="start-date">تاريخ البداية:</label>
                                <input type="date" id="start-date" name="start_date" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="reason">سبب الإجازة:</label>
                            <textarea id="reason" name="reason" rows="4" placeholder="اذكر سبب طلب الإجازة..." required></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="replacement">البديل أثناء الغياب:</label>
                            <input type="text" id="replacement" name="replacement" placeholder="اسم الزميل البديل (اختياري)">
                        </div>
                        
                        <button type="submit" class="submit-btn">📤 تقديم الطلب</button>
                    </form>
                </div>
            </main>
            
            <script>
                // تفاعل أنواع الإجازة
                document.querySelectorAll('.vacation-type').forEach(type => {
                    type.addEventListener('click', function() {
                        document.querySelectorAll('.vacation-type').forEach(t => t.classList.remove('selected'));
                        this.classList.add('selected');
                    });
                });
                
                // تقديم النموذج
                document.getElementById('vacation-form').addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const formData = new FormData(this);
                    
                    // التحقق من البيانات
                    if (!formData.get('type')) {
                        alert('❌ يرجى اختيار نوع الإجازة');
                        return;
                    }
                    
                    // إرسال الطلب
                    fetch('/new_request', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('✅ ' + data.message);
                            window.location.href = '/requests';
                        } else {
                            alert('❌ خطأ: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('خطأ:', error);
                        alert('❌ حدث خطأ في إرسال الطلب');
                    });
                });
                
                // تعيين تاريخ اليوم كحد أدنى
                document.getElementById('start-date').min = new Date().toISOString().split('T')[0];
            </script>
        </body>
        </html>
        '''
        
    def run(self, host='localhost', port=5000, debug=False):
        """تشغيل الخادم"""
        print(f"\n🌐 تشغيل واجهة الويب على: http://{host}:{port}")
        print("📋 بيانات تسجيل الدخول:")
        print("   المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("\n🔗 الروابط المتاحة:")
        print(f"   📊 لوحة التحكم: http://{host}:{port}/")
        print(f"   📝 إدارة الطلبات: http://{host}:{port}/requests")
        print(f"   ➕ طلب جديد: http://{host}:{port}/new_request")
        print("\nاضغط Ctrl+C لإيقاف الخادم")
        
        self.app.run(host=host, port=port, debug=debug)

def start_web_interface():
    """بدء واجهة الويب"""
    web_app = VacationWebApp()
    
    # تشغيل في خيط منفصل
    web_thread = threading.Thread(target=web_app.run, kwargs={'debug': False})
    web_thread.daemon = True
    web_thread.start()
    
    return web_app

def test_web_interface():
    """اختبار واجهة الويب"""
    try:
        from flask import Flask
        print("✅ Flask متوفر - يمكن تشغيل واجهة الويب")
        
        web_app = VacationWebApp()
        web_app.run(debug=True)
        
    except ImportError:
        print("❌ Flask غير متوفر")
        print("لتثبيت Flask:")
        print("pip install flask")
        
        # واجهة بديلة مبسطة
        print("\n🔄 تشغيل واجهة بديلة...")
        create_simple_html_demo()

def create_simple_html_demo():
    """إنشاء عرض HTML بسيط"""
    html_content = '''
    <!DOCTYPE html>
    <html dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>🌐 عرض واجهة الويب - نظام إدارة الإجازات</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                margin: 0;
                padding: 20px;
                direction: rtl;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
                border-radius: 20px;
                padding: 30px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
            }
            .demo-card {
                background: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
                margin: 15px 0;
                border-left: 4px solid #3498db;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🌐 واجهة الويب لنظام إدارة الإجازات</h1>
                <p>عرض توضيحي للواجهة الويب الحديثة</p>
            </div>
            
            <div class="demo-card">
                <h3>🔐 تسجيل الدخول</h3>
                <p>واجهة تسجيل دخول عصرية مع تدرجات لونية جميلة</p>
            </div>
            
            <div class="demo-card">
                <h3>📊 لوحة التحكم</h3>
                <p>لوحة تحكم تفاعلية مع إحصائيات مباشرة وبطاقات ملونة</p>
            </div>
            
            <div class="demo-card">
                <h3>📝 إدارة الطلبات</h3>
                <p>جدول تفاعلي مع فلاتر متقدمة وإجراءات سريعة</p>
            </div>
            
            <div class="demo-card">
                <h3>➕ طلب إجازة جديد</h3>
                <p>نموذج ذكي لتقديم طلبات الإجازة مع معاينة فورية</p>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <p><strong>🚀 لتشغيل الواجهة الكاملة:</strong></p>
                <code>pip install flask && python modern_web_interface.py</code>
            </div>
        </div>
    </body>
    </html>
    '''
    
    with open('web_demo.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
        
    print("✅ تم إنشاء ملف web_demo.html")
    print("🌐 افتح الملف في المتصفح لرؤية العرض")

if __name__ == '__main__':
    test_web_interface()
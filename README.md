# نظام إدارة الإجازات

نظام شامل لإدارة إجازات الموظفين باستخدام Python و PyQt5

## المميزات

### 🔐 تسجيل الدخول الآمن
- نظام مستخدمين متعدد
- حماية بكلمة مرور
- إدارة الأذونات

### 📥 استيراد الرصيد الابتدائي
- استيراد من ملفات Excel
- دعم التنسيقات المختلفة
- التحقق من صحة البيانات

### 📝 إدارة طلبات الإجازات
- طلبات الإجازة اليومية
- طلبات الإجازة الساعية
- حساب تلقائي للتواريخ
- أنواع إجازات متعددة

### ➕ إدراج الإجازات
- إضافة أيام للرصيد
- توثيق الأسباب
- تتبع التعديلات

### 📊 التقارير والاستعلامات
- تقارير شهرية وسنوية
- البحث السريع
- حساب الرصيد الصافي
- طباعة التقارير

### 🔄 إدارة البيانات
- تعديل وحذف الطلبات
- أرشفة نهاية السنة
- نسخ احتياطية

## المتطلبات التقنية

### البرمجيات المطلوبة
- Python 3.7 أو أحدث
- PyQt5
- pandas
- openpyxl
- sqlite3

### تثبيت المكتبات
```bash
pip install -r requirements.txt
```

## طريقة التشغيل

### 1. تحضير البيئة
```bash
# تحميل المشروع
git clone [project-url]
cd vacation

# تثبيت المكتبات
pip install -r requirements.txt
```

### 2. إنشاء ملف Excel النموذجي
```bash
python create_sample_excel.py
```

### 3. تشغيل البرنامج

#### الطريقة الأولى: تشغيل مباشر
```bash
python main.py
```

#### الطريقة الثانية: تشغيل مبسط
```bash
python run_app.py
```

#### الطريقة الثالثة: النقر المزدوج
انقر نقراً مزدوجاً على ملف `تشغيل_البرنامج.bat`

### 4. اختبار البرنامج
```bash
python test_app.py
```

## بيانات الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## هيكل المشروع

```
vacation/
├── main.py                       # الملف الرئيسي
├── run_app.py                   # ملف تشغيل مبسط
├── test_app.py                  # ملف اختبار البرنامج
├── database.py                  # إدارة قاعدة البيانات
├── main_window.py               # الواجهة الرئيسية
├── create_sample_excel.py       # إنشاء ملف Excel النموذجي
├── requirements.txt             # المكتبات المطلوبة
├── README.md                   # هذا الملف
├── تشغيل_البرنامج.bat          # ملف تشغيل للويندوز
├── نموذج_الرصيد_الابتدائي.xlsx  # ملف Excel النموذجي
└── vacation_system.db          # قاعدة البيانات (تُنشأ تلقائياً)
```

## قاعدة البيانات

### الجداول
- `users` - بيانات المستخدمين
- `initial_balance` - الرصيد الابتدائي
- `daily_requests` - طلبات الإجازة اليومية
- `hourly_requests` - طلبات الإجازة الساعية
- `added_vacations` - الإجازات المدرجة
- `archived_data` - أرشيف البيانات

## استخدام البرنامج

### 1. تسجيل الدخول
- افتح البرنامج
- أدخل اسم المستخدم وكلمة المرور
- اضغط "دخول"

### 2. استيراد الرصيد الابتدائي
- انقر على "📥 استيراد رصيد"
- اختر ملف Excel
- تأكد من وجود الأعمدة:
  - الاسم واللقب
  - الرتبة
  - عدد الأيام
  - التاريخ

### 3. إضافة طلب إجازة يومية
- انقر على "📝 طلب إجازة يومية"
- املأ البيانات المطلوبة
- اضغط "حفظ"

### 4. إضافة طلب إجازة ساعية
- انقر على "⏱️ طلب إجازة ساعية"
- أدخل عدد الساعات
- سيتم حساب المعادل بالأيام تلقائياً

### 5. إدراج إجازة
- انقر على "➕ إدراج إجازات"
- أدخل البيانات والسبب
- اضغط "حفظ"

### 6. عرض التقارير
- انقر على "📊 التقارير"
- اختر الموظف
- اضغط "توليد التقرير"

### 7. البحث عن موظف
- انقر على "🔍 استعلام"
- أدخل اسم الموظف
- اضغط "بحث"

## الميزات المتقدمة

### حساب الإجازات الساعية
```
عدد الأيام = (عدد الساعات × 3) ÷ 24
```

### حساب الرصيد الصافي
```
الرصيد الصافي = الرصيد الابتدائي + الإجازات المدرجة - الإجازات المستفادة
```

### أنواع الإجازات المدعومة
- إجازة سنوية
- إجازة مرضية
- إجازة طارئة
- إجازة أمومة
- أنواع أخرى

## استكشاف الأخطاء

### خطأ في استيراد المكتبات
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### خطأ في فتح قاعدة البيانات
- تأكد من وجود أذونات الكتابة
- أغلق البرنامج وأعد تشغيله

### خطأ في قراءة ملف Excel
- تأكد من تنسيق الملف
- تحقق من أسماء الأعمدة
- تأكد من وجود بيانات

## التطوير والتحسين

### إضافة ميزات جديدة
- تعديل ملف `main_window.py`
- إضافة جداول جديدة في `database.py`
- تحديث الواجهة حسب الحاجة

### إنشاء ملف تنفيذي
```bash
pip install pyinstaller
pyinstaller --onefile --windowed main.py
```

## الدعم

للمساعدة أو الإبلاغ عن مشاكل:
- افتح issue في المشروع
- أرسل تفاصيل المشكلة
- أرفق لقطات شاشة إن أمكن

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتجاري.

## التحديثات

### الإصدار 1.0
- إطلاق النسخة الأولى
- جميع الميزات الأساسية
- واجهة باللغة العربية

### التحديثات المستقبلية
- إضافة ميزة الطباعة
- تحسين التقارير
- إضافة المزيد من أنواع الإجازات
- تحسين الأمان
- إضافة نظام النسخ الاحتياطي التلقائي
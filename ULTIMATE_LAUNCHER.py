#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 المشغل النهائي المتقدم - Ultimate Launcher
أقوى وأشمل أداة لتشغيل جميع أنظمة إدارة الإجازات
"""

import sys
import os
import subprocess
import threading
import time
from datetime import datetime

class UltimateLauncher:
    """المشغل النهائي المتقدم"""
    
    def __init__(self):
        self.available_systems = {
            "1": {
                "name": "🚀 المشغل الموحد الذكي",
                "file": "تشغيل_النظام_المتكامل.py",
                "description": "واجهة تحكم موحدة لجميع الأنظمة",
                "category": "أساسي",
                "priority": 1
            },
            "2": {
                "name": "✨ الواجهة الحديثة",
                "file": "تشغيل_الواجهة_الحديثة.py",
                "description": "واجهة عصرية مع تأثيرات بصرية رائعة",
                "category": "أساسي",
                "priority": 2
            },
            "3": {
                "name": "🔹 الواجهة التقليدية",
                "file": "run_app.py",
                "description": "واجهة مستقرة وموثوقة",
                "category": "أساسي",
                "priority": 3
            },
            "4": {
                "name": "🎛️ نظام الاختيار الذكي",
                "file": "اختيار_الواجهة.py",
                "description": "مقارنة ذكية بين الواجهات",
                "category": "أساسي",
                "priority": 4
            },
            "5": {
                "name": "🏠 الواجهة الرئيسية المتطورة",
                "file": "modern_main_window.py",
                "description": "واجهة رئيسية مع جميع الميزات",
                "category": "متقدم",
                "priority": 5
            },
            "6": {
                "name": "📊 نظام الإحصائيات المتقدم",
                "file": "modern_simple_analytics.py",
                "description": "تحليلات شاملة ورسوم بيانية",
                "category": "متقدم",
                "priority": 6
            },
            "7": {
                "name": "🗄️ مدير قاعدة البيانات",
                "file": "modern_database_manager.py",
                "description": "أدوات شاملة لإدارة البيانات",
                "category": "متقدم",
                "priority": 7
            },
            "8": {
                "name": "🧠 نظام الذكاء الاصطناعي",
                "file": "modern_ai_system.py",
                "description": "تحليل ذكي وتنبؤات متقدمة",
                "category": "متقدم",
                "priority": 8
            },
            "9": {
                "name": "👥 نظام إدارة المستخدمين",
                "file": "modern_user_management.py",
                "description": "إدارة شاملة للمستخدمين والأذونات",
                "category": "إدارة",
                "priority": 9
            },
            "10": {
                "name": "🔔 نظام الإشعارات والنسخ الاحتياطي",
                "file": "modern_notifications_backup.py",
                "description": "إشعارات ذكية ونسخ احتياطي",
                "category": "إدارة",
                "priority": 10
            },
            "11": {
                "name": "⚙️ الإعدادات المتقدمة",
                "file": "modern_search_settings.py",
                "description": "إعدادات شاملة وقابلة للتخصيص",
                "category": "إدارة",
                "priority": 11
            },
            "12": {
                "name": "🌐 واجهة الويب",
                "file": "modern_web_interface.py",
                "description": "تطبيق ويب حديث ومتجاوب",
                "category": "ويب",
                "priority": 12
            },
            "13": {
                "name": "🧪 الاختبارات التفاعلية",
                "file": "اختبار_تفاعلي.py",
                "description": "اختبارات شاملة للنظام",
                "category": "اختبار",
                "priority": 13
            },
            "14": {
                "name": "⚡ الاختبار السريع",
                "file": "اختبار_سريع.py",
                "description": "اختبار سريع للوظائف الأساسية",
                "category": "اختبار",
                "priority": 14
            },
            "15": {
                "name": "📋 الاختبار العملي الشامل",
                "file": "اختبار_عملي_شامل.py",
                "description": "اختبار عملي شامل للنظام",
                "category": "اختبار",
                "priority": 15
            },
            "16": {
                "name": "📋 عرض جميع الأنظمة",
                "file": "عرض_الأنظمة_المتاحة.py",
                "description": "دليل شامل للأنظمة المتاحة",
                "category": "مساعدة",
                "priority": 16
            }
        }
    
    def show_header(self):
        """عرض الرأس"""
        print("=" * 80)
        print("🚀 المشغل النهائي المتقدم - Ultimate Launcher")
        print("🏢 نظام إدارة الإجازات المتكامل - الإصدار النهائي")
        print("=" * 80)
        print()
        
        # معلومات النظام
        print("📊 معلومات النظام:")
        print(f"   📅 تاريخ التشغيل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   📁 مسار المشروع: {os.getcwd()}")
        print(f"   🐍 إصدار Python: {sys.version.split()[0]}")
        print()
    
    def show_menu_by_category(self):
        """عرض القائمة مرتبة حسب الفئة"""
        categories = {
            "أساسي": [],
            "متقدم": [],
            "إدارة": [],
            "ويب": [],
            "اختبار": [],
            "مساعدة": []
        }
        
        # تصنيف الأنظمة
        for key, system in self.available_systems.items():
            categories[system["category"]].append((key, system))
        
        # عرض كل فئة
        for category, systems in categories.items():
            if systems:
                print(f"📂 {category}:")
                print("-" * 40)
                for key, system in systems:
                    status = self.check_system_status(system["file"])
                    print(f"  {key}. {system['name']}")
                    print(f"     📝 {system['description']}")
                    print(f"     {status}")
                    print()
    
    def check_system_status(self, filename):
        """فحص حالة النظام"""
        if os.path.exists(filename):
            return "✅ جاهز للتشغيل"
        else:
            return "❌ غير متوفر"
    
    def show_quick_options(self):
        """عرض الخيارات السريعة"""
        print("⚡ خيارات سريعة:")
        print("-" * 20)
        print("  A. تشغيل تلقائي (أفضل نظام متاح)")
        print("  B. تشغيل جميع الاختبارات")
        print("  C. عرض معلومات النظام")
        print("  D. تشغيل عدة أنظمة")
        print("  0. خروج")
        print()
    
    def auto_run(self):
        """تشغيل تلقائي لأفضل نظام متاح"""
        print("🔍 البحث عن أفضل نظام متاح...")
        
        # ترتيب الأنظمة حسب الأولوية
        sorted_systems = sorted(self.available_systems.items(), 
                              key=lambda x: x[1]["priority"])
        
        for key, system in sorted_systems:
            if os.path.exists(system["file"]):
                print(f"✅ تم العثور على: {system['name']}")
                print(f"🚀 تشغيل {system['file']}...")
                return self.run_system(system["file"])
        
        print("❌ لم يتم العثور على أي نظام متاح!")
        return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 تشغيل جميع الاختبارات...")
        
        test_files = [
            "اختبار_سريع.py",
            "اختبار_عملي_شامل.py",
            "اختبار_تفاعلي.py"
        ]
        
        results = []
        for test_file in test_files:
            if os.path.exists(test_file):
                print(f"🔬 تشغيل {test_file}...")
                try:
                    result = subprocess.run([sys.executable, test_file], 
                                          capture_output=True, text=True, timeout=30)
                    if result.returncode == 0:
                        results.append(f"✅ {test_file}: نجح")
                    else:
                        results.append(f"❌ {test_file}: فشل")
                except subprocess.TimeoutExpired:
                    results.append(f"⏱️ {test_file}: انتهت المهلة")
                except Exception as e:
                    results.append(f"❌ {test_file}: خطأ - {e}")
            else:
                results.append(f"❌ {test_file}: غير موجود")
        
        print("\n📊 نتائج الاختبارات:")
        for result in results:
            print(f"   {result}")
        
        return True
    
    def show_system_info(self):
        """عرض معلومات النظام"""
        print("📊 معلومات النظام التفصيلية:")
        print("-" * 40)
        
        # إحصائيات الملفات
        total_files = len(self.available_systems)
        available_files = sum(1 for system in self.available_systems.values() 
                            if os.path.exists(system["file"]))
        
        print(f"📁 إجمالي الأنظمة: {total_files}")
        print(f"✅ الأنظمة المتاحة: {available_files}")
        print(f"❌ الأنظمة غير المتاحة: {total_files - available_files}")
        print()
        
        # تصنيف الأنظمة
        categories = {}
        for system in self.available_systems.values():
            cat = system["category"]
            if cat not in categories:
                categories[cat] = 0
            categories[cat] += 1
        
        print("📂 تصنيف الأنظمة:")
        for category, count in categories.items():
            print(f"   {category}: {count} نظام")
        
        print()
    
    def run_multiple_systems(self):
        """تشغيل عدة أنظمة"""
        print("🎯 تشغيل عدة أنظمة:")
        print("أدخل أرقام الأنظمة مفصولة بفاصلة (مثال: 1,2,3)")
        
        try:
            choices = input("الأنظمة المطلوبة: ").strip().split(',')
            choices = [c.strip() for c in choices if c.strip()]
            
            if not choices:
                print("❌ لم يتم إدخال أي خيار!")
                return False
            
            print(f"🚀 تشغيل {len(choices)} نظام...")
            
            for choice in choices:
                if choice in self.available_systems:
                    system = self.available_systems[choice]
                    print(f"▶️ تشغيل {system['name']}...")
                    
                    # تشغيل في خيط منفصل
                    thread = threading.Thread(
                        target=self.run_system, 
                        args=(system["file"],)
                    )
                    thread.start()
                    time.sleep(1)  # انتظار قصير بين التشغيلات
                else:
                    print(f"❌ الخيار {choice} غير صحيح!")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل الأنظمة: {e}")
            return False
    
    def run_system(self, filename):
        """تشغيل نظام محدد"""
        if not os.path.exists(filename):
            print(f"❌ الملف غير موجود: {filename}")
            return False
        
        try:
            print(f"🚀 تشغيل {filename}...")
            print("🔐 معلومات تسجيل الدخول:")
            print("   👤 اسم المستخدم: admin")
            print("   🔑 كلمة المرور: admin123")
            print()
            
            subprocess.run([sys.executable, filename])
            
            print(f"✅ تم إنهاء {filename} بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل {filename}: {e}")
            return False
    
    def main_menu(self):
        """القائمة الرئيسية"""
        while True:
            self.show_header()
            self.show_menu_by_category()
            self.show_quick_options()
            
            try:
                choice = input("🎯 اختر الخيار المطلوب: ").strip().upper()
                
                if choice == "0":
                    print("👋 شكراً لاستخدام المشغل النهائي!")
                    break
                elif choice == "A":
                    self.auto_run()
                elif choice == "B":
                    self.run_all_tests()
                elif choice == "C":
                    self.show_system_info()
                elif choice == "D":
                    self.run_multiple_systems()
                elif choice in self.available_systems:
                    system = self.available_systems[choice]
                    self.run_system(system["file"])
                else:
                    print("❌ خيار غير صحيح! جرب مرة أخرى.")
                
                input("\n⏸️ اضغط Enter للمتابعة...")
                
            except KeyboardInterrupt:
                print("\n\n⏹️ تم إيقاف المشغل بواسطة المستخدم")
                break
            except Exception as e:
                print(f"❌ خطأ غير متوقع: {e}")
                input("⏸️ اضغط Enter للمتابعة...")

def main():
    """الدالة الرئيسية"""
    launcher = UltimateLauncher()
    launcher.main_menu()

if __name__ == "__main__":
    main()
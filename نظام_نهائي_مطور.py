#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
النظام النهائي المطور - جميع المطالب مطبقة:
- مقاسات نوافذ محسّنة ومناسبة 
- قائمة منسدلة للأسماء مع إمكانية الإضافة
- إدراج يدوي لعدد الأيام والساعات
- توجيه عربي كامل
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime
import json
import os

class CompactVacationSystem:
    """النظام الرئيسي المدمج للإجازات"""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.app.setFont(QFont("Sakkal Majalla", 9))
        self.app.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد قائمة الموظفين
        self.employees_file = "employees_compact.json"
        self.requests_file = "vacation_requests_compact.json"
        self.load_employees()
        
        print("🚀 النظام النهائي المطور")
        print("=" * 35)
        print("✅ مقاسات محسّنة ومناسبة")
        print("✅ قائمة منسدلة للأسماء")
        print("✅ إدراج يدوي للأيام/الساعات")
        print("✅ توجيه عربي كامل")
        print("=" * 35)
    
    def load_employees(self):
        """تحميل قائمة الموظفين"""
        try:
            if os.path.exists(self.employees_file):
                with open(self.employees_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.employees = data.get("employees", [])
            else:
                # قائمة افتراضية
                self.employees = [
                    "أحمد محمد علي",
                    "فاطمة أحمد خالد", 
                    "محمد خالد عبدالله",
                    "نور سالم محمد",
                    "علي حسن أحمد"
                ]
                self.save_employees()
        except:
            self.employees = ["موظف افتراضي"]
    
    def save_employees(self):
        """حفظ قائمة الموظفين"""
        try:
            data = {"employees": self.employees}
            with open(self.employees_file, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الموظفين: {e}")
    
    def add_employee(self, name):
        """إضافة موظف جديد"""
        if name.strip() and name.strip() not in self.employees:
            self.employees.append(name.strip())
            self.save_employees()
            return True
        return False
    
    def create_login_window(self):
        """إنشاء نافذة تسجيل الدخول المدمجة"""
        login = QDialog()
        login.setWindowTitle("🔐 دخول")
        login.setFixedSize(200, 100)
        login.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = self.app.primaryScreen().geometry()
        x = (screen.width() - 200) // 2
        y = (screen.height() - 100) // 2
        login.move(x, y)
        
        layout = QVBoxLayout(login)
        layout.setContentsMargins(8, 5, 8, 5)
        layout.setSpacing(3)
        
        # العنوان
        title = QLabel("🔐 دخول")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 10px;
                font-weight: bold;
                color: white;
                background: #3498db;
                border-radius: 3px;
                padding: 4px;
            }
        """)
        layout.addWidget(title)
        
        # اسم المستخدم
        username = QLineEdit()
        username.setPlaceholderText("المستخدم")
        username.setText("admin")
        username.setFixedHeight(18)
        username.setLayoutDirection(Qt.RightToLeft)
        username.setStyleSheet("""
            QLineEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                border: 1px solid #bdc3c7;
                border-radius: 2px;
                padding: 2px;
                font-size: 8px;
                background: white;
            }
        """)
        layout.addWidget(username)
        
        # كلمة المرور
        password = QLineEdit()
        password.setPlaceholderText("كلمة المرور")
        password.setText("admin123")
        password.setEchoMode(QLineEdit.Password)
        password.setFixedHeight(18)
        password.setLayoutDirection(Qt.RightToLeft)
        password.setStyleSheet(username.styleSheet())
        layout.addWidget(password)
        
        # زر الدخول
        login_btn = QPushButton("✅ دخول")
        login_btn.setFixedHeight(20)
        login_btn.setStyleSheet("""
            QPushButton {
                background: #27ae60;
                border: none;
                border-radius: 2px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2ecc71;
            }
        """)
        
        def check_login():
            if username.text() == "admin" and password.text() == "admin123":
                login.accept()
            else:
                QMessageBox.warning(login, "خطأ", "بيانات خاطئة!")
        
        login_btn.clicked.connect(check_login)
        layout.addWidget(login_btn)
        
        # الأنماط العامة
        login.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }
        """)
        
        return login
    
    def create_main_window(self):
        """إنشاء النافذة الرئيسية المدمجة"""
        main_window = QMainWindow()
        main_window.setWindowTitle("🏢 نظام الإجازات المطور")
        main_window.setGeometry(100, 100, 300, 200)
        main_window.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = self.app.primaryScreen().geometry()
        x = (screen.width() - 300) // 2
        y = (screen.height() - 200) // 2
        main_window.move(x, y)
        
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(6)
        
        # العنوان
        title = QLabel("🏢 نظام إدارة الإجازات")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2c3e50, stop: 1 #34495e);
                border-radius: 4px;
                padding: 6px;
            }
        """)
        layout.addWidget(title)
        
        # وصف النظام
        desc = QLabel("👥 قائمة أسماء + إدراج يدوي")
        desc.setAlignment(Qt.AlignCenter)
        desc.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 8px;
                color: #7f8c8d;
                padding: 2px;
            }
        """)
        layout.addWidget(desc)
        
        # الأزرار الرئيسية
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(4)
        
        # زر الإجازة اليومية
        daily_btn = QPushButton("📅 إجازة يومية")
        daily_btn.setFixedHeight(25)
        daily_btn.setStyleSheet("""
            QPushButton {
                background: #e74c3c;
                border: none;
                border-radius: 4px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 9px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #c0392b;
            }
        """)
        daily_btn.clicked.connect(lambda: self.open_daily_vacation(main_window))
        buttons_layout.addWidget(daily_btn)
        
        # زر الإجازة الساعية
        hourly_btn = QPushButton("⏱️ إجازة ساعية")
        hourly_btn.setFixedHeight(25)
        hourly_btn.setStyleSheet("""
            QPushButton {
                background: #f39c12;
                border: none;
                border-radius: 4px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 9px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #e67e22;
            }
        """)
        hourly_btn.clicked.connect(lambda: self.open_hourly_vacation(main_window))
        buttons_layout.addWidget(hourly_btn)
        
        # زر عرض الطلبات
        view_btn = QPushButton("📋 عرض الطلبات")
        view_btn.setFixedHeight(25)
        view_btn.setStyleSheet("""
            QPushButton {
                background: #27ae60;
                border: none;
                border-radius: 4px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 9px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #229954;
            }
        """)
        view_btn.clicked.connect(lambda: self.view_requests(main_window))
        buttons_layout.addWidget(view_btn)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        # معلومات النظام
        info = QLabel("💡 مقاسات صغيرة + قوائم منسدلة")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 7px;
                color: #95a5a6;
                background: #ecf0f1;
                border-radius: 2px;
                padding: 3px;
            }
        """)
        layout.addWidget(info)
        
        # الأنماط العامة
        main_window.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }
        """)
        
        return main_window
    
    def create_vacation_dialog(self, vacation_type, parent):
        """إنشاء نافذة طلب الإجازة"""
        if vacation_type == "daily":
            title = "📅 إجازة يومية"
            width, height = 320, 260
            color = "#e74c3c"
        else:
            title = "⏱️ إجازة ساعية" 
            width, height = 320, 240
            color = "#f39c12"
        
        dialog = QDialog(parent)
        dialog.setWindowTitle(title)
        dialog.setFixedSize(width, height)
        dialog.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = self.app.primaryScreen().geometry()
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        dialog.move(x, y)
        
        layout = QVBoxLayout(dialog)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(4)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 10px;
                font-weight: bold;
                color: white;
                background: {color};
                border-radius: 3px;
                padding: 5px;
            }}
        """)
        layout.addWidget(title_label)
        
        # إنشاء النموذج
        form_layout = QVBoxLayout()
        form_layout.setSpacing(3)
        
        # اسم الموظف (قائمة منسدلة)
        employee_row = QWidget()
        employee_layout = QHBoxLayout(employee_row)
        employee_layout.setContentsMargins(1, 1, 1, 1)
        employee_layout.setSpacing(3)
        
        employee_label = QLabel("👤 الموظف:")
        employee_label.setFixedWidth(60)
        employee_label.setAlignment(Qt.AlignRight)
        employee_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 8px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        
        employee_combo = QComboBox()
        employee_combo.setEditable(True)
        employee_combo.setLayoutDirection(Qt.RightToLeft)
        employee_combo.setFixedHeight(18)
        employee_combo.addItems(self.employees)
        employee_combo.setStyleSheet("""
            QComboBox {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                background: white;
                border: 1px solid #bdc3c7;
                border-radius: 2px;
                padding: 2px;
                font-size: 7px;
                color: #2c3e50;
            }
            QComboBox:focus {
                border: 2px solid #3498db;
            }
        """)
        
        add_btn = QPushButton("➕")
        add_btn.setFixedSize(18, 18)
        add_btn.setStyleSheet("""
            QPushButton {
                background: #27ae60;
                border: none;
                border-radius: 2px;
                color: white;
                font-size: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2ecc71;
            }
        """)
        
        def add_new_employee():
            name, ok = QInputDialog.getText(dialog, "إضافة موظف", "الاسم:")
            if ok and name.strip():
                if self.add_employee(name.strip()):
                    employee_combo.addItem(name.strip())
                    employee_combo.setCurrentText(name.strip())
                    QMessageBox.information(dialog, "نجح", f"تم إضافة: {name}")
        
        add_btn.clicked.connect(add_new_employee)
        
        employee_layout.addWidget(employee_label)
        employee_layout.addWidget(employee_combo)
        employee_layout.addWidget(add_btn)
        form_layout.addWidget(employee_row)
        
        # باقي الحقول
        fields = {}
        
        if vacation_type == "daily":
            field_configs = [
                ("📅 من:", "start_date", "2024-12-01"),
                ("📅 إلى:", "end_date", "2024-12-03"),
                ("📊 الأيام:", "days", None),
                ("📋 النوع:", "type", "إجازة اعتيادية"),
                ("📝 السبب:", "reason", "السبب...")
            ]
        else:
            field_configs = [
                ("📅 التاريخ:", "date", "2024-12-01"),
                ("🕘 من:", "start_time", "09:00"),
                ("🕐 إلى:", "end_time", "11:00"),
                ("⏰ الساعات:", "hours", None),
                ("📋 النوع:", "type", "موعد طبي"),
                ("📝 السبب:", "reason", "السبب...")
            ]
        
        for label_text, field_name, placeholder in field_configs:
            row = QWidget()
            row_layout = QHBoxLayout(row)
            row_layout.setContentsMargins(1, 1, 1, 1)
            row_layout.setSpacing(3)
            
            label = QLabel(label_text)
            label.setFixedWidth(60)
            label.setAlignment(Qt.AlignRight)
            label.setStyleSheet(employee_label.styleSheet())
            
            if field_name in ["days", "hours"]:
                # SpinBox للأيام والساعات
                widget = QSpinBox()
                widget.setMinimum(1)
                widget.setMaximum(365 if field_name == "days" else 24)
                widget.setValue(1 if field_name == "days" else 2)
                widget.setSuffix(" يوم" if field_name == "days" else " ساعة")
                widget.setLayoutDirection(Qt.RightToLeft)
                widget.setFixedHeight(18)
                widget.setStyleSheet("""
                    QSpinBox {
                        font-family: "Sakkal Majalla", "Arial", sans-serif;
                        background: white;
                        border: 1px solid #bdc3c7;
                        border-radius: 2px;
                        padding: 2px;
                        font-size: 7px;
                        color: #2c3e50;
                    }
                    QSpinBox:focus {
                        border: 2px solid #3498db;
                    }
                """)
            else:
                # LineEdit للباقي
                widget = QLineEdit()
                widget.setPlaceholderText(placeholder)
                widget.setLayoutDirection(Qt.RightToLeft)
                widget.setFixedHeight(18)
                widget.setStyleSheet("""
                    QLineEdit {
                        font-family: "Sakkal Majalla", "Arial", sans-serif;
                        background: white;
                        border: 1px solid #bdc3c7;
                        border-radius: 2px;
                        padding: 2px;
                        font-size: 7px;
                        color: #2c3e50;
                    }
                    QLineEdit:focus {
                        border: 2px solid #3498db;
                    }
                """)
            
            fields[field_name] = widget
            
            row_layout.addWidget(label)
            row_layout.addWidget(widget)
            form_layout.addWidget(row)
        
        layout.addLayout(form_layout)
        layout.addStretch()
        
        # معلومات إرشادية
        info_label = QLabel("💡 قائمة منسدلة للأسماء + إدراج يدوي للعدد")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 6px;
                color: #7f8c8d;
                background: #ecf0f1;
                border-radius: 2px;
                padding: 2px;
            }
        """)
        layout.addWidget(info_label)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(4)
        
        save_btn = QPushButton("💾 حفظ")
        save_btn.setFixedHeight(20)
        save_btn.setFixedWidth(60)
        save_btn.setStyleSheet("""
            QPushButton {
                background: #27ae60;
                border: none;
                border-radius: 2px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 7px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2ecc71;
            }
        """)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setFixedHeight(20)
        cancel_btn.setFixedWidth(60)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #95a5a6;
                border: none;
                border-radius: 2px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 7px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #7f8c8d;
            }
        """)
        
        def save_request():
            employee_name = employee_combo.currentText().strip()
            if not employee_name:
                QMessageBox.warning(dialog, "تحذير", "اختر اسم الموظف!")
                return
            
            # إعداد البيانات
            vacation_data = {
                "type": vacation_type,
                "employee_name": employee_name,
                "request_datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            for field_name, widget in fields.items():
                if isinstance(widget, QSpinBox):
                    vacation_data[f"{field_name}_manual"] = widget.value()
                else:
                    vacation_data[field_name] = widget.text().strip()
            
            # حفظ البيانات
            try:
                if os.path.exists(self.requests_file):
                    with open(self.requests_file, "r", encoding="utf-8") as f:
                        requests = json.load(f)
                else:
                    requests = []
                
                requests.append(vacation_data)
                
                with open(self.requests_file, "w", encoding="utf-8") as f:
                    json.dump(requests, f, ensure_ascii=False, indent=2)
                
                count = vacation_data.get("days_manual", vacation_data.get("hours_manual", 0))
                unit = "يوم" if vacation_type == "daily" else "ساعة"
                
                QMessageBox.information(dialog, "✅ تم الحفظ", 
                    f"تم حفظ الطلب!\n\n"
                    f"الموظف: {employee_name}\n"
                    f"العدد: {count} {unit}")
                
                dialog.accept()
                
            except Exception as e:
                QMessageBox.critical(dialog, "خطأ", f"فشل في الحفظ:\n{e}")
        
        save_btn.clicked.connect(save_request)
        cancel_btn.clicked.connect(dialog.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # الأنماط العامة
        dialog.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }
        """)
        
        return dialog
    
    def open_daily_vacation(self, parent):
        """فتح نافذة الإجازة اليومية"""
        dialog = self.create_vacation_dialog("daily", parent)
        dialog.exec_()
    
    def open_hourly_vacation(self, parent):
        """فتح نافذة الإجازة الساعية"""
        dialog = self.create_vacation_dialog("hourly", parent)
        dialog.exec_()
    
    def view_requests(self, parent):
        """عرض الطلبات المحفوظة"""
        try:
            if not os.path.exists(self.requests_file):
                QMessageBox.information(parent, "معلومات", "لا توجد طلبات!")
                return
            
            with open(self.requests_file, "r", encoding="utf-8") as f:
                requests = json.load(f)
            
            if not requests:
                QMessageBox.information(parent, "معلومات", "لا توجد طلبات!")
                return
            
            # نافذة عرض الطلبات
            window = QDialog(parent)
            window.setWindowTitle("📋 الطلبات")
            window.setFixedSize(350, 220)
            window.setLayoutDirection(Qt.RightToLeft)
            
            # توسيط النافذة
            screen = self.app.primaryScreen().geometry()
            x = (screen.width() - 350) // 2
            y = (screen.height() - 220) // 2
            window.move(x, y)
            
            layout = QVBoxLayout(window)
            layout.setContentsMargins(8, 8, 8, 8)
            
            # العنوان
            title = QLabel(f"📋 عدد الطلبات: {len(requests)}")
            title.setAlignment(Qt.AlignCenter)
            title.setStyleSheet("""
                QLabel {
                    font-family: "Sakkal Majalla", "Arial", sans-serif;
                    font-size: 9px;
                    font-weight: bold;
                    color: white;
                    background: #3498db;
                    border-radius: 3px;
                    padding: 4px;
                }
            """)
            layout.addWidget(title)
            
            # جدول الطلبات
            table = QTextEdit()
            table.setReadOnly(True)
            table.setLayoutDirection(Qt.RightToLeft)
            table.setStyleSheet("""
                QTextEdit {
                    font-family: "Sakkal Majalla", "Arial", sans-serif;
                    font-size: 7px;
                    border: 1px solid #bdc3c7;
                    border-radius: 2px;
                    padding: 4px;
                    background: white;
                }
            """)
            
            # تنسيق النصوص
            content = ""
            for i, request in enumerate(requests, 1):
                content += f"طلب {i}:\n"
                content += f"👤 {request.get('employee_name', 'غير محدد')}\n"
                
                if request.get('type') == 'hourly':
                    content += f"⏱️ ساعية - {request.get('hours_manual', 0)} ساعة\n"
                    content += f"📅 {request.get('date', 'غير محدد')}\n"
                else:
                    content += f"📅 يومية - {request.get('days_manual', 0)} يوم\n"
                    content += f"📅 {request.get('start_date', 'غير محدد')} إلى {request.get('end_date', 'غير محدد')}\n"
                
                content += f"📋 {request.get('type', 'غير محدد')}\n"
                content += f"📝 {request.get('reason', 'غير محدد')}\n"
                content += "-" * 20 + "\n\n"
            
            table.setPlainText(content)
            layout.addWidget(table)
            
            # زر الإغلاق
            close_btn = QPushButton("❌ إغلاق")
            close_btn.setFixedHeight(18)
            close_btn.setStyleSheet("""
                QPushButton {
                    background: #95a5a6;
                    border: none;
                    border-radius: 2px;
                    color: white;
                    font-family: "Sakkal Majalla", "Arial", sans-serif;
                    font-size: 7px;
                    font-weight: bold;
                }
            """)
            close_btn.clicked.connect(window.close)
            layout.addWidget(close_btn)
            
            window.exec_()
            
        except Exception as e:
            QMessageBox.critical(parent, "خطأ", f"فشل في قراءة الطلبات:\n{e}")
    
    def run(self):
        """تشغيل النظام"""
        # تسجيل الدخول
        login = self.create_login_window()
        if login.exec_() == QDialog.Accepted:
            print("✅ تم تسجيل الدخول بنجاح!")
            
            # النافذة الرئيسية
            main_window = self.create_main_window()
            main_window.show()
            
            return self.app.exec_()
        else:
            print("❌ تم إلغاء تسجيل الدخول")
            return 0

def main():
    """الدالة الرئيسية"""
    system = CompactVacationSystem()
    return system.run()

if __name__ == "__main__":
    sys.exit(main())
// qbitmap.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QBitmap : public QPixmap
{
%TypeHeaderCode
#include <qbitmap.h>
%End

public:
    QBitmap();
%If (Qt_5_7_0 -)
    QBitmap(const QBitmap &other);
%End
    QBitmap(const QPixmap &);
    QBitmap(int w, int h);
    explicit QBitmap(const QSize &);
    QBitmap(const QString &fileName, const char *format = 0);
    QBitmap(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QBitmap>())
            sipCpp = new sipQBitmap(a0->value<QBitmap>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    virtual ~QBitmap();
    void clear();
    static QBitmap fromImage(const QImage &image, Qt::ImageConversionFlags flags = Qt::ImageConversionFlag::AutoColor);
    static QBitmap fromData(const QSize &size, const uchar *bits, QImage::Format format = QImage::Format_MonoLSB);
    QBitmap transformed(const QTransform &matrix) const;
    void swap(QBitmap &other /Constrained/);
};

@echo off
chcp 65001 > nul
title إعداد نظام إدارة الإجازات على جهاز جديد

echo.
echo 🎯 إعداد نظام إدارة الإجازات على جهاز جديد
echo ===============================================
echo.

echo 📋 الخطوات المطلوبة:
echo   1. التحقق من Python
echo   2. تثبيت المكتبات المطلوبة
echo   3. إنشاء قاعدة البيانات
echo   4. اختبار النظام
echo.

REM الخطوة 1: التحقق من Python
echo 🔍 الخطوة 1: التحقق من Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على هذا الجهاز
    echo.
    echo 💡 يرجى تثبيت Python أولاً:
    echo    1. اذهب إلى: https://python.org
    echo    2. حمل Python 3.7 أو أحدث
    echo    3. أثناء التثبيت، فعل "Add Python to PATH"
    echo    4. أعد تشغيل هذا الملف بعد التثبيت
    echo.
    pause
    exit /b 1
)

echo ✅ Python مثبت بنجاح
python --version
echo.

REM الخطوة 2: تثبيت المكتبات
echo 🔍 الخطوة 2: تثبيت المكتبات المطلوبة...
echo.

echo 📦 تثبيت pandas...
pip install pandas
if errorlevel 1 (
    echo ⚠️ تحذير: مشكلة في تثبيت pandas
) else (
    echo ✅ pandas مثبت بنجاح
)
echo.

echo 📦 تثبيت openpyxl...
pip install openpyxl
if errorlevel 1 (
    echo ⚠️ تحذير: مشكلة في تثبيت openpyxl
) else (
    echo ✅ openpyxl مثبت بنجاح
)
echo.

echo 📦 تثبيت PyQt5...
pip install PyQt5
if errorlevel 1 (
    echo ⚠️ تحذير: مشكلة في تثبيت PyQt5
) else (
    echo ✅ PyQt5 مثبت بنجاح
)
echo.

echo 📦 تثبيت reportlab...
pip install reportlab
if errorlevel 1 (
    echo ⚠️ تحذير: مشكلة في تثبيت reportlab
) else (
    echo ✅ reportlab مثبت بنجاح
)
echo.

REM الخطوة 3: فحص المكتبات
echo 🔍 الخطوة 3: فحص المكتبات المثبتة...
python -c "
import sys
print('🔍 فحص المكتبات:')
print('-' * 30)

try:
    import pandas
    print('✅ pandas - متاح')
except ImportError:
    print('❌ pandas - غير متاح')

try:
    import openpyxl
    print('✅ openpyxl - متاح')
except ImportError:
    print('❌ openpyxl - غير متاح')

try:
    import PyQt5
    print('✅ PyQt5 - متاح')
except ImportError:
    print('❌ PyQt5 - غير متاح')

try:
    import reportlab
    print('✅ reportlab - متاح')
except ImportError:
    print('❌ reportlab - غير متاح')

try:
    import sqlite3
    print('✅ sqlite3 - متاح (مدمج)')
except ImportError:
    print('❌ sqlite3 - غير متاح')
"

echo.

REM الخطوة 4: إنشاء قاعدة بيانات تجريبية
echo 🔍 الخطوة 4: إنشاء قاعدة بيانات تجريبية...
python -c "
import sqlite3
import os

print('📊 إنشاء قاعدة بيانات تجريبية...')

conn = sqlite3.connect('vacation_system.db')
cursor = conn.cursor()

# إنشاء جدول الموظفين
cursor.execute('''
    CREATE TABLE IF NOT EXISTS employees (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        initial_balance INTEGER DEFAULT 30,
        used_days INTEGER DEFAULT 0,
        department TEXT DEFAULT 'عام'
    )
''')

# إضافة بيانات تجريبية
employees_data = [
    ('أحمد محمد علي', 30, 5, 'الإدارة'),
    ('فاطمة عبد الله', 35, 8, 'الموارد البشرية'),
    ('محمد عبد الرحمن', 30, 12, 'المالية')
]

cursor.execute('DELETE FROM employees')
for name, initial, used, dept in employees_data:
    cursor.execute('''
        INSERT INTO employees (name, initial_balance, used_days, department)
        VALUES (?, ?, ?, ?)
    ''', (name, initial, used, dept))

conn.commit()
conn.close()

print('✅ تم إنشاء قاعدة البيانات بنجاح')
print(f'📁 الملف: {os.path.abspath(\"vacation_system.db\")}')
"

echo.

REM الخطوة 5: اختبار النظام
echo 🔍 الخطوة 5: اختبار النظام...
python -c "
print('🧪 اختبار سريع للنظام...')
print('-' * 40)

import sqlite3

conn = sqlite3.connect('vacation_system.db')
cursor = conn.cursor()

cursor.execute('SELECT COUNT(*) FROM employees')
count = cursor.fetchone()[0]

cursor.execute('SELECT name, initial_balance - used_days as remaining FROM employees')
employees = cursor.fetchall()

print(f'👥 عدد الموظفين: {count}')
print('📋 قائمة الموظفين:')
for name, remaining in employees:
    print(f'  - {name}: {remaining} يوم متبقي')

conn.close()
print()
print('✅ النظام يعمل بشكل صحيح!')
"

echo.
echo ===============================================
echo 🎉 تم إعداد النظام بنجاح على الجهاز الجديد!
echo.
echo 🚀 يمكنك الآن تشغيل البرنامج بأي من الطرق التالية:
echo   1. انقر مرتين على: START.bat
echo   2. انقر مرتين على: RUN.bat  
echo   3. من سطر الأوامر: python master_control_panel.py
echo   4. من سطر الأوامر: python main.py
echo.
echo 📋 معلومات مهمة:
echo   - قاعدة البيانات: vacation_system.db
echo   - المجلد الحالي: %CD%
echo   - جميع الملفات جاهزة للاستخدام
echo.
echo ===============================================

pause

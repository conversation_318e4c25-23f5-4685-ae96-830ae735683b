#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح المقاسات والإدراج اليدوي - نوافذ محسّنة بمقاسات مناسبة وإدراج يدوي
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime, timedelta

class OptimizedManualDialog(QDialog):
    """نافذة حوار محسّنة بمقاسات مناسبة وإدراج يدوي"""
    
    def __init__(self, title, width=450, height=380):
        super().__init__()
        self.setWindowTitle(title)
        self.setFixedSize(width, height)
        
        # تطبيق التوجيه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        self.move(x, y)
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد الواجهة الأساسية"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(15, 15, 15, 15)
        self.main_layout.setSpacing(10)
        
        # إضافة الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
    
    def create_form_row(self, label_text, widget):
        """إنشاء صف في النموذج بمقاسات محسّنة"""
        row_widget = QWidget()
        row_layout = QHBoxLayout(row_widget)
        row_layout.setContentsMargins(5, 5, 5, 5)
        row_layout.setSpacing(8)
        
        # التسمية
        label = QLabel(label_text)
        label.setFixedWidth(120)
        label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)
        
        # تطبيق الأنماط على العناصر
        if isinstance(widget, (QLineEdit, QComboBox, QDateEdit, QTimeEdit)):
            widget.setLayoutDirection(Qt.RightToLeft)
            widget.setFixedHeight(35)
            widget.setStyleSheet("""
                QLineEdit, QComboBox, QDateEdit, QTimeEdit {
                    font-family: "Sakkal Majalla", "Arial", sans-serif;
                    background: white;
                    border: 2px solid #bdc3c7;
                    border-radius: 6px;
                    padding: 8px;
                    font-size: 13px;
                    font-weight: normal;
                    color: #2c3e50;
                }
                QLineEdit:focus, QComboBox:focus, QDateEdit:focus, QTimeEdit:focus {
                    border: 2px solid #3498db;
                    background: #f8f9fa;
                }
            """)
        
        row_layout.addWidget(label)
        row_layout.addWidget(widget)
        
        self.main_layout.addWidget(row_widget)
    
    def add_button(self, text, style_type, callback):
        """إضافة زر بمقاسات محسّنة"""
        button = QPushButton(text)
        button.setFixedHeight(40)
        button.setFixedWidth(120)
        
        if style_type == "primary":
            color = "#27ae60"
            hover_color = "#2ecc71"
        elif style_type == "secondary":
            color = "#95a5a6"
            hover_color = "#bdc3c7"
        else:
            color = "#e74c3c"
            hover_color = "#c0392b"
        
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color}, stop: 1 rgba(0,0,0,0.1));
                border: none;
                border-radius: 8px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 13px;
                font-weight: bold;
                padding: 8px;
            }}
            QPushButton:hover {{
                background: {hover_color};
            }}
            QPushButton:pressed {{
                background: rgba(0,0,0,0.1);
            }}
        """)
        
        button.clicked.connect(callback)
        
        # إضافة الزر في layout أفقي
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(button)
        button_layout.addStretch()
        
        self.main_layout.addLayout(button_layout)

class ManualDailyVacationWindow(OptimizedManualDialog):
    """نافذة طلب الإجازة اليومية بإدراج يدوي ومقاسات محسّنة"""
    
    def __init__(self):
        super().__init__("📅 طلب إجازة يومية - إدراج يدوي", 480, 420)
        self.setup_content()
        
    def setup_content(self):
        """إعداد محتوى النافذة"""
        
        # العنوان
        title_label = QLabel("📅 طلب إجازة يومية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #e74c3c, stop: 1 #c0392b);
                border-radius: 8px;
                padding: 12px;
                margin: 5px;
            }
        """)
        self.main_layout.addWidget(title_label)
        
        # اسم الموظف (إدراج يدوي)
        self.employee_name = QLineEdit()
        self.employee_name.setPlaceholderText("أدخل اسم الموظف...")
        self.create_form_row("👤 اسم الموظف:", self.employee_name)
        
        # تاريخ البداية
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate())
        self.start_date.setCalendarPopup(True)
        self.create_form_row("📅 تاريخ البداية:", self.start_date)
        
        # تاريخ النهاية
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate().addDays(1))
        self.end_date.setCalendarPopup(True)
        self.create_form_row("📅 تاريخ النهاية:", self.end_date)
        
        # نوع الإجازة (إدراج يدوي)
        self.vacation_type = QLineEdit()
        self.vacation_type.setPlaceholderText("أدخل نوع الإجازة (مثال: إجازة اعتيادية)...")
        self.create_form_row("📋 نوع الإجازة:", self.vacation_type)
        
        # السبب
        self.reason = QLineEdit()
        self.reason.setPlaceholderText("أدخل سبب الإجازة...")
        self.create_form_row("📝 السبب:", self.reason)
        
        # ملاحظات
        self.notes = QLineEdit()
        self.notes.setPlaceholderText("ملاحظات إضافية (اختياري)...")
        self.create_form_row("📄 ملاحظات:", self.notes)
        
        # مساحة فارغة
        self.main_layout.addStretch()
        
        # الأزرار
        self.add_button("💾 حفظ الطلب", "primary", self.save_request)
        self.add_button("❌ إلغاء", "secondary", self.reject)
        
    def save_request(self):
        """حفظ طلب الإجازة"""
        employee_name = self.employee_name.text().strip()
        vacation_type = self.vacation_type.text().strip()
        
        if not employee_name:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الموظف!")
            self.employee_name.setFocus()
            return
            
        if not vacation_type:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال نوع الإجازة!")
            self.vacation_type.setFocus()
            return
            
        start_date = self.start_date.date()
        end_date = self.end_date.date()
        
        if start_date > end_date:
            QMessageBox.warning(self, "تحذير", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية!")
            return
            
        # حساب عدد الأيام
        days_count = start_date.daysTo(end_date) + 1
        
        # إعداد البيانات
        vacation_data = {
            "employee_name": employee_name,
            "start_date": start_date.toString("yyyy-MM-dd"),
            "end_date": end_date.toString("yyyy-MM-dd"),
            "vacation_type": vacation_type,
            "reason": self.reason.text().strip(),
            "notes": self.notes.text().strip(),
            "days_count": days_count,
            "request_date": QDate.currentDate().toString("yyyy-MM-dd"),
            "request_time": QTime.currentTime().toString("hh:mm:ss")
        }
        
        try:
            # حفظ في ملف JSON
            import json
            import os
            
            # قراءة البيانات الموجودة
            if os.path.exists("vacation_requests.json"):
                with open("vacation_requests.json", "r", encoding="utf-8") as f:
                    requests = json.load(f)
            else:
                requests = []
            
            # إضافة الطلب الجديد
            requests.append(vacation_data)
            
            # حفظ البيانات
            with open("vacation_requests.json", "w", encoding="utf-8") as f:
                json.dump(requests, f, ensure_ascii=False, indent=2)
            
            QMessageBox.information(self, "نجح الحفظ", 
                f"تم حفظ طلب الإجازة بنجاح!\n\n"
                f"الموظف: {employee_name}\n"
                f"النوع: {vacation_type}\n"
                f"المدة: {days_count} يوم\n"
                f"من {start_date.toString('yyyy-MM-dd')} إلى {end_date.toString('yyyy-MM-dd')}")
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الطلب:\n{e}")

class ManualHourlyVacationWindow(OptimizedManualDialog):
    """نافذة طلب الإجازة الساعية بإدراج يدوي ومقاسات محسّنة"""
    
    def __init__(self):
        super().__init__("⏱️ طلب إجازة ساعية - إدراج يدوي", 480, 400)
        self.setup_content()
        
    def setup_content(self):
        """إعداد محتوى النافذة"""
        
        # العنوان
        title_label = QLabel("⏱️ طلب إجازة ساعية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #f39c12, stop: 1 #e67e22);
                border-radius: 8px;
                padding: 12px;
                margin: 5px;
            }
        """)
        self.main_layout.addWidget(title_label)
        
        # اسم الموظف (إدراج يدوي)
        self.employee_name = QLineEdit()
        self.employee_name.setPlaceholderText("أدخل اسم الموظف...")
        self.create_form_row("👤 اسم الموظف:", self.employee_name)
        
        # التاريخ
        self.date = QDateEdit()
        self.date.setDate(QDate.currentDate())
        self.date.setCalendarPopup(True)
        self.create_form_row("📅 التاريخ:", self.date)
        
        # وقت البداية
        self.start_time = QTimeEdit()
        self.start_time.setTime(QTime(9, 0))
        self.create_form_row("🕘 وقت البداية:", self.start_time)
        
        # وقت النهاية
        self.end_time = QTimeEdit()
        self.end_time.setTime(QTime(11, 0))
        self.create_form_row("🕐 وقت النهاية:", self.end_time)
        
        # نوع الإجازة (إدراج يدوي)
        self.vacation_type = QLineEdit()
        self.vacation_type.setPlaceholderText("أدخل نوع الإجازة (مثال: موعد طبي)...")
        self.create_form_row("📋 نوع الإجازة:", self.vacation_type)
        
        # السبب
        self.reason = QLineEdit()
        self.reason.setPlaceholderText("أدخل سبب الإجازة...")
        self.create_form_row("📝 السبب:", self.reason)
        
        # مساحة فارغة
        self.main_layout.addStretch()
        
        # الأزرار
        self.add_button("💾 حفظ الطلب", "primary", self.save_request)
        self.add_button("❌ إلغاء", "secondary", self.reject)
        
    def save_request(self):
        """حفظ طلب الإجازة الساعية"""
        employee_name = self.employee_name.text().strip()
        vacation_type = self.vacation_type.text().strip()
        
        if not employee_name:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الموظف!")
            self.employee_name.setFocus()
            return
            
        if not vacation_type:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال نوع الإجازة!")
            self.vacation_type.setFocus()
            return
            
        start_time = self.start_time.time()
        end_time = self.end_time.time()
        
        if start_time >= end_time:
            QMessageBox.warning(self, "تحذير", "وقت البداية يجب أن يكون قبل وقت النهاية!")
            return
            
        # حساب عدد الساعات
        start_minutes = start_time.hour() * 60 + start_time.minute()
        end_minutes = end_time.hour() * 60 + end_time.minute()
        duration_minutes = end_minutes - start_minutes
        duration_hours = duration_minutes / 60
        
        # إعداد البيانات
        vacation_data = {
            "employee_name": employee_name,
            "date": self.date.date().toString("yyyy-MM-dd"),
            "start_time": start_time.toString("hh:mm"),
            "end_time": end_time.toString("hh:mm"),
            "vacation_type": vacation_type,
            "reason": self.reason.text().strip(),
            "duration_hours": round(duration_hours, 2),
            "duration_minutes": duration_minutes,
            "request_date": QDate.currentDate().toString("yyyy-MM-dd"),
            "request_time": QTime.currentTime().toString("hh:mm:ss"),
            "type": "hourly"
        }
        
        try:
            # حفظ في ملف JSON
            import json
            import os
            
            # قراءة البيانات الموجودة
            if os.path.exists("vacation_requests.json"):
                with open("vacation_requests.json", "r", encoding="utf-8") as f:
                    requests = json.load(f)
            else:
                requests = []
            
            # إضافة الطلب الجديد
            requests.append(vacation_data)
            
            # حفظ البيانات
            with open("vacation_requests.json", "w", encoding="utf-8") as f:
                json.dump(requests, f, ensure_ascii=False, indent=2)
            
            QMessageBox.information(self, "نجح الحفظ", 
                f"تم حفظ طلب الإجازة الساعية بنجاح!\n\n"
                f"الموظف: {employee_name}\n"
                f"النوع: {vacation_type}\n"
                f"المدة: {duration_hours} ساعة\n"
                f"من {start_time.toString('hh:mm')} إلى {end_time.toString('hh:mm')}\n"
                f"التاريخ: {self.date.date().toString('yyyy-MM-dd')}")
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الطلب:\n{e}")

class CompactLoginWindow(QDialog):
    """نافذة تسجيل دخول مدمجة بمقاسات صغيرة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔐 تسجيل الدخول")
        self.setFixedSize(320, 180)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - 320) // 2
        y = (screen.height() - 180) // 2
        self.move(x, y)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة تسجيل الدخول"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(8)
        
        # العنوان
        title = QLabel("🔐 تسجيل الدخول")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #3498db, stop: 1 #2980b9);
                border-radius: 6px;
                padding: 8px;
            }
        """)
        layout.addWidget(title)
        
        # اسم المستخدم
        self.username = QLineEdit()
        self.username.setPlaceholderText("اسم المستخدم")
        self.username.setText("admin")
        self.username.setFixedHeight(30)
        self.username.setLayoutDirection(Qt.RightToLeft)
        self.username.setStyleSheet("""
            QLineEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 6px;
                font-size: 12px;
                background: white;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
        """)
        layout.addWidget(self.username)
        
        # كلمة المرور
        self.password = QLineEdit()
        self.password.setPlaceholderText("كلمة المرور")
        self.password.setText("admin123")
        self.password.setEchoMode(QLineEdit.Password)
        self.password.setFixedHeight(30)
        self.password.setLayoutDirection(Qt.RightToLeft)
        self.password.setStyleSheet(self.username.styleSheet())
        layout.addWidget(self.password)
        
        # زر الدخول
        login_btn = QPushButton("🚀 دخول")
        login_btn.setFixedHeight(35)
        login_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #27ae60, stop: 1 #229954);
                border: none;
                border-radius: 6px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 13px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2ecc71;
            }
        """)
        login_btn.clicked.connect(self.login)
        layout.addWidget(login_btn)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }
        """)
    
    def login(self):
        """عملية تسجيل الدخول"""
        if self.username.text() == "admin" and self.password.text() == "admin123":
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة!")

class CompactMainWindow(QMainWindow):
    """النافذة الرئيسية المدمجة بمقاسات محسّنة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🏢 نظام إدارة الإجازات - إدراج يدوي")
        self.setGeometry(100, 100, 500, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - 500) // 2
        y = (screen.height() - 400) // 2
        self.move(x, y)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد الواجهة الرئيسية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # العنوان
        title = QLabel("🏢 نظام إدارة الإجازات")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 20px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2c3e50, stop: 1 #34495e);
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        layout.addWidget(title)
        
        # وصف النظام
        desc = QLabel("📝 إدراج يدوي للطلبات مع مقاسات محسّنة ومريحة")
        desc.setAlignment(Qt.AlignCenter)
        desc.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 14px;
                color: #7f8c8d;
                padding: 8px;
            }
        """)
        layout.addWidget(desc)
        
        # الأزرار الرئيسية
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(10)
        
        # زر الإجازة اليومية
        daily_btn = self.create_main_button(
            "📅 طلب إجازة يومية", 
            "إدراج يدوي لطلب الإجازات اليومية",
            "#e74c3c",
            self.open_daily_vacation
        )
        buttons_layout.addWidget(daily_btn)
        
        # زر الإجازة الساعية
        hourly_btn = self.create_main_button(
            "⏱️ طلب إجازة ساعية",
            "إدراج يدوي لطلب الإجازات الساعية", 
            "#f39c12",
            self.open_hourly_vacation
        )
        buttons_layout.addWidget(hourly_btn)
        
        # زر عرض الطلبات
        view_btn = self.create_main_button(
            "📋 عرض الطلبات المحفوظة",
            "استعراض جميع طلبات الإجازات المسجلة",
            "#27ae60", 
            self.view_requests
        )
        buttons_layout.addWidget(view_btn)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        # معلومات النظام
        info = QLabel("💡 جميع البيانات تدخل يدوياً - مقاسات محسّنة ومريحة")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 12px;
                color: #95a5a6;
                background: #ecf0f1;
                border-radius: 5px;
                padding: 8px;
            }
        """)
        layout.addWidget(info)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }
        """)
    
    def create_main_button(self, title, description, color, callback):
        """إنشاء زر رئيسي"""
        button = QPushButton()
        button.setFixedHeight(70)
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color}, stop: 1 rgba(0,0,0,0.1));
                border: none;
                border-radius: 10px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-weight: bold;
                text-align: center;
                padding: 10px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 rgba(255,255,255,0.2), stop: 1 {color});
            }}
            QPushButton:pressed {{
                background: {color};
            }}
        """)
        
        button.setText(f"{title}\n{description}")
        button.clicked.connect(callback)
        
        return button
    
    def open_daily_vacation(self):
        """فتح نافذة الإجازة اليومية"""
        window = ManualDailyVacationWindow()
        window.exec_()
    
    def open_hourly_vacation(self):
        """فتح نافذة الإجازة الساعية"""
        window = ManualHourlyVacationWindow()
        window.exec_()
    
    def view_requests(self):
        """عرض الطلبات المحفوظة"""
        try:
            import json
            import os
            
            if not os.path.exists("vacation_requests.json"):
                QMessageBox.information(self, "معلومات", "لا توجد طلبات محفوظة حتى الآن!")
                return
            
            with open("vacation_requests.json", "r", encoding="utf-8") as f:
                requests = json.load(f)
            
            if not requests:
                QMessageBox.information(self, "معلومات", "لا توجد طلبات محفوظة!")
                return
            
            # عرض الطلبات في نافذة جديدة
            self.show_requests_window(requests)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في قراءة الطلبات:\n{e}")
    
    def show_requests_window(self, requests):
        """عرض نافذة الطلبات"""
        window = QDialog(self)
        window.setWindowTitle("📋 الطلبات المحفوظة")
        window.setFixedSize(600, 400)
        window.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout(window)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # العنوان
        title = QLabel(f"📋 إجمالي الطلبات: {len(requests)}")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: white;
                background: #3498db;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        layout.addWidget(title)
        
        # جدول الطلبات
        table = QTextEdit()
        table.setReadOnly(True)
        table.setLayoutDirection(Qt.RightToLeft)
        table.setStyleSheet("""
            QTextEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 12px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 10px;
                background: white;
            }
        """)
        
        # تنسيق النصوص
        content = ""
        for i, request in enumerate(requests, 1):
            content += f"الطلب رقم {i}:\n"
            content += f"👤 الموظف: {request.get('employee_name', 'غير محدد')}\n"
            
            if request.get('type') == 'hourly':
                content += f"⏱️ نوع: إجازة ساعية\n"
                content += f"📅 التاريخ: {request.get('date', 'غير محدد')}\n"
                content += f"🕘 من: {request.get('start_time', 'غير محدد')} إلى: {request.get('end_time', 'غير محدد')}\n"
                content += f"⏰ المدة: {request.get('duration_hours', 0)} ساعة\n"
            else:
                content += f"📅 نوع: إجازة يومية\n"
                content += f"📅 من: {request.get('start_date', 'غير محدد')} إلى: {request.get('end_date', 'غير محدد')}\n"
                content += f"📊 عدد الأيام: {request.get('days_count', 0)} يوم\n"
            
            content += f"📋 نوع الإجازة: {request.get('vacation_type', 'غير محدد')}\n"
            content += f"📝 السبب: {request.get('reason', 'غير محدد')}\n"
            content += f"📄 ملاحظات: {request.get('notes', 'لا توجد')}\n"
            content += f"🕒 تاريخ الطلب: {request.get('request_date', 'غير محدد')} - {request.get('request_time', 'غير محدد')}\n"
            content += "=" * 50 + "\n\n"
        
        table.setPlainText(content)
        layout.addWidget(table)
        
        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setFixedHeight(35)
        close_btn.setStyleSheet("""
            QPushButton {
                background: #95a5a6;
                border: none;
                border-radius: 6px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 13px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #7f8c8d;
            }
        """)
        close_btn.clicked.connect(window.close)
        layout.addWidget(close_btn)
        
        window.exec_()

def main():
    """الدالة الرئيسية للنظام المحسّن"""
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    font = QFont("Sakkal Majalla", 10)
    app.setFont(font)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🚀 تشغيل النظام المحسّن مع الإدراج اليدوي...")
    print("=" * 50)
    print("✅ مقاسات محسّنة ومريحة")
    print("✅ إدراج يدوي للموظفين وأنواع الإجازات")  
    print("✅ توجيه عربي كامل")
    print("✅ نوافذ مدمجة وواضحة")
    print("=" * 50)
    
    # تسجيل الدخول
    login = CompactLoginWindow()
    if login.exec_() == QDialog.Accepted:
        print("✅ تم تسجيل الدخول بنجاح!")
        
        # النافذة الرئيسية
        main_window = CompactMainWindow()
        main_window.show()
        
        return app.exec_()
    else:
        print("❌ تم إلغاء تسجيل الدخول")
        return 0

if __name__ == "__main__":
    sys.exit(main())
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شاشة تسجيل الدخول فقط
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class TestLoginWindow(QDialog):
    """نافذة اختبار تسجيل الدخول"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة تسجيل الدخول"""
        self.setWindowTitle("🔐 اختبار شاشة تسجيل الدخول")
        self.setFixedSize(500, 400)
        
        # الخط
        self.setFont(QFont("Sakkal Majalla", 12))
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # العنوان
        title = QLabel("🔐 تسجيل الدخول")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            margin-bottom: 20px;
        """)
        layout.addWidget(title)
        
        # نموذج تسجيل الدخول
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        
        # مربع اسم المستخدم
        username_label = QLabel("👤 اسم المستخدم:")
        username_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
        """)
        
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم...")
        self.username_edit.setText("admin")
        self.username_edit.setStyleSheet("""
            QLineEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 12px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                color: #1a1a1a;
                min-height: 40px;
                max-height: 50px;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
                background: #ffffff;
            }
            QLineEdit:hover {
                border: 2px solid #5dade2;
            }
        """)
        
        form_layout.addRow(username_label, self.username_edit)
        
        # مربع كلمة المرور
        password_label = QLabel("🔐 كلمة المرور:")
        password_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
        """)
        
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("أدخل كلمة المرور...")
        self.password_edit.setText("admin123")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setStyleSheet("""
            QLineEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 12px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                color: #1a1a1a;
                min-height: 40px;
                max-height: 50px;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
                background: #ffffff;
            }
            QLineEdit:hover {
                border: 2px solid #5dade2;
            }
        """)
        
        form_layout.addRow(password_label, self.password_edit)
        
        layout.addLayout(form_layout)
        
        # زر تسجيل الدخول
        login_button = QPushButton("🚀 تسجيل الدخول")
        login_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #27ae60, stop: 1 #229954);
                border: none;
                border-radius: 12px;
                color: white;
                font-weight: bold;
                font-size: 16px;
                padding: 15px;
                min-height: 40px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2ecc71, stop: 1 #27ae60);
            }
            QPushButton:pressed {
                background: #1e8449;
            }
        """)
        login_button.clicked.connect(self.test_login)
        layout.addWidget(login_button)
        
        # إضافة مساحة
        layout.addStretch()
        
        # تطبيق الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
        
    def test_login(self):
        """اختبار تسجيل الدخول"""
        username = self.username_edit.text()
        password = self.password_edit.text()
        
        if username == "admin" and password == "admin123":
            QMessageBox.information(self, "✅ نجح", "تم تسجيل الدخول بنجاح!")
            self.accept()
        else:
            QMessageBox.warning(self, "❌ خطأ", "بيانات تسجيل الدخول غير صحيحة!")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    font = QFont("Sakkal Majalla", 12)
    app.setFont(font)
    
    window = TestLoginWindow()
    window.show()
    
    print("🔍 اختبار شاشة تسجيل الدخول:")
    print("👤 اسم المستخدم: admin")
    print("🔐 كلمة المرور: admin123")
    print("📝 تحقق من وضوح مربعات النص!")
    
    sys.exit(app.exec_())
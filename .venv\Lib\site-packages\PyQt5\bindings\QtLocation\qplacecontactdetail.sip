// qplacecontactdetail.sip generated by MetaSIP
//
// This file is part of the QtLocation Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_5_0 -)

class QPlaceContactDetail
{
%TypeHeaderCode
#include <qplacecontactdetail.h>
%End

public:
    static const QString Phone;
    static const QString Email;
    static const QString Website;
    static const QString Fax;
    QPlaceContactDetail();
    QPlaceContactDetail(const QPlaceContactDetail &other);
    virtual ~QPlaceContactDetail();
    bool operator==(const QPlaceContactDetail &other) const;
    bool operator!=(const QPlaceContactDetail &other) const;
    QString label() const;
    void setLabel(const QString &label);
    QString value() const;
    void setValue(const QString &value);
    void clear();
};

%End

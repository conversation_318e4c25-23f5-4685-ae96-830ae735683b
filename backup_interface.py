#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إدارة النسخ الاحتياطية
Backup Management Interface
"""

import os
import sys
from datetime import datetime
from backup_system import BackupSystem
import json

class BackupInterface:
    def __init__(self):
        self.backup_system = BackupSystem()
    
    def display_main_menu(self):
        """عرض القائمة الرئيسية"""
        stats = self.backup_system.get_backup_statistics()
        
        print("\n" + "="*70)
        print("💾 نظام إدارة النسخ الاحتياطية")
        print("="*70)
        print(f"📊 الإحصائيات: {stats['total_backups']} نسخة | {self._format_size(stats['total_size'])}")
        
        if stats['latest_backup']:
            latest_date = datetime.fromisoformat(stats['latest_backup']['created_at']).strftime('%Y-%m-%d %H:%M')
            print(f"🕐 آخر نسخة احتياطية: {latest_date}")
        
        print("-"*70)
        print("1. إنشاء نسخة احتياطية يدوية")
        print("2. عرض قائمة النسخ الاحتياطية")
        print("3. استعادة نسخة احتياطية")
        print("4. حذف نسخة احتياطية")
        print("5. التحقق من سلامة النسخ")
        print("6. إعدادات النسخ الاحتياطي")
        print("7. تشغيل نسخة احتياطية تلقائية")
        print("8. إحصائيات مفصلة")
        print("9. تنظيف النسخ القديمة")
        print("0. خروج")
        print("="*70)
    
    def create_manual_backup(self):
        """إنشاء نسخة احتياطية يدوية"""
        print("\n💾 إنشاء نسخة احتياطية يدوية")
        print("-" * 40)
        
        description = input("وصف النسخة الاحتياطية (اختياري): ").strip()
        if not description:
            description = f"نسخة يدوية - {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        print("⏳ جاري إنشاء النسخة الاحتياطية...")
        success, message = self.backup_system.create_backup('manual', description)
        
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
    
    def list_backups(self):
        """عرض قائمة النسخ الاحتياطية"""
        backups = self.backup_system.list_backups()
        
        if not backups:
            print("\n📭 لا توجد نسخ احتياطية")
            return None
        
        print(f"\n💾 قائمة النسخ الاحتياطية ({len(backups)} نسخة)")
        print("-" * 80)
        
        for i, backup in enumerate(backups, 1):
            self._display_backup_item(i, backup)
        
        return backups
    
    def _display_backup_item(self, index, backup):
        """عرض عنصر نسخة احتياطية"""
        created_date = datetime.fromisoformat(backup['created_at']).strftime('%Y-%m-%d %H:%M:%S')
        size_str = self._format_size(backup.get('size', 0))
        type_icon = "📦" if backup.get('type') == 'compressed' else "📁"
        
        print(f"{index:2d}. {type_icon} {backup['name']}")
        print(f"    📅 {created_date} | 💾 {size_str} | 📄 {backup.get('files_count', 0)} ملف")
        print(f"    📝 {backup.get('description', 'بدون وصف')}")
        print()
    
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        backups = self.list_backups()
        if not backups:
            return
        
        print("🔄 استعادة نسخة احتياطية")
        print("-" * 40)
        
        try:
            choice = int(input("اختر رقم النسخة الاحتياطية للاستعادة: ")) - 1
            
            if 0 <= choice < len(backups):
                backup = backups[choice]
                
                print(f"\n⚠️ تحذير: سيتم استبدال الملفات الحالية!")
                print(f"النسخة المختارة: {backup['name']}")
                print(f"التاريخ: {datetime.fromisoformat(backup['created_at']).strftime('%Y-%m-%d %H:%M:%S')}")
                
                confirm = input("هل أنت متأكد من المتابعة؟ (y/n): ").lower()
                
                if confirm in ['y', 'yes', 'نعم', 'ن']:
                    print("⏳ جاري استعادة النسخة الاحتياطية...")
                    success, message = self.backup_system.restore_backup(backup['path'])
                    
                    if success:
                        print(f"✅ {message}")
                        print("🔄 يُنصح بإعادة تشغيل التطبيق")
                    else:
                        print(f"❌ {message}")
                else:
                    print("❌ تم إلغاء العملية")
            else:
                print("⚠️ اختيار غير صحيح")
                
        except ValueError:
            print("⚠️ يرجى إدخال رقم صحيح")
    
    def delete_backup(self):
        """حذف نسخة احتياطية"""
        backups = self.list_backups()
        if not backups:
            return
        
        print("🗑️ حذف نسخة احتياطية")
        print("-" * 40)
        
        try:
            choice = int(input("اختر رقم النسخة الاحتياطية للحذف: ")) - 1
            
            if 0 <= choice < len(backups):
                backup = backups[choice]
                
                print(f"\n⚠️ تحذير: سيتم حذف النسخة الاحتياطية نهائياً!")
                print(f"النسخة المختارة: {backup['name']}")
                print(f"الحجم: {self._format_size(backup.get('size', 0))}")
                
                confirm = input("هل أنت متأكد من الحذف؟ (y/n): ").lower()
                
                if confirm in ['y', 'yes', 'نعم', 'ن']:
                    success, message = self.backup_system.delete_backup(backup['path'])
                    
                    if success:
                        print(f"✅ {message}")
                    else:
                        print(f"❌ {message}")
                else:
                    print("❌ تم إلغاء العملية")
            else:
                print("⚠️ اختيار غير صحيح")
                
        except ValueError:
            print("⚠️ يرجى إدخال رقم صحيح")
    
    def verify_backups(self):
        """التحقق من سلامة النسخ الاحتياطية"""
        backups = self.backup_system.list_backups()
        
        if not backups:
            print("\n📭 لا توجد نسخ احتياطية للتحقق منها")
            return
        
        print(f"\n🔍 التحقق من سلامة النسخ الاحتياطية ({len(backups)} نسخة)")
        print("-" * 60)
        
        valid_backups = 0
        invalid_backups = 0
        
        for i, backup in enumerate(backups, 1):
            print(f"فحص {i}/{len(backups)}: {backup['name']}")
            
            success, message = self.backup_system.verify_backup_integrity(backup['path'])
            
            if success:
                print(f"  ✅ {message}")
                valid_backups += 1
            else:
                print(f"  ❌ {message}")
                invalid_backups += 1
        
        print(f"\n📊 نتائج الفحص:")
        print(f"  ✅ نسخ سليمة: {valid_backups}")
        print(f"  ❌ نسخ تالفة: {invalid_backups}")
    
    def manage_settings(self):
        """إدارة إعدادات النسخ الاحتياطي"""
        config = self.backup_system.load_config()
        
        while True:
            print("\n⚙️ إعدادات النسخ الاحتياطي")
            print("-" * 40)
            
            settings = [
                ('auto_backup_enabled', 'تفعيل النسخ التلقائي', 'bool'),
                ('backup_interval_hours', 'فترة النسخ التلقائي (ساعات)', 'int'),
                ('max_backups_to_keep', 'عدد النسخ المحفوظة', 'int'),
                ('compress_backups', 'ضغط النسخ الاحتياطية', 'bool'),
                ('backup_location', 'مجلد النسخ الاحتياطية', 'str')
            ]
            
            for i, (key, description, data_type) in enumerate(settings, 1):
                current_value = config.get(key, 'غير محدد')
                print(f"{i}. {description}: {current_value}")
            
            print("0. العودة للقائمة الرئيسية")
            
            choice = input("\nاختر إعداد للتعديل: ").strip()
            
            if choice == '0':
                break
            
            try:
                index = int(choice) - 1
                if 0 <= index < len(settings):
                    key, description, data_type = settings[index]
                    current_value = config.get(key)
                    
                    print(f"\n📝 تعديل: {description}")
                    print(f"القيمة الحالية: {current_value}")
                    
                    if data_type == 'bool':
                        new_value = input("القيمة الجديدة (true/false): ").strip().lower()
                        if new_value in ['true', 'false']:
                            config[key] = new_value == 'true'
                        else:
                            print("⚠️ قيمة غير صحيحة")
                            continue
                    elif data_type == 'int':
                        new_value = input("القيمة الجديدة (رقم): ").strip()
                        try:
                            config[key] = int(new_value)
                        except ValueError:
                            print("⚠️ يرجى إدخال رقم صحيح")
                            continue
                    else:  # str
                        new_value = input("القيمة الجديدة: ").strip()
                        if new_value:
                            config[key] = new_value
                        else:
                            print("⚠️ القيمة لا يمكن أن تكون فارغة")
                            continue
                    
                    self.backup_system.save_config(config)
                    print("✅ تم تحديث الإعداد")
                else:
                    print("⚠️ اختيار غير صحيح")
            except ValueError:
                print("⚠️ يرجى إدخال رقم صحيح")
    
    def run_auto_backup(self):
        """تشغيل النسخ الاحتياطي التلقائي"""
        print("\n🤖 تشغيل النسخ الاحتياطي التلقائي")
        print("-" * 40)
        
        print("⏳ فحص الحاجة لنسخة احتياطية...")
        success, message = self.backup_system.run_auto_backup()
        
        if success:
            print(f"✅ {message}")
        else:
            print(f"ℹ️ {message}")
    
    def show_detailed_statistics(self):
        """عرض إحصائيات مفصلة"""
        stats = self.backup_system.get_backup_statistics()
        config = self.backup_system.load_config()
        
        print("\n📊 إحصائيات مفصلة للنسخ الاحتياطية")
        print("=" * 60)
        
        print(f"📈 الإحصائيات العامة:")
        print(f"   إجمالي النسخ: {stats['total_backups']}")
        print(f"   الحجم الإجمالي: {self._format_size(stats['total_size'])}")
        print(f"   نسخ مضغوطة: {stats['compressed_backups']}")
        print(f"   نسخ مجلدات: {stats['folder_backups']}")
        
        if stats['latest_backup']:
            latest_date = datetime.fromisoformat(stats['latest_backup']['created_at']).strftime('%Y-%m-%d %H:%M:%S')
            print(f"\n🕐 آخر نسخة احتياطية:")
            print(f"   التاريخ: {latest_date}")
            print(f"   الحجم: {self._format_size(stats['latest_backup'].get('size', 0))}")
            print(f"   النوع: {stats['latest_backup'].get('type', 'غير محدد')}")
        
        if stats['oldest_backup']:
            oldest_date = datetime.fromisoformat(stats['oldest_backup']['created_at']).strftime('%Y-%m-%d %H:%M:%S')
            print(f"\n📅 أقدم نسخة احتياطية:")
            print(f"   التاريخ: {oldest_date}")
            print(f"   الحجم: {self._format_size(stats['oldest_backup'].get('size', 0))}")
        
        print(f"\n⚙️ الإعدادات الحالية:")
        print(f"   النسخ التلقائي: {'مفعل' if config.get('auto_backup_enabled') else 'معطل'}")
        print(f"   فترة النسخ: {config.get('backup_interval_hours', 24)} ساعة")
        print(f"   عدد النسخ المحفوظة: {config.get('max_backups_to_keep', 30)}")
        print(f"   الضغط: {'مفعل' if config.get('compress_backups') else 'معطل'}")
    
    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        print("\n🧹 تنظيف النسخ الاحتياطية القديمة")
        print("-" * 40)
        
        config = self.backup_system.load_config()
        max_backups = config.get('max_backups_to_keep', 30)
        
        backups = self.backup_system.list_backups()
        
        if len(backups) <= max_backups:
            print(f"ℹ️ عدد النسخ الحالي ({len(backups)}) ضمن الحد المسموح ({max_backups})")
            return
        
        excess_count = len(backups) - max_backups
        print(f"⚠️ يوجد {excess_count} نسخة زائدة عن الحد المسموح")
        
        confirm = input("هل تريد حذف النسخ الزائدة؟ (y/n): ").lower()
        
        if confirm in ['y', 'yes', 'نعم', 'ن']:
            # حذف النسخ الزائدة (الأقدم)
            backups_to_delete = backups[max_backups:]
            deleted_count = 0
            
            for backup in backups_to_delete:
                success, message = self.backup_system.delete_backup(backup['path'])
                if success:
                    deleted_count += 1
                    print(f"✅ تم حذف: {backup['name']}")
                else:
                    print(f"❌ فشل حذف: {backup['name']} - {message}")
            
            print(f"\n📊 تم حذف {deleted_count} نسخة احتياطية")
        else:
            print("❌ تم إلغاء العملية")
    
    def _format_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 بايت"
        
        size_names = ["بايت", "كيلوبايت", "ميجابايت", "جيجابايت"]
        i = 0
        
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def run(self):
        """تشغيل واجهة إدارة النسخ الاحتياطية"""
        print("💾 مرحباً بك في نظام إدارة النسخ الاحتياطية!")
        
        while True:
            try:
                self.display_main_menu()
                choice = input("اختر العملية المطلوبة: ").strip()
                
                if choice == '0':
                    print("👋 شكراً لاستخدام نظام النسخ الاحتياطية!")
                    break
                elif choice == '1':
                    self.create_manual_backup()
                elif choice == '2':
                    self.list_backups()
                elif choice == '3':
                    self.restore_backup()
                elif choice == '4':
                    self.delete_backup()
                elif choice == '5':
                    self.verify_backups()
                elif choice == '6':
                    self.manage_settings()
                elif choice == '7':
                    self.run_auto_backup()
                elif choice == '8':
                    self.show_detailed_statistics()
                elif choice == '9':
                    self.cleanup_old_backups()
                else:
                    print("⚠️ اختيار غير صحيح")
                
                if choice != '0':
                    input("\nاضغط Enter للمتابعة...")
                
            except KeyboardInterrupt:
                print("\n\n👋 تم إيقاف البرنامج")
                break
            except Exception as e:
                print(f"\n❌ خطأ غير متوقع: {e}")
                input("اضغط Enter للمتابعة...")

if __name__ == "__main__":
    interface = BackupInterface()
    interface.run()

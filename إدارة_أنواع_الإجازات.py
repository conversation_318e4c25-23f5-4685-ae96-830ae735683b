#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة أنواع الإجازات مع إمكانية التعديل
"""

import sys
import json
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class VacationTypesManager:
    """مدير أنواع الإجازات"""
    
    def __init__(self):
        self.data_file = "vacation_types_data.json"
        self.vacation_types = self.load_vacation_types()
    
    def load_vacation_types(self):
        """تحميل أنواع الإجازات من الملف"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('vacation_types', [])
            except:
                pass
        
        # قائمة افتراضية إذا لم يوجد الملف
        default_types = [
            {"id": 1, "name": "إجازة اعتيادية", "description": "إجازة سنوية اعتيادية", "max_days": 30},
            {"id": 2, "name": "إجازة مرضية", "description": "إجازة بسبب المرض", "max_days": 90},
            {"id": 3, "name": "إجازة طارئة", "description": "إجازة لظرف طارئ", "max_days": 7},
            {"id": 4, "name": "إجازة بدون راتب", "description": "إجازة بدون راتب", "max_days": 365},
            {"id": 5, "name": "إجازة أمومة", "description": "إجازة وضع للأمهات", "max_days": 70},
            {"id": 6, "name": "إجازة دراسية", "description": "إجازة لإكمال الدراسة", "max_days": 180},
            {"id": 7, "name": "إجازة حج وعمرة", "description": "إجازة دينية للحج أو العمرة", "max_days": 21},
            {"id": 8, "name": "إجازة ساعية", "description": "إجازة لساعات محددة", "max_days": 1}
        ]
        self.save_vacation_types(default_types)
        return default_types
    
    def save_vacation_types(self, types=None):
        """حفظ أنواع الإجازات في الملف"""
        if types is None:
            types = self.vacation_types
        
        data = {
            "vacation_types": types,
            "last_updated": QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss")
        }
        
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في حفظ البيانات: {e}")
            return False
    
    def get_vacation_type_names(self):
        """الحصول على أسماء أنواع الإجازات فقط"""
        return [vtype["name"] for vtype in self.vacation_types]
    
    def get_vacation_type_by_name(self, name):
        """الحصول على بيانات نوع إجازة بالاسم"""
        for vtype in self.vacation_types:
            if vtype["name"] == name:
                return vtype
        return None
    
    def add_vacation_type(self, name, description="", max_days=30):
        """إضافة نوع إجازة جديد"""
        new_id = max([vtype["id"] for vtype in self.vacation_types], default=0) + 1
        new_type = {
            "id": new_id,
            "name": name,
            "description": description,
            "max_days": max_days
        }
        self.vacation_types.append(new_type)
        self.save_vacation_types()
        return new_type
    
    def update_vacation_type(self, type_id, name=None, description=None, max_days=None):
        """تحديث بيانات نوع إجازة"""
        for vtype in self.vacation_types:
            if vtype["id"] == type_id:
                if name is not None:
                    vtype["name"] = name
                if description is not None:
                    vtype["description"] = description
                if max_days is not None:
                    vtype["max_days"] = max_days
                self.save_vacation_types()
                return True
        return False
    
    def delete_vacation_type(self, type_id):
        """حذف نوع إجازة"""
        self.vacation_types = [vtype for vtype in self.vacation_types if vtype["id"] != type_id]
        self.save_vacation_types()

class VacationTypesManagementWindow(QDialog):
    """نافذة إدارة أنواع الإجازات"""
    
    def __init__(self):
        super().__init__()
        self.vacation_manager = VacationTypesManager()
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة إدارة أنواع الإجازات"""
        self.setWindowTitle("📋 إدارة أنواع الإجازات")
        self.setFixedSize(800, 600)
        
        # تطبيق التوجيه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - 800) // 2
        y = (screen.height() - 600) // 2
        self.move(x, y)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # العنوان
        title_label = QLabel("📋 إدارة أنواع الإجازات")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 20px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #8e44ad, stop: 1 #9b59b6);
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إطار المحتوى
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        content_layout = QVBoxLayout(content_frame)
        content_layout.setSpacing(10)
        
        # أزرار الإدارة
        buttons_layout = QHBoxLayout()
        
        add_button = QPushButton("➕ إضافة نوع إجازة")
        add_button.setStyleSheet(self.get_button_style("#27ae60"))
        add_button.clicked.connect(self.add_vacation_type)
        
        edit_button = QPushButton("✏️ تعديل")
        edit_button.setStyleSheet(self.get_button_style("#3498db"))
        edit_button.clicked.connect(self.edit_vacation_type)
        
        delete_button = QPushButton("🗑️ حذف")
        delete_button.setStyleSheet(self.get_button_style("#e74c3c"))
        delete_button.clicked.connect(self.delete_vacation_type)
        
        refresh_button = QPushButton("🔄 تحديث")
        refresh_button.setStyleSheet(self.get_button_style("#f39c12"))
        refresh_button.clicked.connect(self.refresh_table)
        
        buttons_layout.addWidget(add_button)
        buttons_layout.addWidget(edit_button)
        buttons_layout.addWidget(delete_button)
        buttons_layout.addWidget(refresh_button)
        buttons_layout.addStretch()
        
        content_layout.addLayout(buttons_layout)
        
        # جدول أنواع الإجازات
        self.types_table = QTableWidget()
        self.types_table.setColumnCount(4)
        self.types_table.setHorizontalHeaderLabels([
            "الرقم", "نوع الإجازة", "الوصف", "الحد الأقصى (أيام)"
        ])
        
        # تطبيق التوجيه العربي على الجدول
        self.types_table.setLayoutDirection(Qt.RightToLeft)
        
        # تعيين عرض الأعمدة
        header = self.types_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.types_table.setColumnWidth(0, 60)
        
        self.types_table.setAlternatingRowColors(True)
        self.types_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.types_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                gridline-color: #dee2e6;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 13px;
            }
            QHeaderView::section {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 8px;
                font-weight: bold;
                color: #495057;
            }
            QTableWidget::item:selected {
                background: #007bff;
                color: white;
            }
        """)
        
        content_layout.addWidget(self.types_table)
        main_layout.addWidget(content_frame)
        
        # أزرار التحكم السفلية
        bottom_buttons = QHBoxLayout()
        
        close_button = QPushButton("❌ إغلاق")
        close_button.setStyleSheet(self.get_button_style("#6c757d"))
        close_button.clicked.connect(self.accept)
        
        bottom_buttons.addStretch()
        bottom_buttons.addWidget(close_button)
        
        main_layout.addLayout(bottom_buttons)
        
        # تعبئة الجدول
        self.refresh_table()
        
        # الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
    
    def get_button_style(self, color):
        """إنشاء نمط للأزرار"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color}, stop: 1 rgba(0,0,0,0.1));
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px 15px;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background: {color};
            }}
            QPushButton:pressed {{
                background: rgba(0,0,0,0.2);
            }}
        """
    
    def refresh_table(self):
        """تحديث جدول أنواع الإجازات"""
        self.vacation_manager.vacation_types = self.vacation_manager.load_vacation_types()
        types = self.vacation_manager.vacation_types
        
        self.types_table.setRowCount(len(types))
        
        for row, vtype in enumerate(types):
            self.types_table.setItem(row, 0, QTableWidgetItem(str(vtype["id"])))
            self.types_table.setItem(row, 1, QTableWidgetItem(vtype["name"]))
            self.types_table.setItem(row, 2, QTableWidgetItem(vtype["description"]))
            self.types_table.setItem(row, 3, QTableWidgetItem(str(vtype["max_days"])))
    
    def add_vacation_type(self):
        """إضافة نوع إجازة جديد"""
        dialog = VacationTypeDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            name, description, max_days = dialog.get_data()
            if name.strip():
                self.vacation_manager.add_vacation_type(name, description, max_days)
                self.refresh_table()
                QMessageBox.information(self, "✅ تم الحفظ", f"تم إضافة نوع الإجازة: {name}")
            else:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم نوع الإجازة!")
    
    def edit_vacation_type(self):
        """تعديل نوع إجازة محدد"""
        current_row = self.types_table.currentRow()
        if current_row >= 0:
            type_id = int(self.types_table.item(current_row, 0).text())
            vacation_type = None
            for vtype in self.vacation_manager.vacation_types:
                if vtype["id"] == type_id:
                    vacation_type = vtype
                    break
            
            if vacation_type:
                dialog = VacationTypeDialog(self, vacation_type)
                if dialog.exec_() == QDialog.Accepted:
                    name, description, max_days = dialog.get_data()
                    if name.strip():
                        self.vacation_manager.update_vacation_type(type_id, name, description, max_days)
                        self.refresh_table()
                        QMessageBox.information(self, "✅ تم التحديث", f"تم تحديث نوع الإجازة: {name}")
                    else:
                        QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم نوع الإجازة!")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد نوع إجازة للتعديل!")
    
    def delete_vacation_type(self):
        """حذف نوع إجازة محدد"""
        current_row = self.types_table.currentRow()
        if current_row >= 0:
            type_name = self.types_table.item(current_row, 1).text()
            type_id = int(self.types_table.item(current_row, 0).text())
            
            reply = QMessageBox.question(self, "تأكيد الحذف", 
                                       f"هل تريد حذف نوع الإجازة: {type_name}؟\n\nتحذير: سيتم حذف جميع الطلبات المرتبطة بهذا النوع!")
            
            if reply == QMessageBox.Yes:
                self.vacation_manager.delete_vacation_type(type_id)
                self.refresh_table()
                QMessageBox.information(self, "✅ تم الحذف", f"تم حذف نوع الإجازة: {type_name}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد نوع إجازة للحذف!")

class VacationTypeDialog(QDialog):
    """نافذة إضافة/تعديل نوع إجازة"""
    
    def __init__(self, parent=None, vacation_type=None):
        super().__init__(parent)
        self.vacation_type = vacation_type
        self.setup_ui()
        
        if vacation_type:
            self.load_vacation_type_data()
    
    def setup_ui(self):
        """إعداد واجهة الحوار"""
        title = "✏️ تعديل نوع إجازة" if self.vacation_type else "➕ إضافة نوع إجازة جديد"
        self.setWindowTitle(title)
        self.setFixedSize(450, 350)
        
        # تطبيق التوجيه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        if self.parent():
            parent_geo = self.parent().geometry()
            x = parent_geo.x() + (parent_geo.width() - 450) // 2
            y = parent_geo.y() + (parent_geo.height() - 350) // 2
            self.move(x, y)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #8e44ad, stop: 1 #9b59b6);
                border-radius: 10px;
                padding: 15px;
            }
        """)
        layout.addWidget(title_label)
        
        # النموذج
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(10)
        
        # اسم نوع الإجازة
        self.create_form_row("📋 اسم نوع الإجازة:", form_layout)
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("مثال: إجازة اعتيادية...")
        self.name_edit.setLayoutDirection(Qt.RightToLeft)
        self.apply_input_style(self.name_edit)
        form_layout.addWidget(self.name_edit)
        
        # الوصف
        self.create_form_row("📝 وصف نوع الإجازة:", form_layout)
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("وصف مختصر لنوع الإجازة...")
        self.description_edit.setLayoutDirection(Qt.RightToLeft)
        self.apply_input_style(self.description_edit)
        form_layout.addWidget(self.description_edit)
        
        # الحد الأقصى للأيام
        self.create_form_row("📊 الحد الأقصى (أيام):", form_layout)
        self.max_days_spin = QSpinBox()
        self.max_days_spin.setMinimum(1)
        self.max_days_spin.setMaximum(365)
        self.max_days_spin.setValue(30)
        self.max_days_spin.setLayoutDirection(Qt.RightToLeft)
        self.apply_input_style(self.max_days_spin)
        form_layout.addWidget(self.max_days_spin)
        
        layout.addWidget(form_frame)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        save_button = QPushButton("💾 حفظ")
        save_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #27ae60, stop: 1 #20c997);
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px 20px;
                min-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #34ce57, stop: 1 #27ae60);
            }
        """)
        save_button.clicked.connect(self.accept)
        
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #6c757d, stop: 1 #5a6268);
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px 20px;
                min-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #868e96, stop: 1 #6c757d);
            }
        """)
        cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)
        
        layout.addLayout(buttons_layout)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
    
    def create_form_row(self, text, layout):
        """إنشاء تسمية للنموذج"""
        label = QLabel(text)
        label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                margin-top: 5px;
            }
        """)
        layout.addWidget(label)
    
    def apply_input_style(self, widget):
        """تطبيق أنماط على عناصر الإدخال"""
        widget.setStyleSheet("""
            QLineEdit, QSpinBox {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                background: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                color: #000000;
                min-height: 15px;
            }
            QLineEdit:focus, QSpinBox:focus {
                border: 2px solid #007bff;
                background: white;
            }
        """)
    
    def load_vacation_type_data(self):
        """تحميل بيانات نوع الإجازة للتعديل"""
        if self.vacation_type:
            self.name_edit.setText(self.vacation_type["name"])
            self.description_edit.setText(self.vacation_type["description"])
            self.max_days_spin.setValue(self.vacation_type["max_days"])
    
    def get_data(self):
        """الحصول على البيانات المدخلة"""
        return (
            self.name_edit.text().strip(),
            self.description_edit.text().strip(),
            self.max_days_spin.value()
        )

# إنشاء مدير أنواع الإجازات الافتراضي
_vacation_types_manager = VacationTypesManager()

def get_vacation_types_manager():
    """الحصول على مدير أنواع الإجازات"""
    return _vacation_types_manager

def get_vacation_type_names():
    """الحصول على أسماء أنواع الإجازات للقوائم المنسدلة"""
    return _vacation_types_manager.get_vacation_type_names()

def show_vacation_types_management():
    """عرض نافذة إدارة أنواع الإجازات"""
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    window = VacationTypesManagementWindow()
    return window.exec_()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي والتوجيه
    font = QFont("Sakkal Majalla", 10)
    app.setFont(font)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = VacationTypesManagementWindow()
    window.show()
    
    sys.exit(app.exec_())
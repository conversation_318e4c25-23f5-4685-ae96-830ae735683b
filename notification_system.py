#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الإشعارات والتنبيهات المتقدم
Advanced Notification and Alert System
"""

import sqlite3
import json
from datetime import datetime, timedelta
from database import VacationDatabase
import os

class NotificationSystem:
    def __init__(self, db_path='vacation_system.db'):
        self.db = VacationDatabase(db_path)
        self.notifications_db = 'notifications.db'
        self.init_notifications_db()
    
    def init_notifications_db(self):
        """إنشاء قاعدة بيانات الإشعارات"""
        conn = sqlite3.connect(self.notifications_db)
        cursor = conn.cursor()
        
        # جدول الإشعارات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type TEXT NOT NULL,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                employee_name TEXT,
                priority TEXT DEFAULT 'medium',
                status TEXT DEFAULT 'unread',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                scheduled_for TIMESTAMP,
                data TEXT
            )
        ''')
        
        # جدول إعدادات التنبيهات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS alert_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_name TEXT UNIQUE NOT NULL,
                setting_value TEXT NOT NULL,
                description TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إعدادات افتراضية
        default_settings = [
            ('low_balance_threshold', '5', 'حد التنبيه عند انخفاض الرصيد'),
            ('vacation_expiry_days', '30', 'عدد الأيام للتنبيه قبل انتهاء الإجازة'),
            ('enable_email_notifications', 'false', 'تفعيل الإشعارات عبر البريد الإلكتروني'),
            ('enable_desktop_notifications', 'true', 'تفعيل الإشعارات على سطح المكتب'),
            ('daily_report_time', '09:00', 'وقت إرسال التقرير اليومي')
        ]
        
        for setting_name, setting_value, description in default_settings:
            cursor.execute('''
                INSERT OR IGNORE INTO alert_settings (setting_name, setting_value, description)
                VALUES (?, ?, ?)
            ''', (setting_name, setting_value, description))
        
        conn.commit()
        conn.close()
    
    def get_setting(self, setting_name, default_value=None):
        """الحصول على إعداد معين"""
        conn = sqlite3.connect(self.notifications_db)
        cursor = conn.cursor()
        
        cursor.execute('SELECT setting_value FROM alert_settings WHERE setting_name = ?', (setting_name,))
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return result[0]
        return default_value
    
    def update_setting(self, setting_name, setting_value):
        """تحديث إعداد معين"""
        conn = sqlite3.connect(self.notifications_db)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE alert_settings 
            SET setting_value = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE setting_name = ?
        ''', (setting_value, setting_name))
        
        conn.commit()
        conn.close()
    
    def create_notification(self, notification_type, title, message, employee_name=None, 
                          priority='medium', scheduled_for=None, data=None):
        """إنشاء إشعار جديد"""
        conn = sqlite3.connect(self.notifications_db)
        cursor = conn.cursor()
        
        data_json = json.dumps(data) if data else None
        
        cursor.execute('''
            INSERT INTO notifications 
            (type, title, message, employee_name, priority, scheduled_for, data)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (notification_type, title, message, employee_name, priority, scheduled_for, data_json))
        
        notification_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return notification_id
    
    def get_notifications(self, status='unread', limit=50):
        """الحصول على الإشعارات"""
        conn = sqlite3.connect(self.notifications_db)
        cursor = conn.cursor()
        
        if status:
            cursor.execute('''
                SELECT * FROM notifications 
                WHERE status = ? 
                ORDER BY priority DESC, created_at DESC 
                LIMIT ?
            ''', (status, limit))
        else:
            cursor.execute('''
                SELECT * FROM notifications 
                ORDER BY created_at DESC 
                LIMIT ?
            ''', (limit,))
        
        notifications = cursor.fetchall()
        conn.close()
        
        return notifications
    
    def mark_notification_read(self, notification_id):
        """تحديد الإشعار كمقروء"""
        conn = sqlite3.connect(self.notifications_db)
        cursor = conn.cursor()
        
        cursor.execute('UPDATE notifications SET status = ? WHERE id = ?', ('read', notification_id))
        
        conn.commit()
        conn.close()
    
    def check_low_balance_alerts(self):
        """فحص التنبيهات لانخفاض الرصيد"""
        threshold = float(self.get_setting('low_balance_threshold', '5'))
        employees = self.db.get_all_employees()
        alerts_created = 0
        
        for employee in employees:
            balance = self.db.get_employee_balance(employee)
            net_balance = balance['net_balance']
            
            if net_balance <= threshold:
                # التحقق من عدم وجود تنبيه مشابه حديث
                if not self._has_recent_notification('low_balance', employee, days=7):
                    title = f"تنبيه: رصيد منخفض - {employee}"
                    message = f"رصيد الموظف {employee} منخفض: {net_balance:.2f} يوم"
                    
                    self.create_notification(
                        'low_balance', title, message, employee, 'high',
                        data={'balance': net_balance, 'threshold': threshold}
                    )
                    alerts_created += 1
        
        return alerts_created
    
    def check_vacation_expiry_alerts(self):
        """فحص التنبيهات لانتهاء الإجازات"""
        expiry_days = int(self.get_setting('vacation_expiry_days', '30'))
        cutoff_date = datetime.now() + timedelta(days=expiry_days)
        
        # الحصول على الإجازات التي ستنتهي قريباً
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT full_name, end_date, vacation_type, days_count
            FROM daily_requests 
            WHERE end_date <= ? AND end_date >= ?
            ORDER BY end_date
        ''', (cutoff_date.strftime('%Y-%m-%d'), datetime.now().strftime('%Y-%m-%d')))
        
        expiring_vacations = cursor.fetchall()
        conn.close()
        
        alerts_created = 0
        for vacation in expiring_vacations:
            employee, end_date, vacation_type, days_count = vacation
            
            if not self._has_recent_notification('vacation_expiry', employee, days=3):
                title = f"تنبيه: انتهاء إجازة - {employee}"
                message = f"ستنتهي إجازة {employee} ({vacation_type}) في {end_date}"
                
                self.create_notification(
                    'vacation_expiry', title, message, employee, 'medium',
                    data={'end_date': end_date, 'vacation_type': vacation_type, 'days_count': days_count}
                )
                alerts_created += 1
        
        return alerts_created
    
    def check_pending_requests_alerts(self):
        """فحص التنبيهات للطلبات المعلقة"""
        # هذه الوظيفة يمكن تطويرها عند إضافة نظام الموافقات
        # حالياً سنتحقق من الطلبات الحديثة التي تحتاج متابعة
        
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        # الطلبات اليومية الحديثة (آخر 7 أيام)
        cursor.execute('''
            SELECT COUNT(*) FROM daily_requests 
            WHERE created_at >= datetime('now', '-7 days')
        ''')
        recent_daily = cursor.fetchone()[0]
        
        # الطلبات الساعية الحديثة
        cursor.execute('''
            SELECT COUNT(*) FROM hourly_requests 
            WHERE created_at >= datetime('now', '-7 days')
        ''')
        recent_hourly = cursor.fetchone()[0]
        
        conn.close()
        
        if recent_daily > 0 or recent_hourly > 0:
            if not self._has_recent_notification('pending_requests', None, days=1):
                title = "تنبيه: طلبات جديدة تحتاج مراجعة"
                message = f"يوجد {recent_daily} طلب يومي و {recent_hourly} طلب ساعي جديد"
                
                self.create_notification(
                    'pending_requests', title, message, None, 'medium',
                    data={'daily_count': recent_daily, 'hourly_count': recent_hourly}
                )
                return 1
        
        return 0
    
    def generate_daily_summary(self):
        """إنشاء ملخص يومي"""
        today = datetime.now().strftime('%Y-%m-%d')
        
        # إحصائيات اليوم
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        # الطلبات اليومية اليوم
        cursor.execute('''
            SELECT COUNT(*) FROM daily_requests 
            WHERE DATE(created_at) = ?
        ''', (today,))
        daily_today = cursor.fetchone()[0]
        
        # الطلبات الساعية اليوم
        cursor.execute('''
            SELECT COUNT(*) FROM hourly_requests 
            WHERE DATE(created_at) = ?
        ''', (today,))
        hourly_today = cursor.fetchone()[0]
        
        # إجمالي الموظفين
        employees_count = len(self.db.get_all_employees())
        
        conn.close()
        
        # إنشاء الملخص
        title = f"ملخص يومي - {today}"
        message = f"""
        📊 ملخص نشاط اليوم:
        👥 إجمالي الموظفين: {employees_count}
        📅 طلبات يومية جديدة: {daily_today}
        ⏰ طلبات ساعية جديدة: {hourly_today}
        
        📈 الإحصائيات العامة:
        - إجمالي الطلبات اليوم: {daily_today + hourly_today}
        """
        
        self.create_notification(
            'daily_summary', title, message.strip(), None, 'low',
            data={
                'date': today,
                'daily_requests': daily_today,
                'hourly_requests': hourly_today,
                'total_employees': employees_count
            }
        )
    
    def _has_recent_notification(self, notification_type, employee_name, days=1):
        """التحقق من وجود إشعار مشابه حديث"""
        conn = sqlite3.connect(self.notifications_db)
        cursor = conn.cursor()
        
        cutoff_date = datetime.now() - timedelta(days=days)
        
        if employee_name:
            cursor.execute('''
                SELECT COUNT(*) FROM notifications 
                WHERE type = ? AND employee_name = ? AND created_at >= ?
            ''', (notification_type, employee_name, cutoff_date))
        else:
            cursor.execute('''
                SELECT COUNT(*) FROM notifications 
                WHERE type = ? AND created_at >= ?
            ''', (notification_type, cutoff_date))
        
        count = cursor.fetchone()[0]
        conn.close()
        
        return count > 0
    
    def run_all_checks(self):
        """تشغيل جميع فحوصات التنبيهات"""
        results = {
            'low_balance': self.check_low_balance_alerts(),
            'vacation_expiry': self.check_vacation_expiry_alerts(),
            'pending_requests': self.check_pending_requests_alerts()
        }
        
        # إنشاء الملخص اليومي إذا لم يتم إنشاؤه اليوم
        if not self._has_recent_notification('daily_summary', None, days=1):
            self.generate_daily_summary()
            results['daily_summary'] = 1
        else:
            results['daily_summary'] = 0
        
        return results
    
    def get_notification_stats(self):
        """الحصول على إحصائيات الإشعارات"""
        conn = sqlite3.connect(self.notifications_db)
        cursor = conn.cursor()
        
        # إحصائيات عامة
        cursor.execute('SELECT COUNT(*) FROM notifications')
        total_notifications = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM notifications WHERE status = "unread"')
        unread_notifications = cursor.fetchone()[0]
        
        # إحصائيات حسب النوع
        cursor.execute('''
            SELECT type, COUNT(*) 
            FROM notifications 
            GROUP BY type 
            ORDER BY COUNT(*) DESC
        ''')
        type_stats = cursor.fetchall()
        
        # إحصائيات حسب الأولوية
        cursor.execute('''
            SELECT priority, COUNT(*) 
            FROM notifications 
            WHERE status = "unread"
            GROUP BY priority
        ''')
        priority_stats = cursor.fetchall()
        
        conn.close()
        
        return {
            'total': total_notifications,
            'unread': unread_notifications,
            'by_type': type_stats,
            'by_priority': priority_stats
        }
    
    def cleanup_old_notifications(self, days=30):
        """تنظيف الإشعارات القديمة"""
        conn = sqlite3.connect(self.notifications_db)
        cursor = conn.cursor()
        
        cutoff_date = datetime.now() - timedelta(days=days)
        
        cursor.execute('''
            DELETE FROM notifications 
            WHERE status = "read" AND created_at < ?
        ''', (cutoff_date,))
        
        deleted_count = cursor.rowcount
        conn.commit()
        conn.close()
        
        return deleted_count

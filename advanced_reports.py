#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التقارير المتقدم لإدارة الإجازات
Advanced Reports System for Vacation Management
"""

import pandas as pd
from datetime import datetime, timedelta
import os
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import json
from database import VacationDatabase

class AdvancedReportsManager:
    def __init__(self, db_path='vacation_system.db'):
        self.db = VacationDatabase(db_path)
        self.setup_pdf_fonts()
    
    def setup_pdf_fonts(self):
        """إعداد الخطوط العربية للـ PDF"""
        try:
            # محاولة تسجيل خط عربي (يمكن تخصيصه حسب النظام)
            # pdfmetrics.registerFont(TTFont('Arabic', 'arial.ttf'))
            pass
        except:
            # في حالة عدم وجود خط عربي، سنستخدم الخط الافتراضي
            pass
    
    def generate_monthly_excel_report(self, year, month, output_path=None):
        """توليد تقرير شهري بصيغة Excel"""
        if not output_path:
            output_path = f'تقرير_شهري_{year}_{month:02d}.xlsx'
        
        # الحصول على بيانات التقرير
        report_data = self.db.get_monthly_report(year, month)
        
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # ورقة الإجازات اليومية
            if report_data['daily_requests']:
                daily_df = pd.DataFrame(report_data['daily_requests'], 
                                      columns=['الاسم', 'نوع الإجازة', 'تاريخ البداية', 'تاريخ النهاية', 'عدد الأيام'])
                daily_df.to_excel(writer, sheet_name='الإجازات اليومية', index=False)
            
            # ورقة الإجازات الساعية
            if report_data['hourly_requests']:
                hourly_df = pd.DataFrame(report_data['hourly_requests'],
                                       columns=['الاسم', 'تاريخ الاستخدام', 'عدد الساعات', 'المعادل بالأيام'])
                hourly_df.to_excel(writer, sheet_name='الإجازات الساعية', index=False)
            
            # ورقة الإحصائيات
            stats_data = {
                'نوع الإحصائية': ['إجمالي الطلبات اليومية', 'إجمالي الأيام اليومية', 
                                'إجمالي الطلبات الساعية', 'إجمالي الساعات', 'إجمالي الأيام الساعية'],
                'القيمة': [
                    report_data['daily_stats'][0],
                    report_data['daily_stats'][1],
                    report_data['hourly_stats'][0],
                    report_data['hourly_stats'][1],
                    report_data['hourly_stats'][2]
                ]
            }
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='الإحصائيات', index=False)
        
        return True, f"تم إنشاء التقرير: {output_path}"
    
    def generate_yearly_excel_report(self, year, output_path=None):
        """توليد تقرير سنوي بصيغة Excel"""
        if not output_path:
            output_path = f'تقرير_سنوي_{year}.xlsx'
        
        report_data = self.db.get_yearly_report(year)
        
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # الإحصائيات الشهرية للإجازات اليومية
            if report_data['monthly_daily_stats']:
                monthly_daily_df = pd.DataFrame(report_data['monthly_daily_stats'],
                                              columns=['الشهر', 'عدد الطلبات', 'إجمالي الأيام'])
                monthly_daily_df.to_excel(writer, sheet_name='إحصائيات شهرية يومية', index=False)
            
            # الإحصائيات الشهرية للإجازات الساعية
            if report_data['monthly_hourly_stats']:
                monthly_hourly_df = pd.DataFrame(report_data['monthly_hourly_stats'],
                                               columns=['الشهر', 'عدد الطلبات', 'إجمالي الساعات', 'إجمالي الأيام'])
                monthly_hourly_df.to_excel(writer, sheet_name='إحصائيات شهرية ساعية', index=False)
            
            # أكثر الموظفين استخداماً
            if report_data['top_employees_daily']:
                top_employees_df = pd.DataFrame(report_data['top_employees_daily'],
                                              columns=['الاسم', 'عدد الطلبات', 'إجمالي الأيام'])
                top_employees_df.to_excel(writer, sheet_name='أكثر الموظفين استخداماً', index=False)
            
            # الملخص السنوي
            summary_data = {
                'نوع الإحصائية': [
                    'إجمالي الطلبات اليومية',
                    'إجمالي الأيام اليومية', 
                    'متوسط الأيام لكل طلب يومي',
                    'إجمالي الطلبات الساعية',
                    'إجمالي الساعات',
                    'إجمالي الأيام الساعية',
                    'متوسط الساعات لكل طلب ساعي'
                ],
                'القيمة': [
                    report_data['yearly_daily_summary'][0] or 0,
                    report_data['yearly_daily_summary'][1] or 0,
                    round(report_data['yearly_daily_summary'][2] or 0, 2),
                    report_data['yearly_hourly_summary'][0] or 0,
                    report_data['yearly_hourly_summary'][1] or 0,
                    round(report_data['yearly_hourly_summary'][2] or 0, 2),
                    round(report_data['yearly_hourly_summary'][3] or 0, 2)
                ]
            }
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='الملخص السنوي', index=False)
        
        return True, f"تم إنشاء التقرير: {output_path}"
    
    def generate_employee_excel_report(self, full_name, start_date=None, end_date=None, output_path=None):
        """توليد تقرير مفصل للموظف بصيغة Excel"""
        if not output_path:
            safe_name = full_name.replace(' ', '_').replace('/', '_')
            period = f"_{start_date}_إلى_{end_date}" if start_date and end_date else ""
            output_path = f'تقرير_موظف_{safe_name}{period}.xlsx'
        
        report_data = self.db.get_employee_detailed_report(full_name, start_date, end_date)
        
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # معلومات الموظف والرصيد
            employee_info = {
                'البيان': [
                    'اسم الموظف',
                    'الرصيد الابتدائي',
                    'الإجازات المدرجة',
                    'الإجازات اليومية المستفادة',
                    'الإجازات الساعية المستفادة',
                    'الرصيد الصافي'
                ],
                'القيمة': [
                    report_data['employee_name'],
                    report_data['balance']['initial_balance'],
                    report_data['balance']['added_vacations'],
                    report_data['balance']['daily_used'],
                    round(report_data['balance']['hourly_used'], 2),
                    round(report_data['balance']['net_balance'], 2)
                ]
            }
            info_df = pd.DataFrame(employee_info)
            info_df.to_excel(writer, sheet_name='معلومات الموظف', index=False)
            
            # الإجازات اليومية
            if report_data['daily_requests']:
                daily_df = pd.DataFrame(report_data['daily_requests'],
                                      columns=['تاريخ البداية', 'تاريخ النهاية', 'نوع الإجازة', 'عدد الأيام', 'تاريخ الإنشاء'])
                daily_df.to_excel(writer, sheet_name='الإجازات اليومية', index=False)
            
            # الإجازات الساعية
            if report_data['hourly_requests']:
                hourly_df = pd.DataFrame(report_data['hourly_requests'],
                                       columns=['تاريخ الاستخدام', 'عدد الساعات', 'المعادل بالأيام', 'تاريخ الإنشاء'])
                hourly_df.to_excel(writer, sheet_name='الإجازات الساعية', index=False)
            
            # الإجازات المدرجة
            if report_data['added_vacations']:
                added_df = pd.DataFrame(report_data['added_vacations'],
                                      columns=['التاريخ', 'عدد الأيام', 'السبب', 'تاريخ الإنشاء'])
                added_df.to_excel(writer, sheet_name='الإجازات المدرجة', index=False)
        
        return True, f"تم إنشاء التقرير: {output_path}"
    
    def generate_custom_report(self, filters, output_path=None):
        """توليد تقرير مخصص حسب المرشحات المحددة"""
        if not output_path:
            output_path = f'تقرير_مخصص_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        
        conn = self.db.get_connection()
        
        # بناء الاستعلام حسب المرشحات
        queries = {}
        
        # مرشح الإجازات اليومية
        if filters.get('include_daily', True):
            daily_query = "SELECT * FROM daily_requests WHERE 1=1"
            daily_params = []
            
            if filters.get('start_date'):
                daily_query += " AND start_date >= ?"
                daily_params.append(filters['start_date'])
            
            if filters.get('end_date'):
                daily_query += " AND start_date <= ?"
                daily_params.append(filters['end_date'])
            
            if filters.get('employee_names'):
                placeholders = ','.join(['?' for _ in filters['employee_names']])
                daily_query += f" AND full_name IN ({placeholders})"
                daily_params.extend(filters['employee_names'])
            
            if filters.get('vacation_types'):
                placeholders = ','.join(['?' for _ in filters['vacation_types']])
                daily_query += f" AND vacation_type IN ({placeholders})"
                daily_params.extend(filters['vacation_types'])
            
            daily_query += " ORDER BY start_date DESC"
            queries['daily'] = (daily_query, daily_params)
        
        # مرشح الإجازات الساعية
        if filters.get('include_hourly', True):
            hourly_query = "SELECT * FROM hourly_requests WHERE 1=1"
            hourly_params = []
            
            if filters.get('start_date'):
                hourly_query += " AND usage_date >= ?"
                hourly_params.append(filters['start_date'])
            
            if filters.get('end_date'):
                hourly_query += " AND usage_date <= ?"
                hourly_params.append(filters['end_date'])
            
            if filters.get('employee_names'):
                placeholders = ','.join(['?' for _ in filters['employee_names']])
                hourly_query += f" AND full_name IN ({placeholders})"
                hourly_params.extend(filters['employee_names'])
            
            hourly_query += " ORDER BY usage_date DESC"
            queries['hourly'] = (hourly_query, hourly_params)
        
        # تنفيذ الاستعلامات وإنشاء التقرير
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            for query_type, (query, params) in queries.items():
                df = pd.read_sql_query(query, conn, params=params)
                
                if query_type == 'daily':
                    df.to_excel(writer, sheet_name='الإجازات اليومية', index=False)
                elif query_type == 'hourly':
                    df.to_excel(writer, sheet_name='الإجازات الساعية', index=False)
        
        conn.close()
        return True, f"تم إنشاء التقرير المخصص: {output_path}"
    
    def export_database_backup(self, output_path=None):
        """تصدير نسخة احتياطية من قاعدة البيانات"""
        if not output_path:
            output_path = f'نسخة_احتياطية_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        
        conn = self.db.get_connection()
        
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # تصدير جميع الجداول
            tables = [
                ('users', 'المستخدمين'),
                ('initial_balance', 'الرصيد الابتدائي'),
                ('daily_requests', 'الطلبات اليومية'),
                ('hourly_requests', 'الطلبات الساعية'),
                ('added_vacations', 'الإجازات المدرجة'),
                ('archived_data', 'البيانات المؤرشفة')
            ]
            
            for table_name, sheet_name in tables:
                try:
                    df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                except Exception as e:
                    print(f"خطأ في تصدير جدول {table_name}: {e}")
        
        conn.close()
        return True, f"تم إنشاء النسخة الاحتياطية: {output_path}"

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة قائمة الموظفين مع إمكانية التعديل
"""

import sys
import json
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class EmployeeManager:
    """مدير قائمة الموظفين"""
    
    def __init__(self):
        self.data_file = "employees_data.json"
        self.employees = self.load_employees()
    
    def load_employees(self):
        """تحميل قائمة الموظفين من الملف"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('employees', [])
            except:
                pass
        
        # قائمة افتراضية إذا لم يوجد الملف
        default_employees = [
            {"id": 1, "name": "أحمد محمد علي", "department": "الإدارة", "position": "مدير"},
            {"id": 2, "name": "فاطمة أحمد خالد", "department": "المحاسبة", "position": "محاسبة"},
            {"id": 3, "name": "محمد خالد عبدالله", "department": "الموارد البشرية", "position": "موظف"},
            {"id": 4, "name": "نور سالم محمد", "department": "التقنية", "position": "مطور"},
            {"id": 5, "name": "علي حسن أحمد", "department": "المبيعات", "position": "مندوب"},
            {"id": 6, "name": "مريم عبدالله سعد", "department": "التسويق", "position": "مسوقة"},
            {"id": 7, "name": "يوسف محمد علي", "department": "الصيانة", "position": "فني"},
            {"id": 8, "name": "هدى أحمد محمد", "department": "خدمة العملاء", "position": "موظفة"}
        ]
        self.save_employees(default_employees)
        return default_employees
    
    def save_employees(self, employees=None):
        """حفظ قائمة الموظفين في الملف"""
        if employees is None:
            employees = self.employees
        
        data = {
            "employees": employees,
            "last_updated": QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss")
        }
        
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في حفظ البيانات: {e}")
            return False
    
    def get_employee_names(self):
        """الحصول على أسماء الموظفين فقط"""
        return [emp["name"] for emp in self.employees]
    
    def get_employee_by_name(self, name):
        """الحصول على بيانات موظف بالاسم"""
        for emp in self.employees:
            if emp["name"] == name:
                return emp
        return None
    
    def add_employee(self, name, department="", position=""):
        """إضافة موظف جديد"""
        new_id = max([emp["id"] for emp in self.employees], default=0) + 1
        new_employee = {
            "id": new_id,
            "name": name,
            "department": department,
            "position": position
        }
        self.employees.append(new_employee)
        self.save_employees()
        return new_employee
    
    def update_employee(self, employee_id, name=None, department=None, position=None):
        """تحديث بيانات موظف"""
        for emp in self.employees:
            if emp["id"] == employee_id:
                if name is not None:
                    emp["name"] = name
                if department is not None:
                    emp["department"] = department
                if position is not None:
                    emp["position"] = position
                self.save_employees()
                return True
        return False
    
    def delete_employee(self, employee_id):
        """حذف موظف"""
        self.employees = [emp for emp in self.employees if emp["id"] != employee_id]
        self.save_employees()

class EmployeeManagementWindow(QDialog):
    """نافذة إدارة الموظفين"""
    
    def __init__(self):
        super().__init__()
        self.employee_manager = EmployeeManager()
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة إدارة الموظفين"""
        self.setWindowTitle("👥 إدارة قائمة الموظفين")
        self.setFixedSize(700, 500)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - 700) // 2
        y = (screen.height() - 500) // 2
        self.move(x, y)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # العنوان
        title_label = QLabel("👥 إدارة قائمة الموظفين")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 20px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2c3e50, stop: 1 #34495e);
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إطار المحتوى
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        content_layout = QVBoxLayout(content_frame)
        content_layout.setSpacing(10)
        
        # أزرار الإدارة
        buttons_layout = QHBoxLayout()
        
        add_button = QPushButton("➕ إضافة موظف")
        add_button.setStyleSheet(self.get_button_style("#27ae60"))
        add_button.clicked.connect(self.add_employee)
        
        edit_button = QPushButton("✏️ تعديل")
        edit_button.setStyleSheet(self.get_button_style("#3498db"))
        edit_button.clicked.connect(self.edit_employee)
        
        delete_button = QPushButton("🗑️ حذف")
        delete_button.setStyleSheet(self.get_button_style("#e74c3c"))
        delete_button.clicked.connect(self.delete_employee)
        
        refresh_button = QPushButton("🔄 تحديث")
        refresh_button.setStyleSheet(self.get_button_style("#f39c12"))
        refresh_button.clicked.connect(self.refresh_table)
        
        buttons_layout.addWidget(add_button)
        buttons_layout.addWidget(edit_button)
        buttons_layout.addWidget(delete_button)
        buttons_layout.addWidget(refresh_button)
        buttons_layout.addStretch()
        
        content_layout.addLayout(buttons_layout)
        
        # جدول الموظفين
        self.employees_table = QTableWidget()
        self.employees_table.setColumnCount(4)
        self.employees_table.setHorizontalHeaderLabels([
            "الرقم", "اسم الموظف", "القسم", "المنصب"
        ])
        
        # تعيين عرض الأعمدة
        header = self.employees_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.employees_table.setColumnWidth(0, 60)
        
        self.employees_table.setAlternatingRowColors(True)
        self.employees_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.employees_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                gridline-color: #dee2e6;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 13px;
            }
            QHeaderView::section {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 8px;
                font-weight: bold;
                color: #495057;
            }
            QTableWidget::item:selected {
                background: #007bff;
                color: white;
            }
        """)
        
        content_layout.addWidget(self.employees_table)
        main_layout.addWidget(content_frame)
        
        # أزرار التحكم السفلية
        bottom_buttons = QHBoxLayout()
        
        close_button = QPushButton("❌ إغلاق")
        close_button.setStyleSheet(self.get_button_style("#6c757d"))
        close_button.clicked.connect(self.accept)
        
        bottom_buttons.addStretch()
        bottom_buttons.addWidget(close_button)
        
        main_layout.addLayout(bottom_buttons)
        
        # تعبئة الجدول
        self.refresh_table()
        
        # الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
    
    def get_button_style(self, color):
        """إنشاء نمط للأزرار"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color}, stop: 1 rgba(0,0,0,0.1));
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px 15px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background: {color};
            }}
            QPushButton:pressed {{
                background: rgba(0,0,0,0.2);
            }}
        """
    
    def refresh_table(self):
        """تحديث جدول الموظفين"""
        self.employee_manager.employees = self.employee_manager.load_employees()
        employees = self.employee_manager.employees
        
        self.employees_table.setRowCount(len(employees))
        
        for row, emp in enumerate(employees):
            self.employees_table.setItem(row, 0, QTableWidgetItem(str(emp["id"])))
            self.employees_table.setItem(row, 1, QTableWidgetItem(emp["name"]))
            self.employees_table.setItem(row, 2, QTableWidgetItem(emp["department"]))
            self.employees_table.setItem(row, 3, QTableWidgetItem(emp["position"]))
    
    def add_employee(self):
        """إضافة موظف جديد"""
        dialog = EmployeeDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            name, department, position = dialog.get_data()
            if name.strip():
                self.employee_manager.add_employee(name, department, position)
                self.refresh_table()
                QMessageBox.information(self, "✅ تم الحفظ", f"تم إضافة الموظف: {name}")
            else:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الموظف!")
    
    def edit_employee(self):
        """تعديل موظف محدد"""
        current_row = self.employees_table.currentRow()
        if current_row >= 0:
            emp_id = int(self.employees_table.item(current_row, 0).text())
            employee = None
            for emp in self.employee_manager.employees:
                if emp["id"] == emp_id:
                    employee = emp
                    break
            
            if employee:
                dialog = EmployeeDialog(self, employee)
                if dialog.exec_() == QDialog.Accepted:
                    name, department, position = dialog.get_data()
                    if name.strip():
                        self.employee_manager.update_employee(emp_id, name, department, position)
                        self.refresh_table()
                        QMessageBox.information(self, "✅ تم التحديث", f"تم تحديث بيانات: {name}")
                    else:
                        QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الموظف!")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد موظف للتعديل!")
    
    def delete_employee(self):
        """حذف موظف محدد"""
        current_row = self.employees_table.currentRow()
        if current_row >= 0:
            emp_name = self.employees_table.item(current_row, 1).text()
            emp_id = int(self.employees_table.item(current_row, 0).text())
            
            reply = QMessageBox.question(self, "تأكيد الحذف", 
                                       f"هل تريد حذف الموظف: {emp_name}؟\n\nتحذير: سيتم حذف جميع طلبات الإجازات المرتبطة بهذا الموظف!")
            
            if reply == QMessageBox.Yes:
                self.employee_manager.delete_employee(emp_id)
                self.refresh_table()
                QMessageBox.information(self, "✅ تم الحذف", f"تم حذف الموظف: {emp_name}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد موظف للحذف!")

class EmployeeDialog(QDialog):
    """نافذة إضافة/تعديل موظف"""
    
    def __init__(self, parent=None, employee=None):
        super().__init__(parent)
        self.employee = employee
        self.setup_ui()
        
        if employee:
            self.load_employee_data()
    
    def setup_ui(self):
        """إعداد واجهة الحوار"""
        title = "✏️ تعديل موظف" if self.employee else "➕ إضافة موظف جديد"
        self.setWindowTitle(title)
        self.setFixedSize(400, 300)
        
        # توسيط النافذة
        if self.parent():
            parent_geo = self.parent().geometry()
            x = parent_geo.x() + (parent_geo.width() - 400) // 2
            y = parent_geo.y() + (parent_geo.height() - 300) // 2
            self.move(x, y)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #3498db, stop: 1 #2980b9);
                border-radius: 10px;
                padding: 15px;
            }
        """)
        layout.addWidget(title_label)
        
        # النموذج
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(10)
        
        # اسم الموظف
        self.create_form_row("👤 اسم الموظف:", form_layout)
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل الاسم الثلاثي...")
        self.apply_input_style(self.name_edit)
        form_layout.addWidget(self.name_edit)
        
        # القسم
        self.create_form_row("🏢 القسم:", form_layout)
        self.department_combo = QComboBox()
        self.department_combo.setEditable(True)
        self.department_combo.addItems([
            "الإدارة", "المحاسبة", "الموارد البشرية", "التقنية", 
            "المبيعات", "التسويق", "الصيانة", "خدمة العملاء"
        ])
        self.apply_input_style(self.department_combo)
        form_layout.addWidget(self.department_combo)
        
        # المنصب
        self.create_form_row("💼 المنصب:", form_layout)
        self.position_combo = QComboBox()
        self.position_combo.setEditable(True)
        self.position_combo.addItems([
            "مدير", "نائب مدير", "رئيس قسم", "موظف", "محاسب", 
            "مطور", "فني", "مندوب", "مسوق", "سكرتير"
        ])
        self.apply_input_style(self.position_combo)
        form_layout.addWidget(self.position_combo)
        
        layout.addWidget(form_frame)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        save_button = QPushButton("💾 حفظ")
        save_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #27ae60, stop: 1 #20c997);
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px 20px;
                min-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #34ce57, stop: 1 #27ae60);
            }
        """)
        save_button.clicked.connect(self.accept)
        
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #6c757d, stop: 1 #5a6268);
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px 20px;
                min-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #868e96, stop: 1 #6c757d);
            }
        """)
        cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)
        
        layout.addLayout(buttons_layout)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
    
    def create_form_row(self, text, layout):
        """إنشاء تسمية للنموذج"""
        label = QLabel(text)
        label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                margin-top: 5px;
            }
        """)
        layout.addWidget(label)
    
    def apply_input_style(self, widget):
        """تطبيق أنماط على عناصر الإدخال"""
        widget.setStyleSheet("""
            QLineEdit, QComboBox {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                background: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                color: #000000;
                min-height: 15px;
            }
            QLineEdit:focus, QComboBox:focus {
                border: 2px solid #007bff;
                background: white;
            }
        """)
    
    def load_employee_data(self):
        """تحميل بيانات الموظف للتعديل"""
        if self.employee:
            self.name_edit.setText(self.employee["name"])
            self.department_combo.setCurrentText(self.employee["department"])
            self.position_combo.setCurrentText(self.employee["position"])
    
    def get_data(self):
        """الحصول على البيانات المدخلة"""
        return (
            self.name_edit.text().strip(),
            self.department_combo.currentText().strip(),
            self.position_combo.currentText().strip()
        )

# إنشاء مدير الموظفين الافتراضي
_employee_manager = EmployeeManager()

def get_employee_manager():
    """الحصول على مدير الموظفين"""
    return _employee_manager

def get_employee_names():
    """الحصول على أسماء الموظفين للقوائم المنسدلة"""
    return _employee_manager.get_employee_names()

def show_employee_management():
    """عرض نافذة إدارة الموظفين"""
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    window = EmployeeManagementWindow()
    return window.exec_()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    font = QFont("Sakkal Majalla", 10)
    app.setFont(font)
    
    window = EmployeeManagementWindow()
    window.show()
    
    sys.exit(app.exec_())
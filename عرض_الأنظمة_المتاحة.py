#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
عرض جميع الأنظمة المتاحة للتشغيل
"""

import os
import sys
import subprocess
from datetime import datetime

def show_available_systems():
    """عرض جميع الأنظمة المتاحة"""
    
    print("=" * 80)
    print("🏢 نظام إدارة الإجازات المتكامل - الإصدار المتقدم النهائي")
    print("=" * 80)
    print()
    
    # معلومات النظام
    print("📊 معلومات النظام:")
    print(f"   📅 تاريخ التشغيل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   📁 مسار المشروع: {os.getcwd()}")
    print(f"   🐍 إصدار Python: {sys.version.split()[0]}")
    print()
    
    # قائمة الأنظمة المتاحة
    systems = [
        {
            "name": "🚀 المشغل الموحد الذكي",
            "file": "تشغيل_النظام_المتكامل.py",
            "description": "واجهة تحكم موحدة لجميع الأنظمة",
            "status": "متاح"
        },
        {
            "name": "🎛️ نظام الاختيار الذكي",
            "file": "اختيار_الواجهة.py",
            "description": "مقارنة ذكية بين الواجهات",
            "status": "متاح"
        },
        {
            "name": "✨ الواجهة الحديثة",
            "file": "تشغيل_الواجهة_الحديثة.py",
            "description": "واجهة عصرية مع تأثيرات بصرية",
            "status": "متاح"
        },
        {
            "name": "🔹 الواجهة التقليدية",
            "file": "run_app.py",
            "description": "واجهة مستقرة وموثوقة",
            "status": "متاح"
        },
        {
            "name": "🏠 الواجهة الرئيسية المتطورة",
            "file": "modern_main_window.py",
            "description": "واجهة رئيسية مع جميع الميزات",
            "status": "متاح"
        },
        {
            "name": "📊 نظام الإحصائيات المتقدم",
            "file": "modern_simple_analytics.py",
            "description": "تحليلات شاملة ورسوم بيانية",
            "status": "متاح"
        },
        {
            "name": "🗄️ مدير قاعدة البيانات",
            "file": "modern_database_manager.py",
            "description": "أدوات شاملة لإدارة البيانات",
            "status": "متاح"
        },
        {
            "name": "🧠 نظام الذكاء الاصطناعي",
            "file": "modern_ai_system.py",
            "description": "تحليل ذكي وتنبؤات متقدمة",
            "status": "متاح مع تحذيرات"
        },
        {
            "name": "👥 نظام إدارة المستخدمين",
            "file": "modern_user_management.py",
            "description": "إدارة شاملة للمستخدمين والأذونات",
            "status": "متاح"
        },
        {
            "name": "🔔 نظام الإشعارات",
            "file": "modern_notifications_backup.py",
            "description": "إشعارات ذكية ونسخ احتياطي",
            "status": "متاح"
        },
        {
            "name": "⚙️ الإعدادات المتقدمة",
            "file": "modern_search_settings.py",
            "description": "إعدادات شاملة وقابلة للتخصيص",
            "status": "متاح"
        },
        {
            "name": "🌐 واجهة الويب",
            "file": "modern_web_interface.py",
            "description": "تطبيق ويب حديث ومتجاوب",
            "status": "يتطلب Flask"
        },
        {
            "name": "🧪 الاختبارات التفاعلية",
            "file": "اختبار_تفاعلي.py",
            "description": "اختبارات شاملة للنظام",
            "status": "متاح"
        },
        {
            "name": "⚡ الاختبار السريع",
            "file": "اختبار_سريع.py",
            "description": "اختبار سريع للوظائف الأساسية",
            "status": "متاح"
        },
        {
            "name": "📋 الاختبار العملي الشامل",
            "file": "اختبار_عملي_شامل.py",
            "description": "اختبار عملي شامل للنظام",
            "status": "متاح"
        }
    ]
    
    print("📋 الأنظمة المتاحة للتشغيل:")
    print("-" * 80)
    
    for i, system in enumerate(systems, 1):
        status_icon = "✅" if system["status"] == "متاح" else "⚠️" if "تحذيرات" in system["status"] else "❌"
        print(f"{i:2d}. {system['name']}")
        print(f"    📁 الملف: {system['file']}")
        print(f"    📝 الوصف: {system['description']}")
        print(f"    {status_icon} الحالة: {system['status']}")
        print()
    
    print("=" * 80)
    print("🚀 طرق التشغيل:")
    print("   • للتشغيل المباشر: python اسم_الملف.py")
    print("   • للمشغل الموحد: python تشغيل_النظام_المتكامل.py")
    print("   • للاختيار الذكي: python اختيار_الواجهة.py")
    print()
    
    print("💡 التوصيات:")
    print("   🌟 للمستخدمين الجدد: ابدأ بـ 'نظام الاختيار الذكي'")
    print("   🎯 للاستخدام اليومي: استخدم 'الواجهة الحديثة'")
    print("   🔧 للمطورين: استخدم 'المشغل الموحد الذكي'")
    print("   📊 للتحليلات: استخدم 'نظام الإحصائيات المتقدم'")
    print()
    
    print("🔐 معلومات تسجيل الدخول الافتراضية:")
    print("   👤 اسم المستخدم: admin")
    print("   🔑 كلمة المرور: admin123")
    print()
    
    print("=" * 80)
    print("✨ النظام جاهز للاستخدام! اختر النظام المناسب لك وابدأ العمل.")
    print("=" * 80)

def check_system_requirements():
    """فحص متطلبات النظام"""
    
    print("\n🔍 فحص متطلبات النظام:")
    print("-" * 40)
    
    # فحص Python
    python_version = sys.version.split()[0]
    print(f"🐍 Python: {python_version} ✅")
    
    # فحص المكتبات المطلوبة
    required_modules = [
        ("PyQt5", "PyQt5.QtWidgets"),
        ("sqlite3", "sqlite3"),
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        ("matplotlib", "matplotlib"),
        ("flask", "flask")
    ]
    
    for module_name, import_name in required_modules:
        try:
            __import__(import_name)
            print(f"📦 {module_name}: متوفر ✅")
        except ImportError:
            status = "❌ غير متوفر"
            if module_name in ["flask"]:
                status += " (اختياري)"
            elif module_name in ["pandas", "numpy", "matplotlib"]:
                status += " (للإحصائيات)"
            print(f"📦 {module_name}: {status}")
    
    # فحص الملفات
    print(f"\n📁 فحص الملفات:")
    important_files = [
        "تشغيل_النظام_المتكامل.py",
        "تشغيل_الواجهة_الحديثة.py",
        "اختيار_الواجهة.py",
        "run_app.py",
        "modern_main_window.py"
    ]
    
    for file_name in important_files:
        if os.path.exists(file_name):
            print(f"📄 {file_name}: موجود ✅")
        else:
            print(f"📄 {file_name}: غير موجود ❌")
    
    print("-" * 40)

def run_system_demo():
    """تشغيل عرض توضيحي للنظام"""
    
    print("\n🎬 عرض توضيحي للنظام:")
    print("=" * 50)
    
    try:
        # تشغيل النظام الأساسي
        print("🚀 تشغيل النظام الأساسي...")
        subprocess.run([sys.executable, "run_app.py"], 
                      timeout=2, capture_output=True)
        print("✅ النظام الأساسي يعمل بشكل طبيعي")
        
    except subprocess.TimeoutExpired:
        print("✅ النظام الأساسي بدأ بنجاح (تم إيقاف العرض)")
    except FileNotFoundError:
        print("❌ ملف النظام الأساسي غير موجود")
    except Exception as e:
        print(f"⚠️ خطأ في تشغيل النظام: {str(e)}")
    
    print("=" * 50)

def main():
    """الدالة الرئيسية"""
    
    # عرض الأنظمة المتاحة
    show_available_systems()
    
    # فحص متطلبات النظام
    check_system_requirements()
    
    # عرض توضيحي
    run_system_demo()
    
    print("\n🎉 انتهى عرض الأنظمة المتاحة!")
    print("💡 يمكنك الآن تشغيل أي نظام من القائمة أعلاه.")
    
    # انتظار إدخال المستخدم
    input("\n⏸️ اضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
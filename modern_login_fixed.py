#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة تسجيل الدخول المحسّنة والخالية من الأخطاء
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime
import json

# استيراد الأنماط الحديثة
from modern_ui_styles import get_professional_login_stylesheet

class FixedLoginWindow(QDialog):
    """نافذة تسجيل دخول محسّنة وخالية من الأخطاء"""
    
    def __init__(self):
        super().__init__()
        self.user_data = None
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة تسجيل الدخول المحسّنة"""
        self.setWindowTitle("🔐 تسجيل الدخول - نظام إدارة الإجازات")
        self.setFixedSize(540, 680)
        
        # توسيط النافذة
        self.center_window()
        
        # تطبيق الأنماط المحسّنة
        self.setStyleSheet(get_professional_login_stylesheet())
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(40, 45, 40, 40)
        main_layout.setSpacing(30)
        
        # شريط العنوان المخصص
        self.create_custom_title_bar(main_layout)
        
        # إضافة مساحة فارغة
        main_layout.addSpacing(20)
        
        # شعار النظام
        self.create_logo_section(main_layout)
        
        # العنوان الرئيسي
        self.create_title_section(main_layout)
        
        # نموذج تسجيل الدخول
        self.create_login_form(main_layout)
        
        # أزرار التحكم
        self.create_button_section(main_layout)
        
        # معلومات إضافية
        self.create_info_section(main_layout)
        
        # إضافة مساحة فارغة في الأسفل
        main_layout.addStretch()
        
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        screen = QDesktopWidget().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
        
    def create_custom_title_bar(self, layout):
        """إنشاء شريط عنوان مخصص احترافي"""
        title_bar = QFrame()
        title_bar.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2c3e50, stop: 1 #34495e
                );
                border-radius: 15px;
                border: 2px solid #1a252f;
                padding: 12px;
                margin-bottom: 10px;
            }
        """)
        title_bar.setFixedHeight(65)
        
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(15, 10, 15, 10)
        
        # أيقونة النظام
        icon_label = QLabel("🔐")
        icon_label.setStyleSheet("""
            font-size: 26px;
            background: transparent;
            border: none;
            color: #3498db;
        """)
        title_layout.addWidget(icon_label)
        
        # عنوان النافذة
        window_title = QLabel("نظام إدارة الإجازات - تسجيل الدخول")
        window_title.setStyleSheet("""
            font-family: "Sakkal Majalla", "Arial", sans-serif;
            font-size: 18px;
            font-weight: bold;
            color: white;
            background: transparent;
            border: none;
            padding-left: 12px;
        """)
        title_layout.addWidget(window_title)
        
        title_layout.addStretch()
        
        # شارة الأمان
        security_badge = QLabel("🛡️ آمن")
        security_badge.setStyleSheet("""
            font-family: "Sakkal Majalla", "Arial", sans-serif;
            font-size: 13px;
            color: #2ecc71;
            background: rgba(46, 204, 113, 0.2);
            border: 1px solid #2ecc71;
            border-radius: 8px;
            padding: 6px 10px;
        """)
        title_layout.addWidget(security_badge)
        
        layout.addWidget(title_bar)
        
    def create_logo_section(self, layout):
        """إنشاء قسم الشعار المحسّن"""
        logo_frame = QFrame()
        logo_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #3498db, stop: 1 #2980b9
                );
                border-radius: 65px;
                border: 4px solid #34495e;
            }
        """)
        logo_frame.setFixedSize(130, 130)
        
        logo_layout = QVBoxLayout(logo_frame)
        logo_layout.setContentsMargins(0, 0, 0, 0)
        logo_layout.setSpacing(-5)
        
        # الأيقونة الرئيسية
        main_icon = QLabel("🏢")
        main_icon.setAlignment(Qt.AlignCenter)
        main_icon.setStyleSheet("""
            font-size: 52px;
            background: transparent;
            border: none;
            color: white;
        """)
        
        # نص فرعي
        sub_text = QLabel("HR System")
        sub_text.setAlignment(Qt.AlignCenter)
        sub_text.setStyleSheet("""
            font-family: "Sakkal Majalla", "Arial", sans-serif;
            font-size: 12px;
            font-weight: bold;
            background: transparent;
            border: none;
            color: #ecf0f1;
        """)
        
        logo_layout.addWidget(main_icon)
        logo_layout.addWidget(sub_text)
        
        # توسيط الشعار
        logo_container = QHBoxLayout()
        logo_container.addStretch()
        logo_container.addWidget(logo_frame)
        logo_container.addStretch()
        
        layout.addLayout(logo_container)
        
    def create_title_section(self, layout):
        """إنشاء قسم العنوان"""
        title_label = QLabel("نظام إدارة الإجازات")
        title_label.setObjectName("title_label")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        subtitle_label = QLabel("مرحباً بك! يرجى تسجيل الدخول للمتابعة")
        subtitle_label.setObjectName("subtitle_label")
        subtitle_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle_label)
        
    def create_login_form(self, layout):
        """إنشاء نموذج تسجيل الدخول المحسّن"""
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 rgba(255, 255, 255, 0.95), 
                    stop: 1 rgba(248, 249, 250, 0.95)
                );
                border-radius: 25px;
                border: 3px solid #34495e;
                padding: 30px;
                margin: 15px;
            }
        """)
        
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(25)
        form_layout.setContentsMargins(20, 20, 20, 20)
        
        # حقل اسم المستخدم
        username_label = QLabel("👤 اسم المستخدم:")
        username_label.setStyleSheet("""
            font-family: "Sakkal Majalla", "Arial", sans-serif;
            color: #2c3e50; 
            font-weight: bold; 
            background: transparent; 
            font-size: 18px;
            padding: 8px 0px;
            margin-bottom: 8px;
        """)
        form_layout.addWidget(username_label)
        
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم...")
        self.username_edit.setText("admin")
        self.username_edit.setMinimumHeight(60)
        self.username_edit.setMaximumHeight(70)
        form_layout.addWidget(self.username_edit)
        
        # إضافة مساحة
        form_layout.addSpacing(15)
        
        # حقل كلمة المرور
        password_label = QLabel("🔐 كلمة المرور:")
        password_label.setStyleSheet("""
            font-family: "Sakkal Majalla", "Arial", sans-serif;
            color: #2c3e50; 
            font-weight: bold; 
            background: transparent; 
            font-size: 18px;
            padding: 8px 0px;
            margin-bottom: 8px;
        """)
        form_layout.addWidget(password_label)
        
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("أدخل كلمة المرور...")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setText("admin123")
        self.password_edit.setMinimumHeight(60)
        self.password_edit.setMaximumHeight(70)
        self.password_edit.returnPressed.connect(self.handle_login)
        form_layout.addWidget(self.password_edit)
        
        # إضافة مساحة
        form_layout.addSpacing(20)
        
        # زر إظهار كلمة المرور
        show_password_layout = QHBoxLayout()
        self.show_password_checkbox = QCheckBox("👁️ إظهار كلمة المرور")
        self.show_password_checkbox.stateChanged.connect(self.toggle_password_visibility)
        self.show_password_checkbox.setStyleSheet("""
            QCheckBox {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                color: #2c3e50;
                background: transparent;
                font-size: 16px;
                font-weight: bold;
                padding: 8px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 2px solid #34495e;
                border-radius: 5px;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #e67e22;
                border: 2px solid #d35400;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #3498db;
            }
        """)
        show_password_layout.addWidget(self.show_password_checkbox)
        show_password_layout.addStretch()
        form_layout.addLayout(show_password_layout)
        
        # مربع تذكر كلمة المرور
        remember_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("💾 تذكر بياناتي")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                color: #2c3e50;
                background: transparent;
                font-size: 16px;
                font-weight: bold;
                padding: 8px;
            }
            QCheckBox::indicator {
                width: 22px;
                height: 22px;
                border: 2px solid #34495e;
                border-radius: 6px;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #2ecc71;
                border: 2px solid #27ae60;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #3498db;
            }
        """)
        remember_layout.addWidget(self.remember_checkbox)
        remember_layout.addStretch()
        form_layout.addLayout(remember_layout)
        
        layout.addWidget(form_frame)
        
    def create_button_section(self, layout):
        """إنشاء قسم الأزرار المحسّن"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("🚀 تسجيل الدخول")
        self.login_button.clicked.connect(self.handle_login)
        self.login_button.setDefault(True)
        self.login_button.setMinimumHeight(65)
        
        # زر المساعدة
        self.help_button = QPushButton("❓ مساعدة")
        self.help_button.clicked.connect(self.show_help)
        self.help_button.setMinimumHeight(65)
        self.help_button.setStyleSheet("""
            QPushButton {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f39c12, stop: 1 #e67e22
                );
                border: 3px solid #d68910;
                border-radius: 20px;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 18px;
                margin: 8px;
                min-height: 60px;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f5b041, stop: 1 #f39c12
                );
                border: 3px solid #f39c12;
            }
        """)
        
        # زر الإلغاء
        self.cancel_button = QPushButton("❌ إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setMinimumHeight(65)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e74c3c, stop: 1 #c0392b
                );
                border: 3px solid #a93226;
                border-radius: 20px;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 18px;
                margin: 8px;
                min-height: 60px;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ec7063, stop: 1 #e74c3c
                );
                border: 3px solid #e74c3c;
            }
        """)
        
        button_layout.addWidget(self.login_button)
        button_layout.addWidget(self.help_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
    def create_info_section(self, layout):
        """إنشاء قسم المعلومات المحسّن"""
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 rgba(52, 73, 94, 0.9), 
                    stop: 1 rgba(44, 62, 80, 0.9)
                );
                border-radius: 18px;
                border: 2px solid #34495e;
                padding: 20px;
                margin: 12px;
            }
        """)
        
        info_layout = QVBoxLayout(info_frame)
        
        info_label = QLabel("💡 بيانات تجريبية:\n👤 المستخدم: admin\n🔐 كلمة المرور: admin123")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            font-family: "Sakkal Majalla", "Arial", sans-serif;
            color: white;
            font-size: 16px;
            font-weight: bold;
            background: transparent;
            padding: 10px;
            line-height: 1.6;
        """)
        
        info_layout.addWidget(info_label)
        
        # إضافة شريط حالة
        status_bar = QLabel("🔄 جاهز لتسجيل الدخول")
        status_bar.setAlignment(Qt.AlignCenter)
        status_bar.setStyleSheet("""
            font-family: "Sakkal Majalla", "Arial", sans-serif;
            color: #2ecc71;
            font-size: 14px;
            font-weight: bold;
            background: rgba(46, 204, 113, 0.15);
            border: 1px solid rgba(46, 204, 113, 0.4);
            border-radius: 10px;
            padding: 8px;
            margin-top: 8px;
        """)
        info_layout.addWidget(status_bar)
        
        layout.addWidget(info_frame)
        
    def toggle_password_visibility(self):
        """تبديل رؤية كلمة المرور"""
        if self.show_password_checkbox.isChecked():
            self.password_edit.setEchoMode(QLineEdit.Normal)
        else:
            self.password_edit.setEchoMode(QLineEdit.Password)
            
    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        
        # التحقق من البيانات
        if username == "admin" and password == "admin123":
            self.user_data = {"username": username, "role": "admin"}
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ في تسجيل الدخول", 
                              "اسم المستخدم أو كلمة المرور غير صحيحة!")
            
    def show_help(self):
        """عرض نافذة المساعدة"""
        QMessageBox.information(self, "مساعدة تسجيل الدخول", 
                              """معلومات تسجيل الدخول:
                              
👤 اسم المستخدم: admin
🔐 كلمة المرور: admin123

في حالة نسيان كلمة المرور، يرجى التواصل مع مدير النظام.""")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setFont(QFont("Sakkal Majalla", 12))
    
    login_window = FixedLoginWindow()
    if login_window.exec_() == QDialog.Accepted:
        print("تم تسجيل الدخول بنجاح!")
    
    sys.exit(app.exec_())
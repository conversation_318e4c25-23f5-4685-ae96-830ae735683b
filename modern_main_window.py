#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
الواجهة الرئيسية الحديثة والعصرية لنظام إدارة الإجازات
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime
import json

# استيراد الأنماط الحديثة
from modern_ui_styles import get_modern_stylesheet, get_login_stylesheet, get_card_style, get_icon_button_style, get_professional_login_stylesheet

class ModernLoginWindow(QDialog):
    """نافذة تسجيل دخول حديثة وأنيقة"""
    
    def __init__(self):
        super().__init__()
        self.user_data = None
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة تسجيل الدخول"""
        self.setWindowTitle("🔐 تسجيل الدخول - نظام إدارة الإجازات")
        self.setFixedSize(540, 620)
        
        # جعل النافذة في الوسط
        self.center_window()
        
        # تطبيق الأنماط المحسّنة مع الخط
        self.setStyleSheet(get_professional_login_stylesheet())
        self.setFont(QFont("Sakkal Majalla", 12))
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(40, 45, 40, 40)
        main_layout.setSpacing(30)
        
        # شريط العنوان المخصص
        self.create_custom_title_bar(main_layout)
        
        # إضافة مساحة فارغة في الأعلى
        main_layout.addStretch(1)
        
        # العنوان الرئيسي (بدون شعار)
        self.create_title_section(main_layout)
        
        # نموذج تسجيل الدخول (بدون شعار)
        self.create_login_form(main_layout)
        
        # أزرار التحكم
        self.create_button_section(main_layout)
        
        # معلومات إضافية
        self.create_info_section(main_layout)
        
        # إضافة مساحة فارغة في الأسفل
        main_layout.addStretch()
        
        # تمكين السحب
        self.old_pos = self.pos()
        
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        screen = QDesktopWidget().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
        
    def create_custom_title_bar(self, layout):
        """إنشاء شريط عنوان مخصص احترافي"""
        title_bar = QFrame()
        title_bar.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2c3e50, stop: 1 #34495e
                );
                border-radius: 15px;
                border: 2px solid #1a252f;
                padding: 12px;
                margin-bottom: 10px;
            }
        """)
        title_bar.setFixedHeight(60)
        
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(15, 8, 15, 8)
        
        # أيقونة النظام
        icon_label = QLabel("🔐")
        icon_label.setStyleSheet("""
            font-size: 24px;
            background: transparent;
            border: none;
            color: #3498db;
        """)
        title_layout.addWidget(icon_label)
        
        # عنوان النافذة
        window_title = QLabel("نظام إدارة الإجازات - تسجيل الدخول")
        window_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
            background: transparent;
            border: none;
            padding-left: 10px;
        """)
        title_layout.addWidget(window_title)
        
        title_layout.addStretch()
        
        # شارة الأمان
        security_badge = QLabel("🛡️ آمن")
        security_badge.setStyleSheet("""
            font-size: 12px;
            color: #2ecc71;
            background: rgba(46, 204, 113, 0.2);
            border: 1px solid #2ecc71;
            border-radius: 8px;
            padding: 4px 8px;
        """)
        title_layout.addWidget(security_badge)
        
        layout.addWidget(title_bar)
        
    def create_logo_section(self, layout):
        """إنشاء قسم الشعار"""
        logo_frame = QFrame()
        logo_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #3498db, stop: 1 #2980b9
                );
                border-radius: 60px;
                border: 4px solid #34495e;
            }
        """)
        logo_frame.setFixedSize(120, 120)
        
        logo_layout = QVBoxLayout(logo_frame)
        logo_layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء تخطيط متعدد الطبقات للشعار
        logo_container = QWidget()
        logo_container.setFixedSize(120, 120)
        logo_container_layout = QVBoxLayout(logo_container)
        logo_container_layout.setContentsMargins(0, 0, 0, 0)
        logo_container_layout.setSpacing(0)
        
        # الأيقونة الرئيسية
        main_icon = QLabel("🏢")
        main_icon.setAlignment(Qt.AlignCenter)
        main_icon.setStyleSheet("""
            font-size: 48px;
            background: transparent;
            border: none;
            color: white;
        """)
        
        # نص فرعي
        sub_text = QLabel("HR")
        sub_text.setAlignment(Qt.AlignCenter)
        sub_text.setStyleSheet("""
            font-family: "Sakkal Majalla", "Arial", sans-serif;
            font-size: 14px;
            font-weight: bold;
            background: transparent;
            border: none;
            color: #ecf0f1;
            margin-top: -5px;
        """)
        title_layout.addWidget(icon_label)
        
        # عنوان النافذة
        window_title = QLabel("نظام إدارة الإجازات - تسجيل الدخول")
        window_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
            background: transparent;
            border: none;
            padding-left: 10px;
        """)
        title_layout.addWidget(window_title)
        
        title_layout.addStretch()
        
        # شارة الأمان
        security_badge = QLabel("🛡️ آمن")
        security_badge.setStyleSheet("""
            font-size: 12px;
            color: #2ecc71;
            background: rgba(46, 204, 113, 0.2);
            border: 1px solid #2ecc71;
            border-radius: 8px;
            padding: 4px 8px;
        """)
        title_layout.addWidget(security_badge)
        
        layout.addWidget(title_bar)
        
    def create_logo_section(self, layout):
        """إنشاء قسم الشعار"""
        logo_frame = QFrame()
        logo_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #3498db, stop: 1 #2980b9
                );
                border-radius: 60px;
                border: 4px solid #34495e;
            }
        """)
        logo_frame.setFixedSize(120, 120)
        
        logo_layout = QVBoxLayout(logo_frame)
        logo_layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء تخطيط متعدد الطبقات للشعار
        logo_container = QWidget()
        logo_container.setFixedSize(120, 120)
        logo_container_layout = QVBoxLayout(logo_container)
        logo_container_layout.setContentsMargins(0, 0, 0, 0)
        logo_container_layout.setSpacing(0)
        
        # الأيقونة الرئيسية
        main_icon = QLabel("🏢")
        main_icon.setAlignment(Qt.AlignCenter)
        main_icon.setStyleSheet("""
            font-size: 48px;
            background: transparent;
            border: none;
            color: white;
        """)
        
        # نص فرعي
        sub_text = QLabel("HR")
        sub_text.setAlignment(Qt.AlignCenter)
        sub_text.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            background: transparent;
            border: none;
            color: #ecf0f1;
            margin-top: -5px;
        """)
        
        logo_container_layout.addWidget(main_icon)
        logo_container_layout.addWidget(sub_text)
        
        logo_layout.addWidget(logo_container)
        
        # توسيط الشعار
        logo_container = QHBoxLayout()
        logo_container.addStretch()
        logo_container.addWidget(logo_frame)
        logo_container.addStretch()
        
        layout.addLayout(logo_container)
        
    def create_title_section(self, layout):
        """إنشاء قسم العنوان"""
        title_label = QLabel("نظام إدارة الإجازات")
        title_label.setObjectName("title_label")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        subtitle_label = QLabel("مرحباً بك! يرجى تسجيل الدخول للمتابعة")
        subtitle_label.setObjectName("subtitle_label")
        subtitle_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle_label)
        
    def create_login_form(self, layout):
        """إنشاء نموذج تسجيل الدخول"""
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 rgba(255, 255, 255, 0.95), 
                    stop: 1 rgba(248, 249, 250, 0.95)
                );
                border-radius: 25px;
                border: 3px solid #34495e;
                padding: 25px;
                margin: 10px;
            }
        """)
        
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(20)
        form_layout.setContentsMargins(15, 15, 15, 15)
        
        # حقل اسم المستخدم
        username_label = QLabel("👤 اسم المستخدم:")
        username_label.setStyleSheet("""
            font-family: "Sakkal Majalla", "Arial", sans-serif;
            color: #2c3e50; 
            font-weight: bold; 
            background: transparent; 
            font-size: 16px;
            padding: 8px 0px;
            margin-bottom: 5px;
        """)
        form_layout.addWidget(username_label)
        
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم...")
        self.username_edit.setText("admin")
        self.username_edit.setMinimumHeight(60)
        self.username_edit.setMaximumHeight(70)
        # تطبيق أنماط واضحة لضمان الرؤية
        self.username_edit.setStyleSheet("""
            QLineEdit {
                background: white !important;
                border: 3px solid #34495e !important;
                border-radius: 15px !important;
                padding: 15px !important;
                font-size: 16px !important;
                font-weight: bold !important;
                color: #000000 !important;
                font-family: "Sakkal Majalla", "Arial", sans-serif !important;
            }
            QLineEdit:focus {
                border: 3px solid #e67e22 !important;
                background: #ffffff !important;
            }
        """)
        # تأكد من ظهور المربع
        self.username_edit.show()
        self.username_edit.setVisible(True)
        form_layout.addWidget(self.username_edit)
        # إضافة مساحة بعد الحقل
        form_layout.addSpacing(10)
        
        # حقل كلمة المرور
        password_label = QLabel("🔐 كلمة المرور:")
        password_label.setStyleSheet("""
            font-family: "Sakkal Majalla", "Arial", sans-serif;
            color: #2c3e50; 
            font-weight: bold; 
            background: transparent; 
            font-size: 16px;
            padding: 8px 0px;
            margin-bottom: 5px;
        """)
        form_layout.addWidget(password_label)
        
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("أدخل كلمة المرور...")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setText("admin123")
        self.password_edit.setMinimumHeight(60)
        self.password_edit.setMaximumHeight(70)
        # تطبيق أنماط واضحة لضمان الرؤية
        self.password_edit.setStyleSheet("""
            QLineEdit {
                background: white !important;
                border: 3px solid #34495e !important;
                border-radius: 15px !important;
                padding: 15px !important;
                font-size: 16px !important;
                font-weight: bold !important;
                color: #000000 !important;
                font-family: "Sakkal Majalla", "Arial", sans-serif !important;
            }
            QLineEdit:focus {
                border: 3px solid #e67e22 !important;
                background: #ffffff !important;
            }
        """)
        self.password_edit.returnPressed.connect(self.handle_login)  # تسجيل الدخول بالضغط على Enter
        # تأكد من ظهور المربع
        self.password_edit.show()
        self.password_edit.setVisible(True)
        form_layout.addWidget(self.password_edit)
        # إضافة مساحة بعد حقل كلمة المرور
        form_layout.addSpacing(15)
        
        # زر إظهار كلمة المرور
        show_password_layout = QHBoxLayout()
        self.show_password_checkbox = QCheckBox("👁️ إظهار كلمة المرور")
        self.show_password_checkbox.stateChanged.connect(self.toggle_password_visibility)
        self.show_password_checkbox.setStyleSheet("""
            QCheckBox {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                color: #2c3e50;
                background: transparent;
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #34495e;
                border-radius: 5px;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #e67e22;
                border: 2px solid #d35400;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #3498db;
            }
        """)
        show_password_layout.addWidget(self.show_password_checkbox)
        show_password_layout.addStretch()
        form_layout.addLayout(show_password_layout)
        # إضافة مساحة بعد مربع إظهار كلمة المرور
        form_layout.addSpacing(10)
        
        # مربع تذكر كلمة المرور
        remember_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("تذكر بياناتي")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                color: #2c3e50;
                background: transparent;
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 2px solid #34495e;
                border-radius: 6px;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #2ecc71;
                border: 2px solid #27ae60;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #3498db;
            }
        """)
        remember_layout.addWidget(self.remember_checkbox)
        remember_layout.addStretch()
        
        form_layout.addLayout(remember_layout)
        
        # تأكد من ظهور الإطار
        form_frame.show()
        form_frame.setVisible(True)
        layout.addWidget(form_frame)
        
    def create_button_section(self, layout):
        """إنشاء قسم الأزرار"""
        button_layout = QHBoxLayout()
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("🚀 تسجيل الدخول")
        self.login_button.clicked.connect(self.handle_login)
        self.login_button.setDefault(True)
        
        # زر المساعدة
        self.help_button = QPushButton("❓ مساعدة")
        self.help_button.clicked.connect(self.show_help)
        self.help_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f39c12, stop: 1 #e67e22
                );
                border: 3px solid #d68910;
                border-radius: 20px;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
                margin: 8px;
                min-height: 50px;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f5b041, stop: 1 #f39c12
                );
                border: 3px solid #f39c12;
            }
        """)
        
        # زر الإلغاء
        self.cancel_button = QPushButton("❌ إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setObjectName("cancel_button")
        self.cancel_button.setStyleSheet("""
            QPushButton#cancel_button {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e74c3c, stop: 1 #c0392b
                );
                border: 3px solid #a93226;
                border-radius: 20px;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
                margin: 8px;
                min-height: 50px;
            }
            QPushButton#cancel_button:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ec7063, stop: 1 #e74c3c
                );
                border: 3px solid #e74c3c;
            }
        """)
        
        button_layout.addWidget(self.login_button)
        button_layout.addWidget(self.help_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # ربط Enter بتسجيل الدخول
        self.password_edit.returnPressed.connect(self.handle_login)
        
    def create_info_section(self, layout):
        """إنشاء قسم المعلومات"""
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 rgba(52, 73, 94, 0.9), 
                    stop: 1 rgba(44, 62, 80, 0.9)
                );
                border-radius: 15px;
                border: 2px solid #34495e;
                padding: 15px;
                margin: 8px;
            }
        """)
        
        info_layout = QVBoxLayout(info_frame)
        
        info_label = QLabel("💡 بيانات تجريبية:\n👤 المستخدم: admin\n🔐 كلمة المرور: admin123")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            color: white;
            font-size: 14px;
            font-weight: bold;
            background: transparent;
            padding: 8px;
        """)
        
        info_layout.addWidget(info_label)
        
        # إضافة شريط حالة
        status_bar = QLabel("🔄 جاهز لتسجيل الدخول")
        status_bar.setAlignment(Qt.AlignCenter)
        status_bar.setStyleSheet("""
            color: #2ecc71;
            font-size: 12px;
            font-weight: bold;
            background: rgba(46, 204, 113, 0.1);
            border: 1px solid rgba(46, 204, 113, 0.3);
            border-radius: 8px;
            padding: 6px;
            margin-top: 5px;
        """)
        info_layout.addWidget(status_bar)
        
        layout.addWidget(info_frame)
        
    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        
        if not username or not password:
            self.show_message("⚠️ تحذير", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
            
        # محاكاة التحقق من البيانات
        if username == "admin" and password == "admin123":
            self.user_data = {
                'username': username,
                'role': 'admin',
                'login_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # حفظ البيانات إذا تم تحديد تذكر
            if self.remember_checkbox.isChecked():
                self.save_credentials(username)
                
            self.accept()
        else:
            self.show_message("❌ خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            
    def show_help(self):
        """عرض مساعدة تسجيل الدخول"""
        help_text = """
🔐 معلومات تسجيل الدخول:

👤 اسم المستخدم: admin
🔑 كلمة المرور: admin123

💡 نصائح:
• تأكد من كتابة البيانات بشكل صحيح
• لا تستخدم مسافات إضافية
• كلمة المرور حساسة لحالة الأحرف

📞 للمساعدة التقنية:
اتصل بقسم تقنية المعلومات
        """
        
        msg = QMessageBox()
        msg.setWindowTitle("🛟 مساعدة تسجيل الدخول")
        msg.setText(help_text)
        msg.setStyleSheet("""
            QMessageBox {
                background: white;
                border-radius: 10px;
                min-width: 400px;
            }
            QMessageBox QPushButton {
                background: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 80px;
            }
        """)
        msg.exec_()
    
    def toggle_password_visibility(self):
        """تبديل إظهار كلمة المرور"""
        if self.show_password_checkbox.isChecked():
            self.password_edit.setEchoMode(QLineEdit.Normal)
        else:
            self.password_edit.setEchoMode(QLineEdit.Password)
            
    def show_message(self, title, message):
        """عرض رسالة"""
        msg = QMessageBox()
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setStyleSheet("""
            QMessageBox {
                background: white;
                border-radius: 10px;
            }
            QMessageBox QPushButton {
                background: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 80px;
            }
        """)
        msg.exec_()
        
    def save_credentials(self, username):
        """حفظ بيانات المستخدم"""
        try:
            data = {'username': username}
            with open('user_settings.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except:
            pass
            
    def load_credentials(self):
        """تحميل بيانات المستخدم المحفوظة"""
        try:
            with open('user_settings.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.username_edit.setText(data.get('username', ''))
                self.remember_checkbox.setChecked(True)
        except:
            pass
            
    def mousePressEvent(self, event):
        """بداية السحب"""
        self.old_pos = event.globalPos()
        
    def mouseMoveEvent(self, event):
        """سحب النافذة"""
        if event.buttons() == Qt.LeftButton:
            delta = QPoint(event.globalPos() - self.old_pos)
            self.move(self.x() + delta.x(), self.y() + delta.y())
            self.old_pos = event.globalPos()

class ModernMainWindow(QMainWindow):
    """النافذة الرئيسية الحديثة"""
    
    def __init__(self):
        super().__init__()
        self.user_data = None
        self.current_theme = "modern"
        self.init_ui()
        
    def init_ui(self):
        """إعداد الواجهة الرئيسية"""
        self.setWindowTitle("🏢 نظام إدارة الإجازات - الإصدار المحدث")
        self.setGeometry(100, 100, 900, 600)
        self.setMinimumSize(800, 500)
        
        # تطبيق الأنماط الحديثة
        self.setStyleSheet(get_modern_stylesheet())
        
        # تسجيل الدخول أولاً
        if not self.show_login():
            sys.exit()
            
        # إعداد النافذة الرئيسية
        self.setup_main_window()
        
        # إظهار النافذة
        self.show()
        self.raise_()
        self.activateWindow()
        
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        login_window = ModernLoginWindow()
        login_window.load_credentials()
        
        # توسيط النافذة
        desktop = QDesktopWidget()
        x = (desktop.width() - login_window.width()) // 2
        y = (desktop.height() - login_window.height()) // 2
        login_window.move(x, y)
        
        if login_window.exec_() == QDialog.Accepted:
            self.user_data = login_window.user_data
            return True
        return False
        
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        # إنشاء قائمة الطعام
        self.create_menu_bar()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
        # إنشاء المحتوى الرئيسي
        self.create_central_widget()
        
        # عرض رسالة ترحيب
        self.show_welcome_message()
        
    def create_menu_bar(self):
        """إنشاء قائمة الطعام"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu('📁 ملف')
        
        # استيراد البيانات
        import_action = QAction('📥 استيراد من Excel', self)
        import_action.setShortcut('Ctrl+I')
        import_action.triggered.connect(self.import_data)
        file_menu.addAction(import_action)
        
        # تصدير البيانات
        export_action = QAction('📤 تصدير البيانات', self)
        export_action.setShortcut('Ctrl+E')
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # خروج
        exit_action = QAction('🚪 خروج', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة الإعدادات
        settings_menu = menubar.addMenu('⚙️ إعدادات')
        
        # تغيير كلمة المرور
        change_password_action = QAction('🔐 تغيير كلمة المرور', self)
        settings_menu.addAction(change_password_action)
        
        # إعدادات النظام
        system_settings_action = QAction('🛠️ إعدادات النظام', self)
        settings_menu.addAction(system_settings_action)
        
        # قائمة النظام المتقدم
        advanced_menu = menubar.addMenu('🔧 النظام المتقدم')
        
        # إدارة الإشعارات
        notifications_action = QAction('🔔 إدارة الإشعارات', self)
        notifications_action.triggered.connect(self.show_notifications)
        advanced_menu.addAction(notifications_action)
        
        # إدارة النسخ الاحتياطي
        backup_action = QAction('💾 النسخ الاحتياطي', self)
        backup_action.triggered.connect(self.show_backup)
        advanced_menu.addAction(backup_action)
        
        # فاصل
        advanced_menu.addSeparator()
        
        # لوحة الإحصائيات المتقدمة
        analytics_action = QAction('📊 لوحة الإحصائيات المتقدمة', self)
        analytics_action.triggered.connect(self.show_analytics_dashboard)
        advanced_menu.addAction(analytics_action)
        
        # إدارة قاعدة البيانات
        database_action = QAction('🗄️ إدارة قاعدة البيانات', self)
        database_action.triggered.connect(self.show_database_manager)
        advanced_menu.addAction(database_action)
        
        # الذكاء الاصطناعي
        ai_action = QAction('🧠 الذكاء الاصطناعي', self)
        ai_action.triggered.connect(self.show_ai_system)
        advanced_menu.addAction(ai_action)
        
        # إدارة المستخدمين
        users_action = QAction('👥 إدارة المستخدمين', self)
        users_action.triggered.connect(self.show_user_management)
        advanced_menu.addAction(users_action)
        
        # واجهة الويب
        web_action = QAction('🌐 واجهة الويب', self)
        web_action.triggered.connect(self.show_web_interface)
        advanced_menu.addAction(web_action)
        
        # صيانة النظام
        maintenance_action = QAction('🔧 صيانة النظام', self)
        maintenance_action.triggered.connect(self.show_maintenance)
        advanced_menu.addAction(maintenance_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu('❓ مساعدة')
        
        # دليل المستخدم
        user_guide_action = QAction('📖 دليل المستخدم', self)
        help_menu.addAction(user_guide_action)
        
        # حول البرنامج
        about_action = QAction('ℹ️ حول البرنامج', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        statusbar = self.statusBar()
        
        # معلومات المستخدم
        user_label = QLabel(f"👤 المستخدم: {self.user_data['username']} | 🕐 {self.user_data['login_time']}")
        statusbar.addWidget(user_label)
        
        # مساحة فارغة
        statusbar.addPermanentWidget(QLabel(""), 1)
        
        # حالة الاتصال
        connection_label = QLabel("🟢 متصل")
        statusbar.addPermanentWidget(connection_label)
        
        # إصدار البرنامج
        version_label = QLabel("📱 الإصدار 2.0")
        statusbar.addPermanentWidget(version_label)
        
    def create_central_widget(self):
        """إنشاء المحتوى الرئيسي"""
        central_widget = QWidget()
        central_widget.setObjectName("central_widget")
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # العنوان الرئيسي
        self.create_header_section(main_layout)
        
        # قسم المعلومات السريعة
        self.create_info_cards_section(main_layout)
        
        # قسم الأزرار الرئيسية
        self.create_main_buttons_section(main_layout)
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_layout = QVBoxLayout(header_frame)
        
        # العنوان الرئيسي
        title_label = QLabel("🏢 نظام إدارة الإجازات المحدث")
        title_label.setObjectName("title_label")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)
        
        # رسالة الترحيب
        welcome_label = QLabel(f"مرحباً {self.user_data['username']}! اختر العملية التي تريد تنفيذها")
        welcome_label.setObjectName("welcome_label")
        welcome_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(welcome_label)
        
        layout.addWidget(header_frame)
        
    def create_info_cards_section(self, layout):
        """إنشاء قسم بطاقات المعلومات"""
        cards_frame = QFrame()
        cards_layout = QHBoxLayout(cards_frame)
        cards_layout.setSpacing(15)
        
        # بطاقة الموظفين
        employees_card = self.create_info_card("👥", "الموظفين", "50", "#3498db")
        cards_layout.addWidget(employees_card)
        
        # بطاقة الطلبات
        requests_card = self.create_info_card("📝", "الطلبات", "125", "#e74c3c")
        cards_layout.addWidget(requests_card)
        
        # بطاقة الأرصدة
        balance_card = self.create_info_card("💰", "إجمالي الأرصدة", "2,450", "#27ae60")
        cards_layout.addWidget(balance_card)
        
        # بطاقة التقارير
        reports_card = self.create_info_card("📊", "التقارير", "28", "#f39c12")
        cards_layout.addWidget(reports_card)
        
        layout.addWidget(cards_frame)
        
    def create_info_card(self, icon, title, value, color):
        """إنشاء بطاقة معلومات"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 {color}, stop: 1 {color}dd
                );
                border-radius: 15px;
                border: none;
                padding: 20px;
                min-height: 120px;
            }}
            QFrame:hover {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 {color}ee, stop: 1 {color}
                );
            }}
        """)
        
        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(10)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            font-size: 42px;
            color: white;
            background: transparent;
        """)
        card_layout.addWidget(icon_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("""
            font-size: 28px;
            font-weight: bold;
            color: white;
            background: transparent;
        """)
        card_layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            background: transparent;
        """)
        card_layout.addWidget(title_label)
        
        return card
        
    def create_main_buttons_section(self, layout):
        """إنشاء قسم الأزرار الرئيسية"""
        buttons_frame = QFrame()
        buttons_layout = QGridLayout(buttons_frame)
        buttons_layout.setSpacing(15)
        
        # تعريف الأزرار
        buttons_data = [
            ("📥", "استيراد رصيد", "استيراد الرصيد الابتدائي من Excel", "import_button", self.import_data),
            ("📝", "طلب إجازة يومية", "إضافة طلب إجازة يومية جديد", "daily_button", self.daily_request),
            ("⏱️", "طلب إجازة ساعية", "إضافة طلب إجازة ساعية", "hourly_button", self.hourly_request),
            ("➕", "إدراج إجازات", "إضافة أيام إجازة للرصيد", "add_button", self.add_vacation),
            ("📊", "التقارير", "عرض تقارير الأرصدة", "report_button", self.show_reports),
            ("🔍", "استعلام", "البحث عن موظف", "search_button", self.search_employee),
            ("✏️", "تعديل الطلبات", "تعديل الطلبات الموجودة", "edit_button", self.edit_requests),
            ("🗑️", "حذف الطلبات", "حذف الطلبات المحددة", "delete_button", self.delete_requests),
            ("⚙️", "الإعدادات", "إعدادات النظام", "settings_button", self.show_settings),
        ]
        
        # إضافة الأزرار في شبكة 3x3
        for i, (icon, title, description, object_name, callback) in enumerate(buttons_data):
            row = i // 3
            col = i % 3
            
            button = self.create_modern_button(icon, title, description, object_name, callback)
            buttons_layout.addWidget(button, row, col)
            
        layout.addWidget(buttons_frame)
        
    def create_modern_button(self, icon, title, description, object_name, callback):
        """إنشاء زر حديث"""
        button_frame = QFrame()
        button_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 15px;
                border: 2px solid #e0e0e0;
                padding: 20px;
                min-height: 120px;
            }
            QFrame:hover {
                border: 2px solid #3498db;
                background: #f8f9fa;
            }
        """)
        
        button_layout = QVBoxLayout(button_frame)
        button_layout.setSpacing(10)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            font-size: 52px;
            background: transparent;
            border: none;
        """)
        button_layout.addWidget(icon_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            background: transparent;
            border: none;
        """)
        button_layout.addWidget(title_label)
        
        # الوصف
        desc_label = QLabel(description)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("""
            font-size: 14px;
            color: #7f8c8d;
            background: transparent;
            border: none;
        """)
        button_layout.addWidget(desc_label)
        
        # جعل الإطار قابل للنقر
        button_frame.mousePressEvent = lambda event: callback()
        button_frame.setCursor(Qt.PointingHandCursor)
        
        return button_frame
        
    def show_welcome_message(self):
        """عرض رسالة ترحيب"""
        msg = QMessageBox()
        msg.setWindowTitle("🎉 مرحباً!")
        msg.setText(f"مرحباً بك {self.user_data['username']} في النظام المحدث!\n\n"
                   "🆕 الميزات الجديدة:\n"
                   "• واجهة حديثة وعصرية\n"
                   "• بطاقات معلومات تفاعلية\n"
                   "• تصميم محسن للأزرار\n"
                   "• قوائم طعام شاملة\n"
                   "• شريط حالة معلوماتي\n\n"
                   "استمتع بالتجربة الجديدة! 🚀")
        
        msg.setStyleSheet("""
            QMessageBox {
                background: white;
                border-radius: 15px;
                padding: 20px;
            }
            QMessageBox QLabel {
                color: #2c3e50;
                font-size: 14px;
                min-width: 400px;
            }
            QMessageBox QPushButton {
                background: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QMessageBox QPushButton:hover {
                background: #2980b9;
            }
        """)
        
        msg.exec_()
        
    def show_about(self):
        """عرض معلومات البرنامج"""
        msg = QMessageBox()
        msg.setWindowTitle("ℹ️ حول البرنامج")
        msg.setText("🏢 نظام إدارة الإجازات\n\n"
                   "📱 الإصدار: 2.0 (المحدث)\n"
                   "👨‍💻 المطور: فريق التطوير\n"
                   "📅 سنة الإنتاج: 2024\n"
                   "🛠️ التقنيات: Python, PyQt5, SQLite\n\n"
                   "💡 نظام شامل لإدارة إجازات الموظفين\n"
                   "مع واجهة حديثة وسهلة الاستخدام")
        
        msg.setStyleSheet("""
            QMessageBox {
                background: white;
                border-radius: 15px;
            }
            QMessageBox QLabel {
                color: #2c3e50;
                font-size: 14px;
                min-width: 350px;
            }
            QMessageBox QPushButton {
                background: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
            }
        """)
        
        msg.exec_()
        
    # تنفيذ الوظائف مع النوافذ المتقدمة
    def import_data(self):
        """فتح نافذة الاستيراد المتقدمة"""
        try:
            from modern_advanced_windows import AdvancedImportWindow
            import_window = AdvancedImportWindow()
            import_window.exec_()
        except ImportError:
            from modern_dialogs import ModernImportDialog
            import_window = ModernImportDialog()
            import_window.exec_()
        
    def daily_request(self):
        """فتح نافذة طلب الإجازة اليومية"""
        try:
            from modern_dialogs import ModernDailyRequestDialog
            daily_window = ModernDailyRequestDialog()
            daily_window.exec_()
        except ImportError:
            self.show_info_message("📝 طلب إجازة يومية", "سيتم فتح نافذة طلب الإجازة اليومية...")
        
    def hourly_request(self):
        """فتح نافذة طلب الإجازة الساعية المتقدمة"""
        try:
            from modern_request_windows import ModernHourlyRequestWindow
            hourly_window = ModernHourlyRequestWindow()
            hourly_window.exec_()
        except ImportError:
            self.show_info_message("⏱️ طلب إجازة ساعية", "سيتم فتح نافذة طلب الإجازة الساعية...")
        
    def add_vacation(self):
        """فتح نافذة إدراج الإجازات الإضافية"""
        try:
            from modern_request_windows import ModernAddVacationWindow
            add_window = ModernAddVacationWindow()
            add_window.exec_()
        except ImportError:
            self.show_info_message("➕ إدراج إجازات", "سيتم فتح نافذة إدراج الإجازات...")
        
    def show_reports(self):
        """فتح نافذة التقارير المتقدمة"""
        try:
            from modern_advanced_windows import ModernReportsWindow
            reports_window = ModernReportsWindow()
            reports_window.exec_()
        except ImportError:
            self.show_info_message("📊 التقارير", "سيتم فتح نافذة التقارير...")
        
    def search_employee(self):
        """فتح نافذة البحث المتقدمة"""
        try:
            from modern_search_settings import ModernSearchWindow
            search_window = ModernSearchWindow()
            search_window.exec_()
        except ImportError:
            self.show_info_message("🔍 البحث", "سيتم فتح نافذة البحث...")
        
    def edit_requests(self):
        """فتح نافذة تعديل الطلبات المتقدمة"""
        try:
            from modern_edit_delete import ModernEditRequestsWindow
            edit_window = ModernEditRequestsWindow()
            edit_window.exec_()
        except ImportError:
            self.show_info_message("✏️ تعديل الطلبات", "سيتم فتح نافذة تعديل الطلبات...")
        
    def delete_requests(self):
        """فتح نافذة حذف الطلبات المتقدمة"""
        try:
            from modern_edit_delete import ModernDeleteRequestsWindow
            delete_window = ModernDeleteRequestsWindow()
            delete_window.exec_()
        except ImportError:
            self.show_info_message("🗑️ حذف الطلبات", "سيتم فتح نافذة حذف الطلبات...")
        
    def show_settings(self):
        """فتح نافذة الإعدادات الشاملة"""
        try:
            from modern_search_settings import ModernSettingsWindow
            settings_window = ModernSettingsWindow()
            settings_window.exec_()
        except ImportError:
            self.show_info_message("⚙️ الإعدادات", "سيتم فتح نافذة الإعدادات...")
            
    def show_notifications(self):
        """فتح نافذة إدارة الإشعارات"""
        try:
            from modern_notifications_backup import ModernNotificationsWindow
            notifications_window = ModernNotificationsWindow()
            notifications_window.exec_()
        except ImportError:
            self.show_info_message("🔔 الإشعارات", "سيتم فتح نافذة إدارة الإشعارات...")
            
    def show_backup(self):
        """فتح نافذة إدارة النسخ الاحتياطي"""
        try:
            from modern_notifications_backup import ModernBackupWindow
            backup_window = ModernBackupWindow()
            backup_window.exec_()
        except ImportError:
            self.show_info_message("💾 النسخ الاحتياطي", "سيتم فتح نافذة النسخ الاحتياطي...")
            
    def show_analytics_dashboard(self):
        """فتح لوحة الإحصائيات المتقدمة"""
        try:
            from modern_simple_analytics import ModernSimpleAnalyticsWindow
            analytics_window = ModernSimpleAnalyticsWindow()
            analytics_window.exec_()
        except ImportError:
            self.show_info_message("📊 الإحصائيات", "سيتم فتح لوحة الإحصائيات المتقدمة...")
            
    def show_database_manager(self):
        """فتح نافذة إدارة قاعدة البيانات"""
        try:
            from modern_database_manager import ModernDatabaseWindow
            database_window = ModernDatabaseWindow()
            database_window.exec_()
        except ImportError:
            self.show_info_message("🗄️ قاعدة البيانات", "سيتم فتح نافذة إدارة قاعدة البيانات...")
            
    def show_ai_system(self):
        """فتح نظام الذكاء الاصطناعي"""
        try:
            from modern_ai_system import ModernAIWindow
            ai_window = ModernAIWindow()
            ai_window.exec_()
        except ImportError:
            self.show_info_message("🧠 الذكاء الاصطناعي", "سيتم فتح نظام الذكاء الاصطناعي...")
            
    def show_user_management(self):
        """فتح نظام إدارة المستخدمين"""
        try:
            from modern_user_management import ModernUserManagementWindow
            users_window = ModernUserManagementWindow()
            users_window.exec_()
        except ImportError:
            self.show_info_message("👥 إدارة المستخدمين", "سيتم فتح نظام إدارة المستخدمين...")
            
    def show_web_interface(self):
        """فتح واجهة الويب"""
        try:
            from modern_web_interface import start_web_interface
            start_web_interface()
            self.show_info_message("🌐 واجهة الويب", "تم تشغيل واجهة الويب!\n\nافتح المتصفح وتوجه إلى:\nhttp://localhost:5000")
        except ImportError:
            self.show_info_message("🌐 واجهة الويب", "سيتم فتح واجهة الويب...")
            
    def show_maintenance(self):
        """فتح نافذة صيانة النظام"""
        self.show_info_message("🔧 صيانة النظام", "سيتم إضافة نافذة صيانة النظام قريباً...")
        
    def show_info_message(self, title, message):
        """عرض رسالة معلومات"""
        msg = QMessageBox()
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setStyleSheet("""
            QMessageBox {
                background: white;
                border-radius: 10px;
            }
            QMessageBox QPushButton {
                background: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
            }
        """)
        msg.exec_()

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # استخدام نمط Fusion الحديث
    
    # إعداد الخط العربي
    font = QFont("Arial", 10)
    app.setFont(font)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة الرئيسية
    window = ModernMainWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل فوري لنظام إدارة الإجازات
Instant Run for Vacation Management System
"""

import sqlite3
import os
from datetime import datetime

def main():
    print("🎯 نظام إدارة الإجازات المتقدم - تشغيل فوري")
    print("="*60)
    
    # إنشاء قاعدة بيانات تجريبية
    conn = sqlite3.connect('vacation_live.db')
    cursor = conn.cursor()
    
    # إنشاء جدول الموظفين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            initial_balance INTEGER DEFAULT 30,
            used_days INTEGER DEFAULT 0,
            department TEXT DEFAULT 'عام'
        )
    ''')
    
    # إضافة بيانات تجريبية
    employees_data = [
        ('أحمد محمد علي', 30, 5, 'الإدارة'),
        ('فاطمة عبد الله', 35, 8, 'الموارد البشرية'),
        ('محمد عبد الرحمن', 30, 12, 'المالية'),
        ('عائشة سعيد', 40, 3, 'التسويق'),
        ('عبد الله أحمد', 30, 15, 'تقنية المعلومات')
    ]
    
    cursor.execute('DELETE FROM employees')
    for name, initial, used, dept in employees_data:
        cursor.execute('''
            INSERT INTO employees (name, initial_balance, used_days, department)
            VALUES (?, ?, ?, ?)
        ''', (name, initial, used, dept))
    
    conn.commit()
    
    print("✅ تم إنشاء قاعدة البيانات بنجاح")
    print()
    
    # عرض البيانات
    print("👥 قائمة الموظفين:")
    print("-" * 80)
    print("الاسم".ljust(20) + "القسم".ljust(15) + "الرصيد".ljust(8) + "المستخدم".ljust(10) + "المتبقي")
    print("-" * 80)
    
    cursor.execute('SELECT name, department, initial_balance, used_days FROM employees ORDER BY name')
    total_initial = 0
    total_used = 0
    
    for row in cursor.fetchall():
        name, dept, initial, used = row
        remaining = initial - used
        total_initial += initial
        total_used += used
        
        print(f"{name[:18]:<20} {dept[:13]:<15} {initial:<8} {used:<10} {remaining}")
    
    print("-" * 80)
    print(f"الإجمالي:".ljust(35) + f"{total_initial:<8} {total_used:<10} {total_initial - total_used}")
    
    print()
    print("📊 إحصائيات سريعة:")
    print(f"  👥 عدد الموظفين: {len(employees_data)}")
    print(f"  📈 إجمالي الرصيد: {total_initial} يوم")
    print(f"  📉 إجمالي المستخدم: {total_used} يوم")
    print(f"  💰 إجمالي المتبقي: {total_initial - total_used} يوم")
    print(f"  📊 معدل الاستخدام: {(total_used/total_initial*100):.1f}%")
    
    # عرض الموظفين برصيد منخفض
    print()
    print("⚠️ تنبيهات الرصيد المنخفض (<10 أيام):")
    cursor.execute('SELECT name, initial_balance - used_days as remaining FROM employees WHERE remaining < 10')
    low_balance = cursor.fetchall()
    
    if low_balance:
        for name, remaining in low_balance:
            print(f"  🔴 {name}: {remaining} يوم متبقي")
    else:
        print("  ✅ جميع الموظفين لديهم رصيد كافي")
    
    # رسم بياني بسيط
    print()
    print("📈 رسم بياني للأرصدة المتبقية:")
    print("-" * 50)
    
    cursor.execute('SELECT name, initial_balance - used_days as remaining FROM employees ORDER BY remaining DESC')
    max_remaining = max(row[1] for row in cursor.fetchall())
    
    cursor.execute('SELECT name, initial_balance - used_days as remaining FROM employees ORDER BY remaining DESC')
    for name, remaining in cursor.fetchall():
        bar_length = int((remaining / max_remaining) * 30) if max_remaining > 0 else 0
        bar = "█" * bar_length
        short_name = name[:12] + ".." if len(name) > 14 else name
        print(f"{short_name:<14}: {bar} ({remaining} يوم)")
    
    conn.close()
    
    print()
    print("🎉 النظام يعمل بشكل مثالي!")
    print()
    print("💡 الملفات المتاحة:")
    
    files_to_check = [
        'master_control_panel.py',
        'simple_demo.py', 
        'advanced_reports.py',
        'analytics_dashboard.py',
        'backup_system.py',
        'notification_system.py',
        'user_management_interface.py'
    ]
    
    for file in files_to_check:
        status = "✅" if os.path.exists(file) else "❌"
        print(f"  {status} {file}")
    
    print()
    print("🚀 طرق التشغيل:")
    print("  1. python simple_demo.py - العرض التوضيحي التفاعلي")
    print("  2. python master_control_panel.py - لوحة التحكم الرئيسية")
    print("  3. python analytics_dashboard.py - لوحة التحليلات")
    print("  4. python backup_interface.py - نظام النسخ الاحتياطي")
    print("  5. python notifications_interface.py - نظام الإشعارات")
    print("  6. تشغيل_النظام_المتقدم.bat - تشغيل سريع")
    
    print()
    print("🔧 ميزات النظام المتقدم:")
    features = [
        "📊 تقارير متقدمة مع تصدير Excel",
        "🔔 إشعارات ذكية وتنبيهات تلقائية",
        "📈 رسوم بيانية وتحليلات تفصيلية",
        "💾 نسخ احتياطي تلقائي وآمن",
        "👥 إدارة مستخدمين مع صلاحيات متقدمة",
        "🎯 لوحة تحكم موحدة لجميع الأنظمة"
    ]
    
    for feature in features:
        print(f"  ✨ {feature}")
    
    print()
    print("📋 معلومات النظام:")
    print(f"  📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  🗂️ مجلد العمل: {os.getcwd()}")
    print(f"  🐍 Python: متاح ويعمل بشكل صحيح")
    print(f"  🗄️ قاعدة البيانات: تم إنشاؤها بنجاح")
    
    print()
    print("="*60)
    print("✅ النظام جاهز للاستخدام الفوري!")
    print("💡 اختر أي من طرق التشغيل أعلاه لبدء الاستخدام")
    print("="*60)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ خطأ: {e}")
        print("💡 تأكد من وجود Python وجميع الملفات المطلوبة")

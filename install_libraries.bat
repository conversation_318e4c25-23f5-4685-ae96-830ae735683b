@echo off
chcp 65001 > nul
title تثبيت المكتبات المطلوبة

echo.
echo 🎯 تثبيت المكتبات المطلوبة لنظام إدارة الإجازات
echo ================================================
echo.

REM التحقق من وجود Python
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo 💡 يرجى تثبيت Python 3.7 أو أحدث من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version
echo.

echo 🔄 بدء تثبيت المكتبات...
echo ========================
echo.

echo 📦 تثبيت pandas...
pip install pandas
if errorlevel 1 (
    echo ❌ فشل تثبيت pandas
) else (
    echo ✅ تم تثبيت pandas بنجاح
)
echo.

echo 📦 تثبيت openpyxl...
pip install openpyxl
if errorlevel 1 (
    echo ❌ فشل تثبيت openpyxl
) else (
    echo ✅ تم تثبيت openpyxl بنجاح
)
echo.

echo 📦 تثبيت PyQt5...
pip install PyQt5
if errorlevel 1 (
    echo ❌ فشل تثبيت PyQt5
) else (
    echo ✅ تم تثبيت PyQt5 بنجاح
)
echo.

echo 📦 تثبيت reportlab...
pip install reportlab
if errorlevel 1 (
    echo ❌ فشل تثبيت reportlab
) else (
    echo ✅ تم تثبيت reportlab بنجاح
)
echo.

echo ========================
echo 🎉 انتهى تثبيت المكتبات!
echo.

echo 🔍 فحص المكتبات المثبتة...
python -c "
try:
    import pandas
    print('✅ pandas - مثبت')
except ImportError:
    print('❌ pandas - غير مثبت')

try:
    import openpyxl
    print('✅ openpyxl - مثبت')
except ImportError:
    print('❌ openpyxl - غير مثبت')

try:
    import PyQt5
    print('✅ PyQt5 - مثبت')
except ImportError:
    print('❌ PyQt5 - غير مثبت')

try:
    import reportlab
    print('✅ reportlab - مثبت')
except ImportError:
    print('❌ reportlab - غير مثبت')
"

echo.
echo 🚀 يمكنك الآن تشغيل البرنامج:
echo   python master_control_panel.py
echo   python main.py
echo   python simple_demo.py
echo.

pause

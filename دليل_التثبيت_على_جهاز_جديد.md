# دليل تثبيت نظام إدارة الإجازات على جهاز جديد

## 🎯 نظرة عامة
هذا الدليل يوضح كيفية تثبيت وتشغيل نظام إدارة الإجازات المتقدم على جهاز كمبيوتر جديد.

---

## 📋 المتطلبات الأساسية

### 🖥️ متطلبات النظام:
- **نظام التشغيل:** Windows 7/8/10/11
- **المعالج:** أي معالج حديث
- **الذاكرة:** 2 جيجابايت RAM أو أكثر
- **المساحة:** 500 ميجابايت مساحة فارغة

### 🐍 متطلبات البرمجيات:
- **Python 3.7 أو أحدث**
- **اتصال بالإنترنت** (لتحميل المكتبات)

---

## 🔄 خطوات التثبيت

### **الخطوة 1: نسخ ملفات البرنامج**

#### **الطريقة الأولى: نسخ المجلد كاملاً (الأسهل)**
1. انسخ المجلد بالكامل: `vacation`
2. الصقه في الجهاز الجديد في أي مكان (مثل: `C:\VacationSystem`)

#### **الطريقة الثانية: نسخ الملفات الأساسية فقط**
انسخ هذه الملفات:

**الملفات الأساسية:**
- `master_control_panel.py`
- `main.py`
- `database.py`
- `simple_demo.py`
- `live_demo.py`

**الأنظمة المتقدمة:**
- `advanced_reports.py`
- `analytics_dashboard.py`
- `backup_system.py`
- `notification_system.py`
- `user_management_interface.py`
- `reports_interface.py`
- `notifications_interface.py`
- `backup_interface.py`

**ملفات التشغيل:**
- `START.bat`
- `RUN.bat`
- `DEMO.bat`
- `SETUP_NEW_COMPUTER.bat`
- `install_libraries.bat`

**ملفات الإعداد:**
- `requirements.txt`
- `README_ADVANCED_SYSTEM.md`
- `دليل_التشغيل_السريع.md`

---

### **الخطوة 2: تثبيت Python**

#### **إذا لم يكن Python مثبتاً:**
1. **اذهب إلى:** https://python.org
2. **انقر على:** "Download Python" (حمل أحدث إصدار)
3. **شغل ملف التثبيت**
4. **مهم جداً:** ✅ فعل "Add Python to PATH"
5. **انقر:** "Install Now"
6. **انتظر حتى انتهاء التثبيت**

#### **للتحقق من التثبيت:**
1. افتح Command Prompt
2. اكتب: `python --version`
3. يجب أن ترى رقم الإصدار (مثل: Python 3.11.0)

---

### **الخطوة 3: تثبيت المكتبات المطلوبة**

#### **الطريقة الأولى: التثبيت التلقائي (الأسهل)**
1. **انقر مرتين على:** `SETUP_NEW_COMPUTER.bat`
2. **اتبع التعليمات على الشاشة**
3. **انتظر حتى انتهاء التثبيت**

#### **الطريقة الثانية: التثبيت اليدوي**
1. افتح Command Prompt في مجلد البرنامج
2. اكتب الأوامر التالية واحداً تلو الآخر:
```bash
pip install pandas
pip install openpyxl
pip install PyQt5
pip install reportlab
```

#### **الطريقة الثالثة: من ملف requirements**
```bash
pip install -r requirements.txt
```

---

### **الخطوة 4: اختبار التثبيت**

#### **فحص المكتبات:**
```bash
python -c "import pandas, openpyxl, PyQt5, reportlab, sqlite3; print('✅ جميع المكتبات متاحة')"
```

#### **اختبار سريع للنظام:**
```bash
python live_demo.py
```

---

## 🚀 تشغيل البرنامج

### **الطريقة الأولى: التشغيل السريع (الأسهل)**
انقر مرتين على أي من هذه الملفات:
- **`START.bat`** - لوحة التحكم الكاملة
- **`RUN.bat`** - تشغيل سريع
- **`DEMO.bat`** - العرض التوضيحي

### **الطريقة الثانية: من سطر الأوامر**
```bash
# افتح Command Prompt في مجلد البرنامج واكتب:
python master_control_panel.py
```

### **الطريقة الثالثة: الأنظمة المتخصصة**
```bash
python main.py                    # النظام الأساسي
python simple_demo.py             # العرض التوضيحي
python analytics_dashboard.py     # لوحة التحليلات
python run_reports.py             # نظام التقارير
```

---

## 🔐 معلومات تسجيل الدخول

### **المستخدم الافتراضي:**
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`
- **الصلاحيات:** مدير النظام (جميع الصلاحيات)

---

## 🆘 حل المشاكل الشائعة

### **مشكلة: "python is not recognized"**
**الحل:**
1. تأكد من تثبيت Python
2. تأكد من تفعيل "Add Python to PATH" أثناء التثبيت
3. أعد تشغيل الكمبيوتر
4. جرب `py` بدلاً من `python`

### **مشكلة: "No module named..."**
**الحل:**
```bash
pip install [اسم المكتبة]
```

### **مشكلة: البرنامج لا يفتح**
**الحل:**
1. تأكد من وجود جميع الملفات
2. جرب `python simple_demo.py` أولاً
3. تحقق من رسائل الخطأ

### **مشكلة: خطأ في قاعدة البيانات**
**الحل:**
1. احذف ملف `vacation_system.db`
2. شغل البرنامج مرة أخرى (سينشئ قاعدة جديدة)

---

## 📁 هيكل الملفات

```
VacationSystem/
├── master_control_panel.py     # لوحة التحكم الرئيسية
├── main.py                     # النظام الأساسي
├── database.py                 # قاعدة البيانات
├── simple_demo.py              # العرض التوضيحي
├── advanced_reports.py         # نظام التقارير
├── analytics_dashboard.py      # لوحة التحليلات
├── backup_system.py            # نظام النسخ الاحتياطي
├── notification_system.py      # نظام الإشعارات
├── START.bat                   # تشغيل سريع
├── RUN.bat                     # تشغيل مباشر
├── SETUP_NEW_COMPUTER.bat      # إعداد جهاز جديد
├── requirements.txt            # المكتبات المطلوبة
└── vacation_system.db          # قاعدة البيانات (تنشأ تلقائياً)
```

---

## ✅ قائمة التحقق النهائية

- [ ] تم نسخ جميع ملفات البرنامج
- [ ] تم تثبيت Python 3.7+
- [ ] تم تفعيل "Add Python to PATH"
- [ ] تم تثبيت جميع المكتبات المطلوبة
- [ ] تم اختبار النظام بنجاح
- [ ] يمكن تشغيل البرنامج بدون أخطاء

---

## 🎉 تهانينا!

إذا اكتملت جميع الخطوات بنجاح، فإن نظام إدارة الإجازات المتقدم جاهز للاستخدام على الجهاز الجديد!

**للدعم والمساعدة، راجع:**
- `README_ADVANCED_SYSTEM.md` - دليل شامل للنظام
- `دليل_التشغيل_السريع.md` - دليل المستخدم

---

**تم إعداد هذا الدليل بواسطة:** فريق تطوير نظام إدارة الإجازات  
**التاريخ:** 2024  
**الإصدار:** 2.0 المتقدم

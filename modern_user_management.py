#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة المستخدمين المتقدم
"""

import sys
import os
import json
import sqlite3
import hashlib
from datetime import datetime, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from modern_dialogs import ModernDialog

class UserManager:
    """مدير المستخدمين المتقدم"""
    
    def __init__(self, db_path='vacation_system.db'):
        self.db_path = db_path
        self.init_users_table()
        
    def init_users_table(self):
        """إنشاء جدول المستخدمين"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    email TEXT,
                    phone TEXT,
                    department TEXT,
                    role TEXT DEFAULT 'employee',
                    permissions TEXT DEFAULT '{}',
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    login_attempts INTEGER DEFAULT 0,
                    avatar_path TEXT
                )
            ''')
            
            # إنشاء مستخدم افتراضي
            cursor.execute('''
                INSERT OR IGNORE INTO users 
                (username, password_hash, full_name, role, permissions) 
                VALUES (?, ?, ?, ?, ?)
            ''', (
                'admin',
                self.hash_password('admin123'),
                'مدير النظام',
                'admin',
                json.dumps({
                    'manage_users': True,
                    'view_all_requests': True,
                    'approve_requests': True,
                    'system_settings': True,
                    'reports': True
                })
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في إنشاء جدول المستخدمين: {e}")
            
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
        
    def verify_password(self, password, password_hash):
        """التحقق من كلمة المرور"""
        return self.hash_password(password) == password_hash
        
    def authenticate_user(self, username, password):
        """مصادقة المستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, password_hash, full_name, role, permissions, status, login_attempts
                FROM users WHERE username = ?
            ''', (username,))
            
            user = cursor.fetchone()
            
            if not user:
                return {'success': False, 'message': 'اسم المستخدم غير موجود'}
                
            user_id, password_hash, full_name, role, permissions, status, login_attempts = user
            
            if status != 'active':
                return {'success': False, 'message': 'الحساب معطل'}
                
            if login_attempts >= 3:
                return {'success': False, 'message': 'الحساب مقفل - تم تجاوز محاولات تسجيل الدخول'}
                
            if self.verify_password(password, password_hash):
                # تحديث آخر تسجيل دخول وإعادة تعيين المحاولات
                cursor.execute('''
                    UPDATE users 
                    SET last_login = CURRENT_TIMESTAMP, login_attempts = 0
                    WHERE id = ?
                ''', (user_id,))
                
                conn.commit()
                conn.close()
                
                return {
                    'success': True,
                    'user': {
                        'id': user_id,
                        'username': username,
                        'full_name': full_name,
                        'role': role,
                        'permissions': json.loads(permissions) if permissions else {}
                    }
                }
            else:
                # زيادة عدد محاولات تسجيل الدخول الفاشلة
                cursor.execute('''
                    UPDATE users 
                    SET login_attempts = login_attempts + 1
                    WHERE id = ?
                ''', (user_id,))
                
                conn.commit()
                conn.close()
                
                return {'success': False, 'message': 'كلمة المرور غير صحيحة'}
                
        except Exception as e:
            return {'success': False, 'message': f'خطأ في المصادقة: {str(e)}'}
            
    def get_all_users(self):
        """الحصول على جميع المستخدمين"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, username, full_name, email, phone, department, 
                       role, status, created_at, last_login, login_attempts
                FROM users ORDER BY created_at DESC
            ''')
            
            users = cursor.fetchall()
            conn.close()
            
            return users
            
        except Exception as e:
            print(f"خطأ في الحصول على المستخدمين: {e}")
            return []
            
    def add_user(self, user_data):
        """إضافة مستخدم جديد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO users 
                (username, password_hash, full_name, email, phone, department, role, permissions)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_data['username'],
                self.hash_password(user_data['password']),
                user_data['full_name'],
                user_data.get('email', ''),
                user_data.get('phone', ''),
                user_data.get('department', ''),
                user_data.get('role', 'employee'),
                json.dumps(user_data.get('permissions', {}))
            ))
            
            conn.commit()
            conn.close()
            
            return {'success': True, 'message': 'تم إضافة المستخدم بنجاح'}
            
        except sqlite3.IntegrityError:
            return {'success': False, 'message': 'اسم المستخدم موجود بالفعل'}
        except Exception as e:
            return {'success': False, 'message': f'خطأ في إضافة المستخدم: {str(e)}'}
            
    def update_user(self, user_id, user_data):
        """تحديث بيانات المستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            update_fields = []
            values = []
            
            for field in ['username', 'full_name', 'email', 'phone', 'department', 'role', 'status']:
                if field in user_data:
                    update_fields.append(f"{field} = ?")
                    values.append(user_data[field])
                    
            if 'password' in user_data and user_data['password']:
                update_fields.append("password_hash = ?")
                values.append(self.hash_password(user_data['password']))
                
            if 'permissions' in user_data:
                update_fields.append("permissions = ?")
                values.append(json.dumps(user_data['permissions']))
                
            if update_fields:
                values.append(user_id)
                cursor.execute(f'''
                    UPDATE users SET {", ".join(update_fields)}
                    WHERE id = ?
                ''', values)
                
                conn.commit()
                
            conn.close()
            
            return {'success': True, 'message': 'تم تحديث المستخدم بنجاح'}
            
        except sqlite3.IntegrityError:
            return {'success': False, 'message': 'اسم المستخدم موجود بالفعل'}
        except Exception as e:
            return {'success': False, 'message': f'خطأ في تحديث المستخدم: {str(e)}'}
            
    def delete_user(self, user_id):
        """حذف مستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM users WHERE id = ?', (user_id,))
            
            if cursor.rowcount == 0:
                return {'success': False, 'message': 'المستخدم غير موجود'}
                
            conn.commit()
            conn.close()
            
            return {'success': True, 'message': 'تم حذف المستخدم بنجاح'}
            
        except Exception as e:
            return {'success': False, 'message': f'خطأ في حذف المستخدم: {str(e)}'}
            
    def reset_user_password(self, user_id, new_password):
        """إعادة تعيين كلمة مرور المستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE users 
                SET password_hash = ?, login_attempts = 0
                WHERE id = ?
            ''', (self.hash_password(new_password), user_id))
            
            if cursor.rowcount == 0:
                return {'success': False, 'message': 'المستخدم غير موجود'}
                
            conn.commit()
            conn.close()
            
            return {'success': True, 'message': 'تم إعادة تعيين كلمة المرور بنجاح'}
            
        except Exception as e:
            return {'success': False, 'message': f'خطأ في إعادة تعيين كلمة المرور: {str(e)}'}

class ModernUserManagementWindow(ModernDialog):
    """نافذة إدارة المستخدمين المتقدمة"""
    
    def __init__(self):
        super().__init__("👥 إدارة المستخدمين المتقدمة", 1300, 800)
        self.user_manager = UserManager()
        self.setup_user_management_content()
        
    def setup_user_management_content(self):
        """إعداد محتوى إدارة المستخدمين"""
        layout = QVBoxLayout(self.content_area)
        layout.setSpacing(15)
        
        # شريط التحكم
        self.create_user_control_bar(layout)
        
        # تبويبات إدارة المستخدمين
        self.create_user_management_tabs(layout)
        
        # إعداد الأزرار
        self.add_button("👤 إضافة مستخدم", self.add_new_user, "primary")
        self.add_button("🔄 تحديث القائمة", self.refresh_users_list, "secondary")
        self.add_button("📊 تقرير المستخدمين", self.generate_users_report, "secondary")
        self.add_button("❌ إغلاق", self.accept, "secondary")
        
    def create_user_control_bar(self, layout):
        """إنشاء شريط التحكم"""
        control_frame = QFrame()
        control_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2c3e50, stop: 1 #3498db
                );
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        control_layout = QHBoxLayout(control_frame)
        
        # معلومات النظام
        system_info = QVBoxLayout()
        
        title_label = QLabel("👥 نظام إدارة المستخدمين المتقدم")
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: white;
        """)
        system_info.addWidget(title_label)
        
        subtitle_label = QLabel("🔐 إدارة شاملة • أذونات متقدمة • أمان عالي")
        subtitle_label.setStyleSheet("""
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            margin-top: 5px;
        """)
        system_info.addWidget(subtitle_label)
        
        control_layout.addLayout(system_info)
        control_layout.addStretch()
        
        # إحصائيات سريعة
        stats_layout = QVBoxLayout()
        
        total_users = len(self.user_manager.get_all_users())
        users_count_label = QLabel(f"👥 المستخدمين: {total_users}")
        users_count_label.setStyleSheet("color: white; font-weight: bold;")
        stats_layout.addWidget(users_count_label)
        
        active_users = len([u for u in self.user_manager.get_all_users() if u[7] == 'active'])
        active_count_label = QLabel(f"✅ نشط: {active_users}")
        active_count_label.setStyleSheet("color: rgba(255, 255, 255, 0.9);")
        stats_layout.addWidget(active_count_label)
        
        control_layout.addLayout(stats_layout)
        
        layout.addWidget(control_frame)
        
    def create_user_management_tabs(self, layout):
        """إنشاء تبويبات إدارة المستخدمين"""
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                background: white;
            }
            QTabBar::tab {
                background: #f8f9fa;
                border: 2px solid #e0e0e0;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
                color: #2c3e50;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background: #2c3e50;
                color: white;
            }
        """)
        
        # تبويب قائمة المستخدمين
        self.create_users_list_tab()
        
        # تبويب الأذونات
        self.create_permissions_tab()
        
        # تبويب الأمان
        self.create_security_tab()
        
        # تبويب الإحصائيات
        self.create_users_stats_tab()
        
        layout.addWidget(self.tabs)
        
    def create_users_list_tab(self):
        """إنشاء تبويب قائمة المستخدمين"""
        users_widget = QWidget()
        users_layout = QVBoxLayout(users_widget)
        users_layout.setSpacing(15)
        
        # شريط البحث والفلترة
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        search_layout = QHBoxLayout(search_frame)
        
        # البحث
        search_label = QLabel("🔍 البحث:")
        search_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        search_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث باسم المستخدم أو الاسم الكامل...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                font-size: 14px;
            }
        """)
        self.search_input.textChanged.connect(self.filter_users)
        search_layout.addWidget(self.search_input)
        
        # فلتر الدور
        role_label = QLabel("👤 الدور:")
        role_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        search_layout.addWidget(role_label)
        
        self.role_filter = QComboBox()
        self.role_filter.addItems(['الكل', 'admin', 'manager', 'employee'])
        self.role_filter.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                font-size: 14px;
                min-width: 100px;
            }
        """)
        self.role_filter.currentTextChanged.connect(self.filter_users)
        search_layout.addWidget(self.role_filter)
        
        # فلتر الحالة
        status_label = QLabel("📊 الحالة:")
        status_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        search_layout.addWidget(status_label)
        
        self.status_filter = QComboBox()
        self.status_filter.addItems(['الكل', 'active', 'inactive', 'locked'])
        self.status_filter.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                font-size: 14px;
                min-width: 100px;
            }
        """)
        self.status_filter.currentTextChanged.connect(self.filter_users)
        search_layout.addWidget(self.status_filter)
        
        users_layout.addWidget(search_frame)
        
        # جدول المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(8)
        self.users_table.setHorizontalHeaderLabels([
            "المعرف", "اسم المستخدم", "الاسم الكامل", "البريد الإلكتروني", 
            "القسم", "الدور", "الحالة", "آخر دخول"
        ])
        
        self.users_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f1f3f4;
                font-size: 13px;
            }
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2c3e50, stop: 1 #34495e
                );
                color: white;
                border: none;
                padding: 12px;
                font-weight: bold;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #f1f3f4;
            }
            QTableWidget::item:selected {
                background: #e3f2fd;
                color: #1976d2;
            }
        """)
        
        # إعداد السياق menu
        self.users_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.users_table.customContextMenuRequested.connect(self.show_user_context_menu)
        
        # تحميل المستخدمين
        self.load_users()
        
        users_layout.addWidget(self.users_table)
        
        self.tabs.addTab(users_widget, "👥 قائمة المستخدمين")
        
    def create_permissions_tab(self):
        """إنشاء تبويب الأذونات"""
        permissions_widget = QWidget()
        permissions_layout = QVBoxLayout(permissions_widget)
        permissions_layout.setSpacing(15)
        
        # عنوان الأذونات
        permissions_title = QLabel("🔐 إدارة الأذونات والصلاحيات")
        permissions_title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        permissions_layout.addWidget(permissions_title)
        
        # أذونات الأدوار
        roles_frame = QFrame()
        roles_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        roles_layout = QVBoxLayout(roles_frame)
        
        # دور المدير
        admin_card = self.create_role_card(
            "👑 مدير النظام (Admin)",
            "صلاحيات كاملة على النظام",
            ["إدارة المستخدمين", "إعدادات النظام", "جميع التقارير", "حذف البيانات", "النسخ الاحتياطي"],
            "#e74c3c"
        )
        roles_layout.addWidget(admin_card)
        
        # دور المشرف
        manager_card = self.create_role_card(
            "👨‍💼 مشرف (Manager)",
            "إدارة الطلبات والموافقات",
            ["عرض الطلبات", "الموافقة على الطلبات", "تقارير القسم", "إدارة الأرصدة"],
            "#f39c12"
        )
        roles_layout.addWidget(manager_card)
        
        # دور الموظف
        employee_card = self.create_role_card(
            "👤 موظف (Employee)",
            "استخدام أساسي للنظام",
            ["تقديم الطلبات", "عرض الرصيد", "تعديل الطلبات المعلقة", "عرض التقارير الشخصية"],
            "#27ae60"
        )
        roles_layout.addWidget(employee_card)
        
        permissions_layout.addWidget(roles_frame)
        
        self.tabs.addTab(permissions_widget, "🔐 الأذونات")
        
    def create_role_card(self, title, description, permissions, color):
        """إنشاء بطاقة دور"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 {color}, stop: 1 rgba(255, 255, 255, 0.1)
                );
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 15px;
            }}
        """)
        
        card_layout = QVBoxLayout(card)
        
        # العنوان والوصف
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
        """)
        card_layout.addWidget(title_label)
        
        description_label = QLabel(description)
        description_label.setStyleSheet("""
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            margin: 5px 0 15px 0;
        """)
        card_layout.addWidget(description_label)
        
        # قائمة الأذونات
        permissions_text = "الصلاحيات:\n" + "\n".join([f"• {perm}" for perm in permissions])
        permissions_label = QLabel(permissions_text)
        permissions_label.setStyleSheet("""
            font-size: 13px;
            color: rgba(255, 255, 255, 0.95);
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 6px;
        """)
        card_layout.addWidget(permissions_label)
        
        return card
        
    def create_security_tab(self):
        """إنشاء تبويب الأمان"""
        security_widget = QWidget()
        security_layout = QVBoxLayout(security_widget)
        security_layout.setSpacing(15)
        
        # عنوان الأمان
        security_title = QLabel("🛡️ إعدادات الأمان والحماية")
        security_title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        security_layout.addWidget(security_title)
        
        # سياسات كلمة المرور
        password_frame = QFrame()
        password_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        password_layout = QVBoxLayout(password_frame)
        
        password_policy_title = QLabel("🔑 سياسة كلمة المرور")
        password_policy_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        """)
        password_layout.addWidget(password_policy_title)
        
        password_rules = QLabel("""
        ✅ الحد الأدنى: 8 أحرف
        ✅ يجب أن تحتوي على أحرف كبيرة وصغيرة
        ✅ يجب أن تحتوي على رقم واحد على الأقل
        ✅ يجب أن تحتوي على رمز خاص واحد على الأقل
        ⏰ صالحة لمدة 90 يوم
        🔒 لا يمكن إعادة استخدام آخر 5 كلمات مرور
        """)
        password_rules.setStyleSheet("""
            font-size: 14px;
            color: #495057;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        """)
        password_layout.addWidget(password_rules)
        
        security_layout.addWidget(password_frame)
        
        # إعدادات تسجيل الدخول
        login_frame = QFrame()
        login_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        login_layout = QVBoxLayout(login_frame)
        
        login_settings_title = QLabel("🔐 إعدادات تسجيل الدخول")
        login_settings_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        """)
        login_layout.addWidget(login_settings_title)
        
        login_settings = QLabel("""
        🚫 قفل الحساب بعد 3 محاولات فاشلة
        ⏱️ مهلة زمنية: 15 دقيقة بين المحاولات
        📱 إشعار عند تسجيل دخول جديد
        🌍 تتبع عناوين IP لتسجيل الدخول
        📊 سجل مفصل لجميع عمليات تسجيل الدخول
        🔄 إنهاء الجلسة تلقائياً بعد 30 دقيقة من عدم النشاط
        """)
        login_settings.setStyleSheet("""
            font-size: 14px;
            color: #495057;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
        """)
        login_layout.addWidget(login_settings)
        
        security_layout.addWidget(login_frame)
        
        self.tabs.addTab(security_widget, "🛡️ الأمان")
        
    def create_users_stats_tab(self):
        """إنشاء تبويب إحصائيات المستخدمين"""
        stats_widget = QWidget()
        stats_layout = QVBoxLayout(stats_widget)
        stats_layout.setSpacing(15)
        
        # عنوان الإحصائيات
        stats_title = QLabel("📊 إحصائيات المستخدمين والأنشطة")
        stats_title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        stats_layout.addWidget(stats_title)
        
        # بطاقات الإحصائيات
        stats_cards_layout = QHBoxLayout()
        
        users = self.user_manager.get_all_users()
        total_users = len(users)
        active_users = len([u for u in users if u[7] == 'active'])
        admin_users = len([u for u in users if u[6] == 'admin'])
        
        # إجمالي المستخدمين
        total_card = self.create_stats_card("👥", "إجمالي المستخدمين", str(total_users), "#3498db")
        stats_cards_layout.addWidget(total_card)
        
        # المستخدمين النشطين
        active_card = self.create_stats_card("✅", "المستخدمين النشطين", str(active_users), "#27ae60")
        stats_cards_layout.addWidget(active_card)
        
        # المديرين
        admin_card = self.create_stats_card("👑", "المديرين", str(admin_users), "#e74c3c")
        stats_cards_layout.addWidget(admin_card)
        
        # نشاط اليوم
        today_activity = len([u for u in users if u[9] and u[9].startswith(datetime.now().strftime('%Y-%m-%d'))])
        activity_card = self.create_stats_card("📈", "نشاط اليوم", str(today_activity), "#f39c12")
        stats_cards_layout.addWidget(activity_card)
        
        stats_layout.addLayout(stats_cards_layout)
        
        # رسم بياني للأنشطة
        activity_chart = self.create_activity_chart()
        stats_layout.addWidget(activity_chart)
        
        self.tabs.addTab(stats_widget, "📊 الإحصائيات")
        
    def create_stats_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: {color};
                border-radius: 10px;
                padding: 20px;
                min-width: 150px;
                min-height: 100px;
            }}
        """)
        
        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(10)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 32px; color: white;")
        card_layout.addWidget(icon_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: white;
        """)
        card_layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 12px;
            color: rgba(255, 255, 255, 0.9);
        """)
        title_label.setWordWrap(True)
        card_layout.addWidget(title_label)
        
        return card
        
    def create_activity_chart(self):
        """إنشاء رسم بياني للأنشطة"""
        chart_frame = QFrame()
        chart_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 20px;
                margin-top: 15px;
            }
        """)
        
        chart_layout = QVBoxLayout(chart_frame)
        
        chart_title = QLabel("📈 نشاط تسجيل الدخول - آخر 7 أيام")
        chart_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        chart_layout.addWidget(chart_title)
        
        # رسم بياني نصي بسيط
        import random
        days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']
        
        for day in days:
            activity_count = random.randint(5, 25)
            
            day_layout = QHBoxLayout()
            
            # اسم اليوم
            day_label = QLabel(day)
            day_label.setStyleSheet("font-weight: bold; color: #2c3e50; min-width: 80px;")
            day_layout.addWidget(day_label)
            
            # شريط النشاط
            activity_bar = QProgressBar()
            activity_bar.setMaximum(30)
            activity_bar.setValue(activity_count)
            activity_bar.setFormat(f"{activity_count} تسجيل دخول")
            activity_bar.setStyleSheet("""
                QProgressBar {
                    border: 2px solid #3498db;
                    border-radius: 5px;
                    text-align: center;
                    font-weight: bold;
                    background: white;
                }
                QProgressBar::chunk {
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 1, y2: 0,
                        stop: 0 #3498db, stop: 1 #2980b9
                    );
                    border-radius: 3px;
                }
            """)
            day_layout.addWidget(activity_bar)
            
            chart_layout.addLayout(day_layout)
            
        return chart_frame
        
    def load_users(self):
        """تحميل قائمة المستخدمين"""
        users = self.user_manager.get_all_users()
        
        self.users_table.setRowCount(len(users))
        
        for row, user in enumerate(users):
            user_id, username, full_name, email, phone, department, role, status, created_at, last_login, login_attempts = user
            
            # المعرف
            id_item = QTableWidgetItem(str(user_id))
            self.users_table.setItem(row, 0, id_item)
            
            # اسم المستخدم
            username_item = QTableWidgetItem(username)
            username_item.setBackground(QColor(240, 248, 255))
            self.users_table.setItem(row, 1, username_item)
            
            # الاسم الكامل
            fullname_item = QTableWidgetItem(full_name)
            self.users_table.setItem(row, 2, fullname_item)
            
            # البريد الإلكتروني
            email_item = QTableWidgetItem(email or '')
            self.users_table.setItem(row, 3, email_item)
            
            # القسم
            department_item = QTableWidgetItem(department or '')
            self.users_table.setItem(row, 4, department_item)
            
            # الدور
            role_item = QTableWidgetItem(role)
            if role == 'admin':
                role_item.setBackground(QColor(248, 215, 218))
            elif role == 'manager':
                role_item.setBackground(QColor(255, 243, 205))
            else:
                role_item.setBackground(QColor(212, 237, 218))
            self.users_table.setItem(row, 5, role_item)
            
            # الحالة
            status_item = QTableWidgetItem(status)
            if status == 'active':
                status_item.setBackground(QColor(212, 237, 218))
            elif status == 'inactive':
                status_item.setBackground(QColor(255, 243, 205))
            else:
                status_item.setBackground(QColor(248, 215, 218))
            self.users_table.setItem(row, 6, status_item)
            
            # آخر دخول
            last_login_item = QTableWidgetItem(last_login or 'لم يسجل دخول')
            self.users_table.setItem(row, 7, last_login_item)
            
        self.users_table.resizeColumnsToContents()
        
    def filter_users(self):
        """فلترة المستخدمين"""
        search_text = self.search_input.text().lower()
        role_filter = self.role_filter.currentText()
        status_filter = self.status_filter.currentText()
        
        for row in range(self.users_table.rowCount()):
            show_row = True
            
            # فلترة البحث النصي
            if search_text:
                username = self.users_table.item(row, 1).text().lower()
                fullname = self.users_table.item(row, 2).text().lower()
                if search_text not in username and search_text not in fullname:
                    show_row = False
                    
            # فلترة الدور
            if role_filter != 'الكل':
                role = self.users_table.item(row, 5).text()
                if role != role_filter:
                    show_row = False
                    
            # فلترة الحالة
            if status_filter != 'الكل':
                status = self.users_table.item(row, 6).text()
                if status != status_filter:
                    show_row = False
                    
            self.users_table.setRowHidden(row, not show_row)
            
    def show_user_context_menu(self, position):
        """عرض قائمة السياق للمستخدم"""
        if self.users_table.itemAt(position):
            menu = QMenu(self)
            
            edit_action = QAction("✏️ تعديل المستخدم", self)
            edit_action.triggered.connect(self.edit_selected_user)
            menu.addAction(edit_action)
            
            reset_password_action = QAction("🔑 إعادة تعيين كلمة المرور", self)
            reset_password_action.triggered.connect(self.reset_user_password)
            menu.addAction(reset_password_action)
            
            menu.addSeparator()
            
            delete_action = QAction("🗑️ حذف المستخدم", self)
            delete_action.triggered.connect(self.delete_selected_user)
            menu.addAction(delete_action)
            
            menu.exec_(self.users_table.mapToGlobal(position))
            
    def add_new_user(self):
        """إضافة مستخدم جديد"""
        dialog = UserFormDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            user_data = dialog.get_user_data()
            result = self.user_manager.add_user(user_data)
            
            if result['success']:
                QMessageBox.information(self, "نجح", result['message'])
                self.load_users()
            else:
                QMessageBox.critical(self, "خطأ", result['message'])
                
    def edit_selected_user(self):
        """تعديل المستخدم المحدد"""
        current_row = self.users_table.currentRow()
        if current_row >= 0:
            user_id = int(self.users_table.item(current_row, 0).text())
            
            # جلب بيانات المستخدم الحالية
            users = self.user_manager.get_all_users()
            current_user = next((u for u in users if u[0] == user_id), None)
            
            if current_user:
                dialog = UserFormDialog(self, current_user)
                if dialog.exec_() == QDialog.Accepted:
                    user_data = dialog.get_user_data()
                    result = self.user_manager.update_user(user_id, user_data)
                    
                    if result['success']:
                        QMessageBox.information(self, "نجح", result['message'])
                        self.load_users()
                    else:
                        QMessageBox.critical(self, "خطأ", result['message'])
                        
    def reset_user_password(self):
        """إعادة تعيين كلمة مرور المستخدم"""
        current_row = self.users_table.currentRow()
        if current_row >= 0:
            user_id = int(self.users_table.item(current_row, 0).text())
            username = self.users_table.item(current_row, 1).text()
            
            new_password, ok = QInputDialog.getText(
                self, "إعادة تعيين كلمة المرور", 
                f"أدخل كلمة المرور الجديدة للمستخدم '{username}':",
                QLineEdit.Password
            )
            
            if ok and new_password:
                result = self.user_manager.reset_user_password(user_id, new_password)
                
                if result['success']:
                    QMessageBox.information(self, "نجح", result['message'])
                else:
                    QMessageBox.critical(self, "خطأ", result['message'])
                    
    def delete_selected_user(self):
        """حذف المستخدم المحدد"""
        current_row = self.users_table.currentRow()
        if current_row >= 0:
            username = self.users_table.item(current_row, 1).text()
            user_id = int(self.users_table.item(current_row, 0).text())
            
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف المستخدم '{username}'؟\n\nهذا الإجراء لا يمكن التراجع عنه!",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                result = self.user_manager.delete_user(user_id)
                
                if result['success']:
                    QMessageBox.information(self, "نجح", result['message'])
                    self.load_users()
                else:
                    QMessageBox.critical(self, "خطأ", result['message'])
                    
    def refresh_users_list(self):
        """تحديث قائمة المستخدمين"""
        self.load_users()
        QMessageBox.information(self, "تم التحديث", "تم تحديث قائمة المستخدمين بنجاح!")
        
    def generate_users_report(self):
        """إنشاء تقرير المستخدمين"""
        users = self.user_manager.get_all_users()
        
        report = f"""
        📊 تقرير المستخدمين - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        
        📈 الإحصائيات العامة:
        • إجمالي المستخدمين: {len(users)}
        • المستخدمين النشطين: {len([u for u in users if u[7] == 'active'])}
        • المديرين: {len([u for u in users if u[6] == 'admin'])}
        • المشرفين: {len([u for u in users if u[6] == 'manager'])}
        • الموظفين: {len([u for u in users if u[6] == 'employee'])}
        
        📋 تفاصيل المستخدمين:
        """
        
        for user in users[:10]:  # أول 10 مستخدمين
            report += f"\n• {user[2]} ({user[1]}) - {user[6]} - {user[7]}"
            
        QMessageBox.information(self, "تقرير المستخدمين", report)

class UserFormDialog(QDialog):
    """نافذة نموذج المستخدم"""
    
    def __init__(self, parent=None, user_data=None):
        super().__init__(parent)
        self.user_data = user_data
        self.setWindowTitle("👤 نموذج المستخدم")
        self.setFixedSize(500, 600)
        self.setup_form()
        
        if user_data:
            self.load_user_data()
            
    def setup_form(self):
        """إعداد النموذج"""
        layout = QVBoxLayout(self)
        
        # العنوان
        title = QLabel("👤 إضافة/تعديل المستخدم" if not self.user_data else "✏️ تعديل المستخدم")
        title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # النموذج
        form_layout = QFormLayout()
        
        # اسم المستخدم
        self.username_input = QLineEdit()
        self.username_input.setStyleSheet(self.get_input_style())
        form_layout.addRow("اسم المستخدم:", self.username_input)
        
        # كلمة المرور
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(self.get_input_style())
        form_layout.addRow("كلمة المرور:", self.password_input)
        
        # الاسم الكامل
        self.fullname_input = QLineEdit()
        self.fullname_input.setStyleSheet(self.get_input_style())
        form_layout.addRow("الاسم الكامل:", self.fullname_input)
        
        # البريد الإلكتروني
        self.email_input = QLineEdit()
        self.email_input.setStyleSheet(self.get_input_style())
        form_layout.addRow("البريد الإلكتروني:", self.email_input)
        
        # رقم الهاتف
        self.phone_input = QLineEdit()
        self.phone_input.setStyleSheet(self.get_input_style())
        form_layout.addRow("رقم الهاتف:", self.phone_input)
        
        # القسم
        self.department_combo = QComboBox()
        self.department_combo.addItems(['', 'التقنية', 'المالية', 'التسويق', 'الموارد البشرية', 'الإدارة'])
        self.department_combo.setStyleSheet(self.get_input_style())
        form_layout.addRow("القسم:", self.department_combo)
        
        # الدور
        self.role_combo = QComboBox()
        self.role_combo.addItems(['employee', 'manager', 'admin'])
        self.role_combo.setStyleSheet(self.get_input_style())
        form_layout.addRow("الدور:", self.role_combo)
        
        # الحالة
        self.status_combo = QComboBox()
        self.status_combo.addItems(['active', 'inactive', 'locked'])
        self.status_combo.setStyleSheet(self.get_input_style())
        form_layout.addRow("الحالة:", self.status_combo)
        
        layout.addLayout(form_layout)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet("""
            QPushButton {
                background: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #229954;
            }
        """)
        save_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(save_btn)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #c0392b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        
    def get_input_style(self):
        """الحصول على نمط الإدخال"""
        return """
            QLineEdit, QComboBox {
                padding: 8px;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                font-size: 14px;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #3498db;
            }
        """
        
    def load_user_data(self):
        """تحميل بيانات المستخدم للتعديل"""
        if self.user_data:
            user_id, username, full_name, email, phone, department, role, status = self.user_data[:8]
            
            self.username_input.setText(username)
            self.fullname_input.setText(full_name)
            self.email_input.setText(email or '')
            self.phone_input.setText(phone or '')
            self.department_combo.setCurrentText(department or '')
            self.role_combo.setCurrentText(role)
            self.status_combo.setCurrentText(status)
            
    def get_user_data(self):
        """الحصول على بيانات المستخدم من النموذج"""
        data = {
            'username': self.username_input.text(),
            'full_name': self.fullname_input.text(),
            'email': self.email_input.text(),
            'phone': self.phone_input.text(),
            'department': self.department_combo.currentText(),
            'role': self.role_combo.currentText(),
            'status': self.status_combo.currentText()
        }
        
        if self.password_input.text():
            data['password'] = self.password_input.text()
            
        return data

def test_user_management():
    """اختبار نظام إدارة المستخدمين"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # اختبار نافذة إدارة المستخدمين
    user_window = ModernUserManagementWindow()
    user_window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    test_user_management()
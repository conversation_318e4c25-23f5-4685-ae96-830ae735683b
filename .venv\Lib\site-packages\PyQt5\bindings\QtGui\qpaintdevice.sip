// qpaintdevice.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPaintDevice
{
%TypeHeaderCode
#include <qpaintdevice.h>
%End

public:
    enum PaintDeviceMetric
    {
        PdmWidth,
        PdmHeight,
        PdmWidthMM,
        PdmHeightMM,
        PdmNumColors,
        PdmDepth,
        PdmDpiX,
        PdmDpiY,
        PdmPhysicalDpiX,
        PdmPhysicalDpiY,
%If (Qt_5_1_0 -)
        PdmDevicePixelRatio,
%End
%If (Qt_5_6_0 -)
        PdmDevicePixelRatioScaled,
%End
    };

    virtual ~QPaintDevice();
    virtual QPaintEngine *paintEngine() const = 0;
    int width() const;
    int height() const;
    int widthMM() const;
    int heightMM() const;
    int logicalDpiX() const;
    int logicalDpiY() const;
    int physicalDpiX() const;
    int physicalDpiY() const;
    int depth() const;
    bool paintingActive() const;
    int colorCount() const;
%If (Qt_5_1_0 -)
    int devicePixelRatio() const;
%End

protected:
    QPaintDevice();
    virtual int metric(QPaintDevice::PaintDeviceMetric metric) const;

public:
%If (Qt_5_6_0 -)
    qreal devicePixelRatioF() const;
%End
%If (Qt_5_6_0 -)
    static qreal devicePixelRatioFScale();
%End

private:
    QPaintDevice(const QPaintDevice &);
};

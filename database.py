import sqlite3
import os
from datetime import datetime, timedelta
import pandas as pd
import json
import hashlib

class VacationDatabase:
    def __init__(self, db_path='vacation_system.db'):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول المستخدمين المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                full_name TEXT,
                email TEXT,
                department TEXT,
                is_active BOOLEAN DEFAULT 1,
                last_login TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول الأدوار والصلاحيات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                role_name TEXT UNIQUE NOT NULL,
                role_description TEXT,
                permissions TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول صلاحيات المستخدمين المخصصة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                permission_name TEXT NOT NULL,
                granted BOOLEAN DEFAULT 1,
                granted_by INTEGER,
                granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (granted_by) REFERENCES users (id)
            )
        ''')

        # جدول سجل العمليات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                target_type TEXT,
                target_id TEXT,
                details TEXT,
                ip_address TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # جدول الرصيد الابتدائي
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS initial_balance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                full_name TEXT NOT NULL,
                rank TEXT,
                days_count INTEGER NOT NULL,
                date TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول طلبات الإجازة اليومية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS daily_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                full_name TEXT NOT NULL,
                employee_id TEXT,
                job_title TEXT,
                department TEXT,
                vacation_type TEXT NOT NULL,
                start_date TEXT NOT NULL,
                days_count INTEGER NOT NULL,
                end_date TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول طلبات الإجازة الساعية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS hourly_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                full_name TEXT NOT NULL,
                usage_date TEXT NOT NULL,
                hours_count INTEGER NOT NULL,
                days_equivalent REAL NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الإجازات المدرجة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS added_vacations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                full_name TEXT NOT NULL,
                date TEXT NOT NULL,
                days_count INTEGER NOT NULL,
                reason TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الأرشيف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS archived_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                year INTEGER NOT NULL,
                data TEXT NOT NULL,
                archived_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء الأدوار الافتراضية
        default_roles = [
            ('admin', 'مدير النظام', json.dumps([
                'user_management', 'role_management', 'system_settings',
                'view_all_data', 'edit_all_data', 'delete_data',
                'generate_reports', 'backup_restore', 'view_logs'
            ])),
            ('hr_manager', 'مدير الموارد البشرية', json.dumps([
                'view_all_data', 'edit_vacation_data', 'generate_reports',
                'approve_requests', 'manage_employees'
            ])),
            ('supervisor', 'مشرف', json.dumps([
                'view_department_data', 'approve_department_requests',
                'generate_department_reports'
            ])),
            ('employee', 'موظف', json.dumps([
                'view_own_data', 'submit_requests', 'view_own_reports'
            ])),
            ('viewer', 'مستعرض', json.dumps([
                'view_limited_data', 'view_public_reports'
            ]))
        ]

        for role_name, description, permissions in default_roles:
            cursor.execute('''
                INSERT OR IGNORE INTO roles (role_name, role_description, permissions)
                VALUES (?, ?, ?)
            ''', (role_name, description, permissions))

        # إنشاء مستخدم افتراضي محسن
        cursor.execute('''
            INSERT OR IGNORE INTO users
            (username, password, role, full_name, email, department, is_active)
            VALUES ('admin', 'admin123', 'admin', 'مدير النظام', '<EMAIL>', 'الإدارة', 1)
        ''')
        
        conn.commit()
        conn.close()
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        return sqlite3.connect(self.db_path)
    
    def authenticate_user(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, role FROM users 
            WHERE username = ? AND password = ?
        ''', (username, password))
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            return {
                'id': user[0],
                'username': user[1], 
                'role': user[2]
            }
        return None
    
    def import_initial_balance(self, excel_file):
        """استيراد الرصيد الابتدائي من ملف Excel"""
        try:
            df = pd.read_excel(excel_file)
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # حذف البيانات السابقة
            cursor.execute('DELETE FROM initial_balance')
            
            for _, row in df.iterrows():
                cursor.execute('''
                    INSERT INTO initial_balance (full_name, rank, days_count, date)
                    VALUES (?, ?, ?, ?)
                ''', (row['الاسم واللقب'], row['الرتبة'], row['عدد الأيام'], row['التاريخ']))
            
            conn.commit()
            conn.close()
            return True, "تم استيراد البيانات بنجاح"
            
        except Exception as e:
            return False, f"خطأ في استيراد البيانات: {str(e)}"
    
    def import_initial_balance_data(self, full_name, rank, days_count, date):
        """استيراد بيانات رصيد ابتدائي فردية"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO initial_balance (full_name, rank, days_count, date)
                VALUES (?, ?, ?, ?)
            ''', (full_name, rank, days_count, date))
            
            conn.commit()
            conn.close()
            return True, "تم إضافة الرصيد الابتدائي"
            
        except Exception as e:
            return False, f"خطأ في إضافة الرصيد: {str(e)}"
    
    def search_employee(self, search_term):
        """البحث عن موظف باستخدام الاسم"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT DISTINCT full_name FROM initial_balance 
                WHERE full_name LIKE ?
            ''', (f'%{search_term}%',))
            
            results = cursor.fetchall()
            conn.close()
            
            return [row[0] for row in results]
            
        except Exception as e:
            return []
    
    def add_daily_request(self, full_name, employee_id, job_title, department, 
                         vacation_type, start_date, days_count):
        """إضافة طلب إجازة يومية"""
        try:
            # حساب تاريخ النهاية
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = start_dt + timedelta(days=days_count - 1)
            end_date = end_dt.strftime('%Y-%m-%d')
            
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO daily_requests 
                (full_name, employee_id, job_title, department, vacation_type, 
                 start_date, days_count, end_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (full_name, employee_id, job_title, department, vacation_type,
                  start_date, days_count, end_date))
            
            conn.commit()
            conn.close()
            return True, "تم إضافة الطلب بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إضافة الطلب: {str(e)}"
    
    def add_hourly_request(self, full_name, usage_date, hours_count):
        """إضافة طلب إجازة ساعية"""
        try:
            # حساب المعادل بالأيام
            days_equivalent = (hours_count * 3) / 24
            
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO hourly_requests 
                (full_name, usage_date, hours_count, days_equivalent)
                VALUES (?, ?, ?, ?)
            ''', (full_name, usage_date, hours_count, days_equivalent))
            
            conn.commit()
            conn.close()
            return True, "تم إضافة الطلب بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إضافة الطلب: {str(e)}"
    
    def add_vacation(self, full_name, date, days_count, reason):
        """إضافة إجازة مدرجة"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO added_vacations (full_name, date, days_count, reason)
                VALUES (?, ?, ?, ?)
            ''', (full_name, date, days_count, reason))
            
            conn.commit()
            conn.close()
            return True, "تم إضافة الإجازة بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إضافة الإجازة: {str(e)}"
    
    def get_employee_balance(self, full_name):
        """حساب الرصيد الصافي للموظف"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # الرصيد الابتدائي
        cursor.execute('''
            SELECT COALESCE(SUM(days_count), 0) FROM initial_balance 
            WHERE full_name = ?
        ''', (full_name,))
        initial_balance = cursor.fetchone()[0]
        
        # الإجازات المدرجة
        cursor.execute('''
            SELECT COALESCE(SUM(days_count), 0) FROM added_vacations 
            WHERE full_name = ?
        ''', (full_name,))
        added_vacations = cursor.fetchone()[0]
        
        # الإجازات اليومية المستفادة
        cursor.execute('''
            SELECT COALESCE(SUM(days_count), 0) FROM daily_requests 
            WHERE full_name = ?
        ''', (full_name,))
        daily_used = cursor.fetchone()[0]
        
        # الإجازات الساعية المستفادة
        cursor.execute('''
            SELECT COALESCE(SUM(days_equivalent), 0) FROM hourly_requests 
            WHERE full_name = ?
        ''', (full_name,))
        hourly_used = cursor.fetchone()[0]
        
        conn.close()
        
        # الرصيد الصافي = الرصيد الابتدائي + المدرجة - المستفادة
        net_balance = initial_balance + added_vacations - daily_used - hourly_used
        
        return {
            'initial_balance': initial_balance,
            'added_vacations': added_vacations,
            'daily_used': daily_used,
            'hourly_used': hourly_used,
            'net_balance': net_balance
        }
    
    def get_all_employees(self):
        """الحصول على قائمة جميع الموظفين"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT DISTINCT full_name FROM initial_balance
            UNION
            SELECT DISTINCT full_name FROM daily_requests
            UNION
            SELECT DISTINCT full_name FROM hourly_requests
            UNION
            SELECT DISTINCT full_name FROM added_vacations
        ''')
        
        employees = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        return employees
    
    def get_requests_by_type(self, request_type):
        """الحصول على الطلبات حسب النوع"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        if request_type == 'daily':
            cursor.execute('''
                SELECT * FROM daily_requests ORDER BY created_at DESC
            ''')
        elif request_type == 'hourly':
            cursor.execute('''
                SELECT * FROM hourly_requests ORDER BY created_at DESC
            ''')
        elif request_type == 'added':
            cursor.execute('''
                SELECT * FROM added_vacations ORDER BY created_at DESC
            ''')
        
        requests = cursor.fetchall()
        conn.close()
        
        return requests
    
    def delete_request(self, request_id, request_type):
        """حذف طلب"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            if request_type == 'daily':
                cursor.execute('DELETE FROM daily_requests WHERE id = ?', (request_id,))
            elif request_type == 'hourly':
                cursor.execute('DELETE FROM hourly_requests WHERE id = ?', (request_id,))
            elif request_type == 'added':
                cursor.execute('DELETE FROM added_vacations WHERE id = ?', (request_id,))

            conn.commit()
            conn.close()
            return True, "تم حذف الطلب بنجاح"

        except Exception as e:
            return False, f"خطأ في حذف الطلب: {str(e)}"

    def get_monthly_report(self, year, month):
        """تقرير شهري للإجازات"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # تقرير الإجازات اليومية للشهر
        cursor.execute('''
            SELECT full_name, vacation_type, start_date, end_date, days_count
            FROM daily_requests
            WHERE strftime('%Y', start_date) = ? AND strftime('%m', start_date) = ?
            ORDER BY start_date
        ''', (str(year), f'{month:02d}'))
        daily_requests = cursor.fetchall()

        # تقرير الإجازات الساعية للشهر
        cursor.execute('''
            SELECT full_name, usage_date, hours_count, days_equivalent
            FROM hourly_requests
            WHERE strftime('%Y', usage_date) = ? AND strftime('%m', usage_date) = ?
            ORDER BY usage_date
        ''', (str(year), f'{month:02d}'))
        hourly_requests = cursor.fetchall()

        # إحصائيات الشهر
        cursor.execute('''
            SELECT
                COUNT(*) as total_daily_requests,
                COALESCE(SUM(days_count), 0) as total_daily_days
            FROM daily_requests
            WHERE strftime('%Y', start_date) = ? AND strftime('%m', start_date) = ?
        ''', (str(year), f'{month:02d}'))
        daily_stats = cursor.fetchone()

        cursor.execute('''
            SELECT
                COUNT(*) as total_hourly_requests,
                COALESCE(SUM(hours_count), 0) as total_hours,
                COALESCE(SUM(days_equivalent), 0) as total_hourly_days
            FROM hourly_requests
            WHERE strftime('%Y', usage_date) = ? AND strftime('%m', usage_date) = ?
        ''', (str(year), f'{month:02d}'))
        hourly_stats = cursor.fetchone()

        conn.close()

        return {
            'daily_requests': daily_requests,
            'hourly_requests': hourly_requests,
            'daily_stats': daily_stats,
            'hourly_stats': hourly_stats
        }

    def get_yearly_report(self, year):
        """تقرير سنوي للإجازات"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # إحصائيات سنوية شاملة
        cursor.execute('''
            SELECT
                strftime('%m', start_date) as month,
                COUNT(*) as requests_count,
                SUM(days_count) as total_days
            FROM daily_requests
            WHERE strftime('%Y', start_date) = ?
            GROUP BY strftime('%m', start_date)
            ORDER BY month
        ''', (str(year),))
        monthly_daily_stats = cursor.fetchall()

        cursor.execute('''
            SELECT
                strftime('%m', usage_date) as month,
                COUNT(*) as requests_count,
                SUM(hours_count) as total_hours,
                SUM(days_equivalent) as total_days
            FROM hourly_requests
            WHERE strftime('%Y', usage_date) = ?
            GROUP BY strftime('%m', usage_date)
            ORDER BY month
        ''', (str(year),))
        monthly_hourly_stats = cursor.fetchall()

        # أكثر الموظفين استخداماً للإجازات
        cursor.execute('''
            SELECT
                full_name,
                COUNT(*) as requests_count,
                SUM(days_count) as total_days
            FROM daily_requests
            WHERE strftime('%Y', start_date) = ?
            GROUP BY full_name
            ORDER BY total_days DESC
            LIMIT 10
        ''', (str(year),))
        top_employees_daily = cursor.fetchall()

        # إحصائيات عامة للسنة
        cursor.execute('''
            SELECT
                COUNT(*) as total_requests,
                SUM(days_count) as total_days,
                AVG(days_count) as avg_days_per_request
            FROM daily_requests
            WHERE strftime('%Y', start_date) = ?
        ''', (str(year),))
        yearly_daily_summary = cursor.fetchone()

        cursor.execute('''
            SELECT
                COUNT(*) as total_requests,
                SUM(hours_count) as total_hours,
                SUM(days_equivalent) as total_days,
                AVG(hours_count) as avg_hours_per_request
            FROM hourly_requests
            WHERE strftime('%Y', usage_date) = ?
        ''', (str(year),))
        yearly_hourly_summary = cursor.fetchone()

        conn.close()

        return {
            'monthly_daily_stats': monthly_daily_stats,
            'monthly_hourly_stats': monthly_hourly_stats,
            'top_employees_daily': top_employees_daily,
            'yearly_daily_summary': yearly_daily_summary,
            'yearly_hourly_summary': yearly_hourly_summary
        }

    def get_employee_detailed_report(self, full_name, start_date=None, end_date=None):
        """تقرير مفصل لموظف محدد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # بناء شروط التاريخ
        date_condition = ""
        params = [full_name]

        if start_date and end_date:
            date_condition = " AND start_date BETWEEN ? AND ?"
            params.extend([start_date, end_date])

        # الإجازات اليومية
        cursor.execute(f'''
            SELECT start_date, end_date, vacation_type, days_count, created_at
            FROM daily_requests
            WHERE full_name = ?{date_condition}
            ORDER BY start_date DESC
        ''', params)
        daily_requests = cursor.fetchall()

        # الإجازات الساعية
        hourly_params = [full_name]
        hourly_date_condition = ""
        if start_date and end_date:
            hourly_date_condition = " AND usage_date BETWEEN ? AND ?"
            hourly_params.extend([start_date, end_date])

        cursor.execute(f'''
            SELECT usage_date, hours_count, days_equivalent, created_at
            FROM hourly_requests
            WHERE full_name = ?{hourly_date_condition}
            ORDER BY usage_date DESC
        ''', hourly_params)
        hourly_requests = cursor.fetchall()

        # الإجازات المدرجة
        added_params = [full_name]
        added_date_condition = ""
        if start_date and end_date:
            added_date_condition = " AND date BETWEEN ? AND ?"
            added_params.extend([start_date, end_date])

        cursor.execute(f'''
            SELECT date, days_count, reason, created_at
            FROM added_vacations
            WHERE full_name = ?{added_date_condition}
            ORDER BY date DESC
        ''', added_params)
        added_vacations = cursor.fetchall()

        # الرصيد الحالي
        balance = self.get_employee_balance(full_name)

        conn.close()

        return {
            'employee_name': full_name,
            'daily_requests': daily_requests,
            'hourly_requests': hourly_requests,
            'added_vacations': added_vacations,
            'balance': balance,
            'report_period': {'start_date': start_date, 'end_date': end_date}
        }

    # ===== وظائف إدارة المستخدمين والصلاحيات =====

    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()

    def create_user(self, username, password, role='employee', full_name='', email='', department=''):
        """إنشاء مستخدم جديد"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # التحقق من عدم وجود المستخدم
            cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
            if cursor.fetchone():
                conn.close()
                return False, "اسم المستخدم موجود بالفعل"

            # تشفير كلمة المرور
            hashed_password = self.hash_password(password)

            cursor.execute('''
                INSERT INTO users (username, password, role, full_name, email, department, is_active)
                VALUES (?, ?, ?, ?, ?, ?, 1)
            ''', (username, hashed_password, role, full_name, email, department))

            user_id = cursor.lastrowid
            conn.commit()
            conn.close()

            # تسجيل العملية
            self.log_activity(None, 'create_user', 'user', str(user_id), f'إنشاء مستخدم جديد: {username}')

            return True, f"تم إنشاء المستخدم {username} بنجاح"

        except Exception as e:
            return False, f"خطأ في إنشاء المستخدم: {str(e)}"

    def update_user(self, user_id, **kwargs):
        """تحديث بيانات المستخدم"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # بناء استعلام التحديث
            update_fields = []
            values = []

            allowed_fields = ['username', 'role', 'full_name', 'email', 'department', 'is_active']

            for field, value in kwargs.items():
                if field in allowed_fields:
                    update_fields.append(f"{field} = ?")
                    values.append(value)
                elif field == 'password':
                    update_fields.append("password = ?")
                    values.append(self.hash_password(value))

            if not update_fields:
                conn.close()
                return False, "لا توجد حقول للتحديث"

            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            values.append(user_id)

            query = f"UPDATE users SET {', '.join(update_fields)} WHERE id = ?"
            cursor.execute(query, values)

            if cursor.rowcount == 0:
                conn.close()
                return False, "المستخدم غير موجود"

            conn.commit()
            conn.close()

            # تسجيل العملية
            self.log_activity(None, 'update_user', 'user', str(user_id), f'تحديث بيانات المستخدم')

            return True, "تم تحديث بيانات المستخدم بنجاح"

        except Exception as e:
            return False, f"خطأ في تحديث المستخدم: {str(e)}"

    def delete_user(self, user_id):
        """حذف مستخدم"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # التحقق من وجود المستخدم
            cursor.execute('SELECT username FROM users WHERE id = ?', (user_id,))
            user = cursor.fetchone()

            if not user:
                conn.close()
                return False, "المستخدم غير موجود"

            username = user[0]

            # منع حذف المدير الافتراضي
            if username == 'admin':
                conn.close()
                return False, "لا يمكن حذف المدير الافتراضي"

            cursor.execute('DELETE FROM users WHERE id = ?', (user_id,))
            cursor.execute('DELETE FROM user_permissions WHERE user_id = ?', (user_id,))

            conn.commit()
            conn.close()

            # تسجيل العملية
            self.log_activity(None, 'delete_user', 'user', str(user_id), f'حذف المستخدم: {username}')

            return True, f"تم حذف المستخدم {username} بنجاح"

        except Exception as e:
            return False, f"خطأ في حذف المستخدم: {str(e)}"

    def get_all_users(self):
        """الحصول على جميع المستخدمين"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, username, role, full_name, email, department, is_active, last_login, created_at
            FROM users
            ORDER BY created_at DESC
        ''')

        users = cursor.fetchall()
        conn.close()

        return users

    def get_user_by_id(self, user_id):
        """الحصول على مستخدم بالمعرف"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, username, role, full_name, email, department, is_active, last_login, created_at
            FROM users WHERE id = ?
        ''', (user_id,))

        user = cursor.fetchone()
        conn.close()

        return user

    def get_user_permissions(self, user_id):
        """الحصول على صلاحيات المستخدم"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # الحصول على دور المستخدم
        cursor.execute('SELECT role FROM users WHERE id = ?', (user_id,))
        user_role = cursor.fetchone()

        if not user_role:
            conn.close()
            return []

        # الحصول على صلاحيات الدور
        cursor.execute('SELECT permissions FROM roles WHERE role_name = ?', (user_role[0],))
        role_permissions = cursor.fetchone()

        permissions = []
        if role_permissions:
            try:
                permissions = json.loads(role_permissions[0])
            except json.JSONDecodeError:
                permissions = []

        # الحصول على الصلاحيات المخصصة
        cursor.execute('''
            SELECT permission_name, granted
            FROM user_permissions
            WHERE user_id = ?
        ''', (user_id,))

        custom_permissions = cursor.fetchall()
        conn.close()

        # دمج الصلاحيات
        final_permissions = set(permissions)

        for perm_name, granted in custom_permissions:
            if granted:
                final_permissions.add(perm_name)
            else:
                final_permissions.discard(perm_name)

        return list(final_permissions)

    def has_permission(self, user_id, permission):
        """التحقق من وجود صلاحية معينة للمستخدم"""
        user_permissions = self.get_user_permissions(user_id)
        return permission in user_permissions

    def grant_permission(self, user_id, permission, granted_by_user_id):
        """منح صلاحية للمستخدم"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO user_permissions
                (user_id, permission_name, granted, granted_by)
                VALUES (?, ?, 1, ?)
            ''', (user_id, permission, granted_by_user_id))

            conn.commit()
            conn.close()

            # تسجيل العملية
            self.log_activity(granted_by_user_id, 'grant_permission', 'user', str(user_id),
                             f'منح صلاحية: {permission}')

            return True, f"تم منح الصلاحية {permission} بنجاح"

        except Exception as e:
            return False, f"خطأ في منح الصلاحية: {str(e)}"

    def revoke_permission(self, user_id, permission, revoked_by_user_id):
        """سحب صلاحية من المستخدم"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO user_permissions
                (user_id, permission_name, granted, granted_by)
                VALUES (?, ?, 0, ?)
            ''', (user_id, permission, revoked_by_user_id))

            conn.commit()
            conn.close()

            # تسجيل العملية
            self.log_activity(revoked_by_user_id, 'revoke_permission', 'user', str(user_id),
                             f'سحب صلاحية: {permission}')

            return True, f"تم سحب الصلاحية {permission} بنجاح"

        except Exception as e:
            return False, f"خطأ في سحب الصلاحية: {str(e)}"

    def get_all_roles(self):
        """الحصول على جميع الأدوار"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM roles ORDER BY role_name')
        roles = cursor.fetchall()
        conn.close()

        return roles

    def create_role(self, role_name, description, permissions):
        """إنشاء دور جديد"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            permissions_json = json.dumps(permissions)

            cursor.execute('''
                INSERT INTO roles (role_name, role_description, permissions)
                VALUES (?, ?, ?)
            ''', (role_name, description, permissions_json))

            conn.commit()
            conn.close()

            return True, f"تم إنشاء الدور {role_name} بنجاح"

        except sqlite3.IntegrityError:
            return False, "اسم الدور موجود بالفعل"
        except Exception as e:
            return False, f"خطأ في إنشاء الدور: {str(e)}"

    def update_role(self, role_name, description=None, permissions=None):
        """تحديث دور"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            update_fields = []
            values = []

            if description is not None:
                update_fields.append("role_description = ?")
                values.append(description)

            if permissions is not None:
                update_fields.append("permissions = ?")
                values.append(json.dumps(permissions))

            if not update_fields:
                conn.close()
                return False, "لا توجد حقول للتحديث"

            values.append(role_name)
            query = f"UPDATE roles SET {', '.join(update_fields)} WHERE role_name = ?"

            cursor.execute(query, values)

            if cursor.rowcount == 0:
                conn.close()
                return False, "الدور غير موجود"

            conn.commit()
            conn.close()

            return True, f"تم تحديث الدور {role_name} بنجاح"

        except Exception as e:
            return False, f"خطأ في تحديث الدور: {str(e)}"

    def log_activity(self, user_id, action, target_type=None, target_id=None, details=None, ip_address=None):
        """تسجيل نشاط المستخدم"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO activity_log (user_id, action, target_type, target_id, details, ip_address)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, action, target_type, target_id, details, ip_address))

            conn.commit()
            conn.close()

        except Exception:
            pass  # تجاهل أخطاء تسجيل النشاط

    def get_activity_log(self, user_id=None, limit=100):
        """الحصول على سجل النشاط"""
        conn = self.get_connection()
        cursor = conn.cursor()

        if user_id:
            cursor.execute('''
                SELECT al.*, u.username
                FROM activity_log al
                LEFT JOIN users u ON al.user_id = u.id
                WHERE al.user_id = ?
                ORDER BY al.timestamp DESC
                LIMIT ?
            ''', (user_id, limit))
        else:
            cursor.execute('''
                SELECT al.*, u.username
                FROM activity_log al
                LEFT JOIN users u ON al.user_id = u.id
                ORDER BY al.timestamp DESC
                LIMIT ?
            ''', (limit,))

        logs = cursor.fetchall()
        conn.close()

        return logs
    
    def get_advanced_employee_report(self, full_name, start_date=None, end_date=None):
        """تقرير شامل عن موظف محدد"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            report = {
                'employee_name': full_name,
                'balance_info': self.get_employee_balance(full_name),
                'daily_requests': [],
                'hourly_requests': [],
                'added_vacations': [],
                'total_days_used': 0,
                'report_period': {'start': start_date, 'end': end_date}
            }
            
            # إضافة شروط التاريخ إذا كانت موجودة
            date_condition = ""
            date_params = [full_name]
            
            if start_date and end_date:
                date_condition = " AND date(created_at) BETWEEN ? AND ?"
                date_params.extend([start_date, end_date])
            elif start_date:
                date_condition = " AND date(created_at) >= ?"
                date_params.append(start_date)
            elif end_date:
                date_condition = " AND date(created_at) <= ?"
                date_params.append(end_date)
            
            # الطلبات اليومية
            cursor.execute(f'''
                SELECT * FROM daily_requests 
                WHERE full_name = ?{date_condition}
                ORDER BY created_at DESC
            ''', date_params)
            report['daily_requests'] = cursor.fetchall()
            
            # الطلبات الساعية
            cursor.execute(f'''
                SELECT * FROM hourly_requests 
                WHERE full_name = ?{date_condition}
                ORDER BY created_at DESC
            ''', date_params)
            report['hourly_requests'] = cursor.fetchall()
            
            # الإجازات المضافة
            cursor.execute(f'''
                SELECT * FROM added_vacations 
                WHERE full_name = ?{date_condition}
                ORDER BY created_at DESC
            ''', date_params)
            report['added_vacations'] = cursor.fetchall()
            
            # حساب إجمالي الأيام المستخدمة
            daily_total = sum(req[6] for req in report['daily_requests'])  # days_count
            hourly_total = sum(req[4] for req in report['hourly_requests'])  # days_equivalent
            report['total_days_used'] = daily_total + hourly_total
            
            conn.close()
            return report
            
        except Exception as e:
            return None
    
    def get_system_statistics(self):
        """إحصائيات النظام العامة"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            stats = {}
            
            # عدد الموظفين
            cursor.execute('SELECT COUNT(DISTINCT full_name) FROM initial_balance')
            stats['total_employees'] = cursor.fetchone()[0]
            
            # إجمالي الرصيد الابتدائي
            cursor.execute('SELECT SUM(days_count) FROM initial_balance')
            stats['total_initial_balance'] = cursor.fetchone()[0] or 0
            
            # إجمالي الإجازات المستخدمة
            cursor.execute('SELECT SUM(days_count) FROM daily_requests')
            daily_used = cursor.fetchone()[0] or 0
            
            cursor.execute('SELECT SUM(days_equivalent) FROM hourly_requests')
            hourly_used = cursor.fetchone()[0] or 0
            
            stats['total_used_days'] = daily_used + hourly_used
            
            # إجمالي الإجازات المضافة
            cursor.execute('SELECT SUM(days_count) FROM added_vacations')
            stats['total_added_days'] = cursor.fetchone()[0] or 0
            
            # عدد الطلبات حسب النوع
            cursor.execute('SELECT COUNT(*) FROM daily_requests')
            stats['total_daily_requests'] = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM hourly_requests')
            stats['total_hourly_requests'] = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM added_vacations')
            stats['total_added_vacations'] = cursor.fetchone()[0]
            
            # متوسط الاستخدام
            if stats['total_employees'] > 0:
                stats['average_usage_per_employee'] = stats['total_used_days'] / stats['total_employees']
            else:
                stats['average_usage_per_employee'] = 0
            
            # الرصيد الإجمالي المتبقي
            stats['total_remaining_balance'] = (stats['total_initial_balance'] + 
                                             stats['total_added_days'] - 
                                             stats['total_used_days'])
            
            conn.close()
            return stats
            
        except Exception as e:
            return None
    
    def backup_database(self, backup_path):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            return True, f"تم إنشاء النسخة الاحتياطية: {backup_path}"
        except Exception as e:
            return False, f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
    
    def restore_database(self, backup_path):
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            import shutil
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, self.db_path)
                return True, "تم استعادة قاعدة البيانات بنجاح"
            else:
                return False, "ملف النسخة الاحتياطية غير موجود"
        except Exception as e:
            return False, f"خطأ في استعادة قاعدة البيانات: {str(e)}"
    
    def export_to_excel(self, export_path, data_type='all'):
        """تصدير البيانات إلى Excel"""
        try:
            import pandas as pd
            
            with pd.ExcelWriter(export_path, engine='openpyxl') as writer:
                
                if data_type in ['all', 'employees']:
                    # تصدير بيانات الموظفين
                    conn = self.get_connection()
                    
                    # الرصيد الابتدائي
                    df_initial = pd.read_sql_query(
                        'SELECT * FROM initial_balance', conn)
                    df_initial.to_excel(writer, sheet_name='الرصيد الابتدائي', index=False)
                    
                    # تقرير شامل لكل موظف
                    employees = self.get_all_employees()
                    employee_reports = []
                    
                    for emp in employees:
                        balance = self.get_employee_balance(emp)
                        employee_reports.append({
                            'الاسم': emp,
                            'الرصيد الابتدائي': balance['initial_balance'],
                            'الإجازات المضافة': balance['added_vacations'],
                            'الإجازات اليومية المستخدمة': balance['daily_used'],
                            'الإجازات الساعية المستخدمة': balance['hourly_used'],
                            'الرصيد المتبقي': balance['net_balance']
                        })
                    
                    df_summary = pd.DataFrame(employee_reports)
                    df_summary.to_excel(writer, sheet_name='ملخص الموظفين', index=False)
                    
                    conn.close()
                
                if data_type in ['all', 'requests']:
                    conn = self.get_connection()
                    
                    # الطلبات اليومية
                    df_daily = pd.read_sql_query(
                        'SELECT * FROM daily_requests ORDER BY created_at DESC', conn)
                    df_daily.to_excel(writer, sheet_name='الطلبات اليومية', index=False)
                    
                    # الطلبات الساعية
                    df_hourly = pd.read_sql_query(
                        'SELECT * FROM hourly_requests ORDER BY created_at DESC', conn)
                    df_hourly.to_excel(writer, sheet_name='الطلبات الساعية', index=False)
                    
                    # الإجازات المضافة
                    df_added = pd.read_sql_query(
                        'SELECT * FROM added_vacations ORDER BY created_at DESC', conn)
                    df_added.to_excel(writer, sheet_name='الإجازات المضافة', index=False)
                    
                    conn.close()
                
                if data_type in ['all', 'statistics']:
                    # إحصائيات النظام
                    stats = self.get_system_statistics()
                    if stats:
                        df_stats = pd.DataFrame([stats])
                        df_stats.to_excel(writer, sheet_name='إحصائيات النظام', index=False)
            
            return True, f"تم تصدير البيانات إلى: {export_path}"
            
        except Exception as e:
            return False, f"خطأ في تصدير البيانات: {str(e)}"
    
    def validate_data_integrity(self):
        """التحقق من سلامة البيانات"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            issues = []
            
            # التحقق من الأسماء المكررة بأشكال مختلفة
            cursor.execute('''
                SELECT full_name, COUNT(*) 
                FROM initial_balance 
                GROUP BY LOWER(TRIM(full_name)) 
                HAVING COUNT(*) > 1
            ''')
            duplicate_names = cursor.fetchall()
            if duplicate_names:
                issues.append(f"أسماء مكررة في الرصيد الابتدائي: {len(duplicate_names)}")
            
            # التحقق من التواريخ غير المنطقية
            cursor.execute('''
                SELECT id, full_name, start_date, end_date 
                FROM daily_requests 
                WHERE start_date > end_date
            ''')
            invalid_dates = cursor.fetchall()
            if invalid_dates:
                issues.append(f"طلبات بتواريخ غير منطقية: {len(invalid_dates)}")
            
            # التحقق من الأرصدة السالبة
            employees_with_negative_balance = []
            employees = self.get_all_employees()
            for emp in employees:
                balance = self.get_employee_balance(emp)
                if balance['net_balance'] < 0:
                    employees_with_negative_balance.append(emp)
            
            if employees_with_negative_balance:
                issues.append(f"موظفون برصيد سالب: {len(employees_with_negative_balance)}")
            
            # التحقق من القيم الفارغة المهمة
            cursor.execute('SELECT COUNT(*) FROM initial_balance WHERE full_name IS NULL OR full_name = ""')
            empty_names = cursor.fetchone()[0]
            if empty_names > 0:
                issues.append(f"سجلات بأسماء فارغة: {empty_names}")
            
            conn.close()
            
            return {
                'is_valid': len(issues) == 0,
                'issues': issues,
                'total_issues': len(issues)
            }
            
        except Exception as e:
            return {
                'is_valid': False,
                'issues': [f"خطأ في التحقق: {str(e)}"],
                'total_issues': 1
            }
    
    def clean_data(self):
        """تنظيف البيانات"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cleaned_items = []
            
            # تنظيف الأسماء (إزالة المسافات الزائدة)
            cursor.execute('UPDATE initial_balance SET full_name = TRIM(full_name)')
            cursor.execute('UPDATE daily_requests SET full_name = TRIM(full_name)')
            cursor.execute('UPDATE hourly_requests SET full_name = TRIM(full_name)')
            cursor.execute('UPDATE added_vacations SET full_name = TRIM(full_name)')
            
            if cursor.rowcount > 0:
                cleaned_items.append(f"تم تنظيف {cursor.rowcount} أسماء")
            
            # حذف السجلات الفارغة
            cursor.execute('DELETE FROM initial_balance WHERE full_name IS NULL OR full_name = ""')
            if cursor.rowcount > 0:
                cleaned_items.append(f"تم حذف {cursor.rowcount} سجل فارغ")
            
            conn.commit()
            conn.close()
            
            return True, f"تم تنظيف البيانات: {'; '.join(cleaned_items)}"
            
        except Exception as e:
            return False, f"خطأ في تنظيف البيانات: {str(e)}"
    
    def archive_year_data(self, year):
        """أرشفة بيانات سنة معينة"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # جمع البيانات المراد أرشفتها
            archive_data = {
                'year': year,
                'daily_requests': [],
                'hourly_requests': [],
                'added_vacations': []
            }
            
            # الطلبات اليومية
            cursor.execute('''
                SELECT * FROM daily_requests 
                WHERE strftime('%Y', created_at) = ?
            ''', (str(year),))
            archive_data['daily_requests'] = cursor.fetchall()
            
            # الطلبات الساعية
            cursor.execute('''
                SELECT * FROM hourly_requests 
                WHERE strftime('%Y', created_at) = ?
            ''', (str(year),))
            archive_data['hourly_requests'] = cursor.fetchall()
            
            # الإجازات المضافة
            cursor.execute('''
                SELECT * FROM added_vacations 
                WHERE strftime('%Y', created_at) = ?
            ''', (str(year),))
            archive_data['added_vacations'] = cursor.fetchall()
            
            # حفظ البيانات في جدول الأرشيف
            import json
            cursor.execute('''
                INSERT INTO archived_data (year, data)
                VALUES (?, ?)
            ''', (year, json.dumps(archive_data, ensure_ascii=False)))
            
            # حذف البيانات المؤرشفة من الجداول الأساسية
            cursor.execute('DELETE FROM daily_requests WHERE strftime("%Y", created_at) = ?', (str(year),))
            cursor.execute('DELETE FROM hourly_requests WHERE strftime("%Y", created_at) = ?', (str(year),))
            cursor.execute('DELETE FROM added_vacations WHERE strftime("%Y", created_at) = ?', (str(year),))
            
            conn.commit()
            conn.close()
            
            total_archived = (len(archive_data['daily_requests']) + 
                            len(archive_data['hourly_requests']) + 
                            len(archive_data['added_vacations']))
            
            return True, f"تم أرشفة {total_archived} سجل لسنة {year}"
            
        except Exception as e:
            return False, f"خطأ في الأرشفة: {str(e)}"
    
    def get_archived_data(self, year):
        """استرجاع البيانات المؤرشفة لسنة معينة"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT data FROM archived_data WHERE year = ?
            ''', (year,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                import json
                return json.loads(result[0])
            return None
            
        except Exception as e:
            return None
    
    def advanced_search(self, search_params):
        """البحث المتقدم"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            results = {}
            
            # البحث في الموظفين
            if 'employee_name' in search_params:
                name = search_params['employee_name']
                cursor.execute('''
                    SELECT DISTINCT full_name FROM initial_balance 
                    WHERE full_name LIKE ? 
                    ORDER BY full_name
                ''', (f'%{name}%',))
                results['employees'] = [row[0] for row in cursor.fetchall()]
            
            # البحث في الطلبات حسب التاريخ
            if 'date_from' in search_params and 'date_to' in search_params:
                date_from = search_params['date_from']
                date_to = search_params['date_to']
                
                # البحث في الطلبات اليومية
                cursor.execute('''
                    SELECT * FROM daily_requests 
                    WHERE start_date BETWEEN ? AND ?
                    ORDER BY start_date DESC
                ''', (date_from, date_to))
                results['daily_requests'] = cursor.fetchall()
                
                # البحث في الطلبات الساعية
                cursor.execute('''
                    SELECT * FROM hourly_requests 
                    WHERE usage_date BETWEEN ? AND ?
                    ORDER BY usage_date DESC
                ''', (date_from, date_to))
                results['hourly_requests'] = cursor.fetchall()
            
            # البحث حسب نوع الإجازة
            if 'vacation_type' in search_params:
                vacation_type = search_params['vacation_type']
                cursor.execute('''
                    SELECT * FROM daily_requests 
                    WHERE vacation_type LIKE ?
                    ORDER BY created_at DESC
                ''', (f'%{vacation_type}%',))
                results['vacation_type_requests'] = cursor.fetchall()
            
            # البحث في الموظفين برصيد منخفض
            if 'low_balance_threshold' in search_params:
                threshold = search_params['low_balance_threshold']
                low_balance_employees = []
                
                employees = self.get_all_employees()
                for emp in employees:
                    balance = self.get_employee_balance(emp)
                    if balance['net_balance'] <= threshold:
                        low_balance_employees.append({
                            'name': emp,
                            'balance': balance['net_balance']
                        })
                
                results['low_balance_employees'] = low_balance_employees
            
            conn.close()
            return results
            
        except Exception as e:
            return {'error': str(e)}
    
    def get_monthly_statistics(self, year, month):
        """إحصائيات شهرية"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # تحديد بداية ونهاية الشهر
            start_date = f"{year}-{month:02d}-01"
            if month == 12:
                end_date = f"{year+1}-01-01"
            else:
                end_date = f"{year}-{month+1:02d}-01"
            
            stats = {
                'year': year,
                'month': month,
                'daily_requests_count': 0,
                'hourly_requests_count': 0,
                'total_days_requested': 0,
                'most_active_employees': []
            }
            
            # عدد الطلبات اليومية
            cursor.execute('''
                SELECT COUNT(*), SUM(days_count) 
                FROM daily_requests 
                WHERE start_date >= ? AND start_date < ?
            ''', (start_date, end_date))
            daily_result = cursor.fetchone()
            stats['daily_requests_count'] = daily_result[0]
            stats['total_days_requested'] += daily_result[1] or 0
            
            # عدد الطلبات الساعية
            cursor.execute('''
                SELECT COUNT(*), SUM(days_equivalent) 
                FROM hourly_requests 
                WHERE usage_date >= ? AND usage_date < ?
            ''', (start_date, end_date))
            hourly_result = cursor.fetchone()
            stats['hourly_requests_count'] = hourly_result[0]
            stats['total_days_requested'] += hourly_result[1] or 0
            
            # أكثر الموظفين نشاطاً
            cursor.execute('''
                SELECT full_name, COUNT(*) as request_count
                FROM (
                    SELECT full_name FROM daily_requests 
                    WHERE start_date >= ? AND start_date < ?
                    UNION ALL
                    SELECT full_name FROM hourly_requests 
                    WHERE usage_date >= ? AND usage_date < ?
                ) 
                GROUP BY full_name 
                ORDER BY request_count DESC 
                LIMIT 10
            ''', (start_date, end_date, start_date, end_date))
            stats['most_active_employees'] = cursor.fetchall()
            
            conn.close()
            return stats
            
        except Exception as e:
            return None
    
    def create_user(self, username, password, role='user'):
        """إنشاء مستخدم جديد"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO users (username, password, role)
                VALUES (?, ?, ?)
            ''', (username, password, role))
            
            conn.commit()
            conn.close()
            return True, "تم إنشاء المستخدم بنجاح"
            
        except sqlite3.IntegrityError:
            return False, "اسم المستخدم موجود بالفعل"
        except Exception as e:
            return False, f"خطأ في إنشاء المستخدم: {str(e)}"
    
    def update_user(self, user_id, username=None, password=None, role=None):
        """تحديث بيانات المستخدم"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            updates = []
            params = []
            
            if username:
                updates.append("username = ?")
                params.append(username)
            if password:
                updates.append("password = ?")
                params.append(password)
            if role:
                updates.append("role = ?")
                params.append(role)
            
            if updates:
                params.append(user_id)
                query = f"UPDATE users SET {', '.join(updates)} WHERE id = ?"
                cursor.execute(query, params)
                
                conn.commit()
                conn.close()
                return True, "تم تحديث المستخدم بنجاح"
            else:
                return False, "لا توجد بيانات للتحديث"
                
        except Exception as e:
            return False, f"خطأ في تحديث المستخدم: {str(e)}"
    
    def delete_user(self, user_id):
        """حذف مستخدم"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM users WHERE id = ?', (user_id,))
            
            if cursor.rowcount > 0:
                conn.commit()
                conn.close()
                return True, "تم حذف المستخدم بنجاح"
            else:
                conn.close()
                return False, "المستخدم غير موجود"
                
        except Exception as e:
            return False, f"خطأ في حذف المستخدم: {str(e)}"
    
    def get_all_users(self):
        """الحصول على جميع المستخدمين"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, username, role, created_at 
                FROM users 
                ORDER BY created_at DESC
            ''')
            
            users = cursor.fetchall()
            conn.close()
            return users
            
        except Exception as e:
            return []
    
    def get_vacation_calendar(self, year, month=None):
        """الحصول على تقويم الإجازات"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            calendar_data = {}
            
            if month:
                # شهر محدد
                start_date = f"{year}-{month:02d}-01"
                if month == 12:
                    end_date = f"{year+1}-01-01"
                else:
                    end_date = f"{year}-{month+1:02d}-01"
                
                date_condition = "AND start_date >= ? AND start_date < ?"
                date_params = [start_date, end_date]
            else:
                # السنة كاملة
                date_condition = "AND strftime('%Y', start_date) = ?"
                date_params = [str(year)]
            
            # الطلبات اليومية
            cursor.execute(f'''
                SELECT full_name, start_date, end_date, days_count, vacation_type
                FROM daily_requests 
                WHERE 1=1 {date_condition}
                ORDER BY start_date
            ''', date_params)
            
            daily_requests = cursor.fetchall()
            
            # الطلبات الساعية
            cursor.execute(f'''
                SELECT full_name, usage_date, hours_count, days_equivalent
                FROM hourly_requests 
                WHERE 1=1 {date_condition.replace('start_date', 'usage_date')}
                ORDER BY usage_date
            ''', date_params)
            
            hourly_requests = cursor.fetchall()
            
            # تنظيم البيانات حسب التاريخ
            for req in daily_requests:
                date = req[1]  # start_date
                if date not in calendar_data:
                    calendar_data[date] = {'daily': [], 'hourly': []}
                calendar_data[date]['daily'].append({
                    'name': req[0],
                    'start_date': req[1],
                    'end_date': req[2],
                    'days': req[3],
                    'type': req[4]
                })
            
            for req in hourly_requests:
                date = req[1]  # usage_date
                if date not in calendar_data:
                    calendar_data[date] = {'daily': [], 'hourly': []}
                calendar_data[date]['hourly'].append({
                    'name': req[0],
                    'date': req[1],
                    'hours': req[2],
                    'days_equivalent': req[3]
                })
            
            conn.close()
            return calendar_data
            
        except Exception as e:
            return {}
    
    def get_overlapping_vacations(self, start_date, end_date):
        """البحث عن الإجازات المتداخلة"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT full_name, start_date, end_date, days_count, vacation_type
                FROM daily_requests 
                WHERE (start_date <= ? AND end_date >= ?) 
                   OR (start_date <= ? AND end_date >= ?)
                   OR (start_date >= ? AND end_date <= ?)
                ORDER BY start_date
            ''', (end_date, start_date, start_date, end_date, start_date, end_date))
            
            overlapping = cursor.fetchall()
            conn.close()
            
            return overlapping
            
        except Exception as e:
            return []
    
    def calculate_working_days(self, start_date, end_date, exclude_weekends=True):
        """حساب أيام العمل بين تاريخين"""
        try:
            from datetime import datetime, timedelta
            
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            
            working_days = 0
            current_date = start_dt
            
            while current_date <= end_dt:
                if exclude_weekends:
                    # استبعاد الجمعة (4) والسبت (5)
                    if current_date.weekday() not in [4, 5]:
                        working_days += 1
                else:
                    working_days += 1
                    
                current_date += timedelta(days=1)
            
            return working_days
            
        except Exception as e:
            return 0
    
    def get_employees_by_department(self, department):
        """الحصول على الموظفين حسب القسم"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT DISTINCT full_name, job_title, department
                FROM daily_requests 
                WHERE department LIKE ?
                ORDER BY full_name
            ''', (f'%{department}%',))
            
            employees = cursor.fetchall()
            conn.close()
            
            return employees
            
        except Exception as e:
            return []
    
    def get_vacation_types_statistics(self):
        """إحصائيات أنواع الإجازات"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT vacation_type, COUNT(*) as count, SUM(days_count) as total_days
                FROM daily_requests 
                GROUP BY vacation_type 
                ORDER BY count DESC
            ''')
            
            vacation_stats = cursor.fetchall()
            conn.close()
            
            return vacation_stats
            
        except Exception as e:
            return []
    
    def update_request(self, request_id, request_type, **kwargs):
        """تحديث طلب موجود"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            if request_type == 'daily':
                table = 'daily_requests'
                allowed_fields = ['full_name', 'employee_id', 'job_title', 'department', 
                                'vacation_type', 'start_date', 'days_count', 'end_date']
            elif request_type == 'hourly':
                table = 'hourly_requests'
                allowed_fields = ['full_name', 'usage_date', 'hours_count', 'days_equivalent']
            elif request_type == 'added':
                table = 'added_vacations'
                allowed_fields = ['full_name', 'date', 'days_count', 'reason']
            else:
                return False, "نوع الطلب غير صحيح"
            
            # إعداد الاستعلام
            updates = []
            params = []
            
            for field, value in kwargs.items():
                if field in allowed_fields:
                    updates.append(f"{field} = ?")
                    params.append(value)
            
            if not updates:
                return False, "لا توجد حقول صالحة للتحديث"
            
            params.append(request_id)
            query = f"UPDATE {table} SET {', '.join(updates)} WHERE id = ?"
            
            cursor.execute(query, params)
            
            if cursor.rowcount > 0:
                conn.commit()
                conn.close()
                return True, "تم تحديث الطلب بنجاح"
            else:
                conn.close()
                return False, "الطلب غير موجود"
                
        except Exception as e:
            return False, f"خطأ في تحديث الطلب: {str(e)}"
    
    def get_employee_vacation_history(self, full_name, limit=None):
        """سجل إجازات الموظف"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            history = {
                'employee_name': full_name,
                'daily_requests': [],
                'hourly_requests': [],
                'added_vacations': []
            }
            
            # الطلبات اليومية
            query = 'SELECT * FROM daily_requests WHERE full_name = ? ORDER BY created_at DESC'
            if limit:
                query += f' LIMIT {limit}'
            cursor.execute(query, (full_name,))
            history['daily_requests'] = cursor.fetchall()
            
            # الطلبات الساعية
            query = 'SELECT * FROM hourly_requests WHERE full_name = ? ORDER BY created_at DESC'
            if limit:
                query += f' LIMIT {limit}'
            cursor.execute(query, (full_name,))
            history['hourly_requests'] = cursor.fetchall()
            
            # الإجازات المضافة
            query = 'SELECT * FROM added_vacations WHERE full_name = ? ORDER BY created_at DESC'
            if limit:
                query += f' LIMIT {limit}'
            cursor.execute(query, (full_name,))
            history['added_vacations'] = cursor.fetchall()
            
            conn.close()
            return history
            
        except Exception as e:
            return None
    
    def bulk_import_requests(self, excel_file, request_type='daily'):
        """استيراد مجموعي للطلبات من Excel"""
        try:
            df = pd.read_excel(excel_file)
            conn = self.get_connection()
            cursor = conn.cursor()
            
            success_count = 0
            error_count = 0
            errors = []
            
            for index, row in df.iterrows():
                try:
                    if request_type == 'daily':
                        # حساب تاريخ النهاية
                        start_dt = datetime.strptime(str(row['تاريخ البداية']), '%Y-%m-%d')
                        end_dt = start_dt + timedelta(days=int(row['عدد الأيام']) - 1)
                        end_date = end_dt.strftime('%Y-%m-%d')
                        
                        cursor.execute('''
                            INSERT INTO daily_requests 
                            (full_name, employee_id, job_title, department, vacation_type, 
                             start_date, days_count, end_date)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (row['الاسم'], row.get('رقم الموظف', ''), row.get('المسمى الوظيفي', ''),
                              row.get('القسم', ''), row['نوع الإجازة'], row['تاريخ البداية'],
                              row['عدد الأيام'], end_date))
                              
                    elif request_type == 'hourly':
                        days_equivalent = (int(row['عدد الساعات']) * 3) / 24
                        cursor.execute('''
                            INSERT INTO hourly_requests 
                            (full_name, usage_date, hours_count, days_equivalent)
                            VALUES (?, ?, ?, ?)
                        ''', (row['الاسم'], row['تاريخ الاستخدام'], 
                              row['عدد الساعات'], days_equivalent))
                    
                    success_count += 1
                    
                except Exception as e:
                    error_count += 1
                    errors.append(f"السطر {index + 2}: {str(e)}")
            
            conn.commit()
            conn.close()
            
            result_msg = f"تم استيراد {success_count} طلب بنجاح"
            if error_count > 0:
                result_msg += f"، {error_count} أخطاء"
            
            return True, result_msg, errors
            
        except Exception as e:
            return False, f"خطأ في الاستيراد: {str(e)}", []
    
    def generate_balance_alerts(self, threshold=5):
        """تنبيهات الرصيد المنخفض"""
        try:
            alerts = []
            employees = self.get_all_employees()
            
            for emp in employees:
                balance = self.get_employee_balance(emp)
                if balance['net_balance'] <= threshold:
                    alerts.append({
                        'employee_name': emp,
                        'current_balance': balance['net_balance'],
                        'alert_level': 'critical' if balance['net_balance'] <= 0 else 'warning'
                    })
            
            return alerts
            
        except Exception as e:
            return []
    
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # إعادة بناء قاعدة البيانات
            cursor.execute('VACUUM')
            
            # تحليل الجداول
            cursor.execute('ANALYZE')
            
            # إنشاء فهارس لتحسين الأداء
            indexes = [
                'CREATE INDEX IF NOT EXISTS idx_daily_requests_name ON daily_requests(full_name)',
                'CREATE INDEX IF NOT EXISTS idx_daily_requests_date ON daily_requests(start_date)',
                'CREATE INDEX IF NOT EXISTS idx_hourly_requests_name ON hourly_requests(full_name)',
                'CREATE INDEX IF NOT EXISTS idx_hourly_requests_date ON hourly_requests(usage_date)',
                'CREATE INDEX IF NOT EXISTS idx_initial_balance_name ON initial_balance(full_name)',
                'CREATE INDEX IF NOT EXISTS idx_added_vacations_name ON added_vacations(full_name)'
            ]
            
            for index_sql in indexes:
                cursor.execute(index_sql)
            
            conn.commit()
            conn.close()
            
            return True, "تم تحسين قاعدة البيانات بنجاح"
            
        except Exception as e:
            return False, f"خطأ في تحسين قاعدة البيانات: {str(e)}"
    
    def get_database_size(self):
        """الحصول على حجم قاعدة البيانات"""
        try:
            size = os.path.getsize(self.db_path)
            
            # تحويل إلى وحدات مناسبة
            if size < 1024:
                return f"{size} بايت"
            elif size < 1024 * 1024:
                return f"{size / 1024:.2f} كيلوبايت"
            else:
                return f"{size / (1024 * 1024):.2f} ميجابايت"
                
        except Exception as e:
            return "غير محدد"
    
    def close(self):
        """إغلاق قاعدة البيانات بشكل آمن"""
        try:
            # لا يوجد اتصال مستمر للإغلاق في هذا التصميم
            return True, "تم إغلاق قاعدة البيانات"
        except Exception as e:
            return False, f"خطأ في إغلاق قاعدة البيانات: {str(e)}"
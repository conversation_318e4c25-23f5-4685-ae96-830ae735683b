// qcameracontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCameraControl : public QMediaControl
{
%TypeHeaderCode
#include <qcameracontrol.h>
%End

public:
    enum PropertyChangeType
    {
        CaptureMode,
        ImageEncodingSettings,
        VideoEncodingSettings,
        Viewfinder,
        ViewfinderSettings,
    };

    virtual ~QCameraControl();
    virtual QCamera::State state() const = 0;
    virtual void setState(QCamera::State state) = 0;
    virtual QCamera::Status status() const = 0;
    virtual QCamera::CaptureModes captureMode() const = 0;
    virtual void setCaptureMode(QCamera::CaptureModes) = 0;
    virtual bool isCaptureModeSupported(QCamera::CaptureModes mode) const = 0;
    virtual bool canChangeProperty(QCameraControl::PropertyChangeType changeType, QCamera::Status status) const = 0;

signals:
    void stateChanged(QCamera::State);
    void statusChanged(QCamera::Status);
    void error(int error, const QString &errorString);
    void captureModeChanged(QCamera::CaptureModes mode);

protected:
    explicit QCameraControl(QObject *parent /TransferThis/ = 0);
};

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
النظام الشامل المحسّن لإدارة الإجازات
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# إضافة المسار الحالي
sys.path.append(os.path.dirname(__file__))

# استيراد النوافذ المحسّنة
from إصلاح_مقاسات_النوافذ import OptimizedLoginWindow, get_screen_info, calculate_optimal_sizes
from نوافذ_محسّنة_شاملة import (
    DailyVacationWindow, HourlyVacationWindow, 
    SearchEditWindow, ReportsWindow
)

class EnhancedMainWindow(QMainWindow):
    """النافذة الرئيسية المحسّنة الشاملة"""
    
    def __init__(self, user_data=None):
        super().__init__()
        self.user_data = user_data
        self.init_ui()
        
    def init_ui(self):
        """إعداد الواجهة الرئيسية"""
        sizes = calculate_optimal_sizes()
        main_size = sizes['main_window']
        
        self.setWindowTitle("🏢 نظام إدارة الإجازات - الإصدار الشامل المحسّن")
        self.setGeometry(50, 50, main_size['width'], main_size['height'])
        self.setMinimumSize(main_size['min_width'], main_size['min_height'])
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - main_size['width']) // 2
        y = (screen.height() - main_size['height']) // 2
        self.move(x, y)
        
        # إعداد القوائم
        self.create_menu_bar()
        
        # إعداد شريط الأدوات
        self.create_toolbar()
        
        # إعداد الواجهة المركزية
        self.setup_central_widget()
        
        # إعداد شريط الحالة
        self.setup_status_bar()
        
        # الأنماط العامة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
        
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        menubar.setStyleSheet("""
            QMenuBar {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2c3e50, stop: 1 #34495e);
                color: white;
                border: none;
                font-weight: bold;
                font-size: 14px;
                padding: 5px;
            }
            QMenuBar::item {
                background: transparent;
                padding: 8px 15px;
                border-radius: 5px;
                margin: 2px;
            }
            QMenuBar::item:selected {
                background: rgba(255, 255, 255, 0.2);
            }
            QMenu {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 5px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 5px;
                margin: 1px;
            }
            QMenu::item:selected {
                background: #3498db;
                color: white;
            }
        """)
        
        # قائمة الملف
        file_menu = menubar.addMenu('📁 ملف')
        file_menu.addAction('📥 استيراد بيانات', self.import_data)
        file_menu.addAction('📤 تصدير بيانات', self.export_data)
        file_menu.addSeparator()
        file_menu.addAction('🚪 خروج', self.close)
        
        # قائمة الطلبات
        requests_menu = menubar.addMenu('📝 الطلبات')
        requests_menu.addAction('📝 طلب إجازة يومية', self.show_daily_vacation)
        requests_menu.addAction('⏱️ طلب إجازة ساعية', self.show_hourly_vacation)
        requests_menu.addSeparator()
        requests_menu.addAction('🔍 البحث والتعديل', self.show_search_edit)
        
        # قائمة التقارير
        reports_menu = menubar.addMenu('📊 التقارير')
        reports_menu.addAction('📊 تقارير شاملة', self.show_reports)
        reports_menu.addAction('📈 إحصائيات سريعة', self.show_quick_stats)
        
        # قائمة الإدارة
        admin_menu = menubar.addMenu('⚙️ إدارة')
        admin_menu.addAction('👥 إدارة المستخدمين', self.manage_users)
        admin_menu.addAction('🔧 إعدادات النظام', self.system_settings)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu('❓ مساعدة')
        help_menu.addAction('📖 دليل المستخدم', self.show_user_guide)
        help_menu.addAction('ℹ️ حول البرنامج', self.show_about)
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar('أدوات سريعة')
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        toolbar.setStyleSheet("""
            QToolBar {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ecf0f1, stop: 1 #bdc3c7);
                border: none;
                border-bottom: 2px solid #95a5a6;
                padding: 5px;
                spacing: 5px;
            }
            QToolButton {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px;
                margin: 2px;
                font-weight: bold;
                color: #2c3e50;
            }
            QToolButton:hover {
                background: #e8f4fd;
                border: 2px solid #3498db;
            }
            QToolButton:pressed {
                background: #3498db;
                color: white;
            }
        """)
        
        # إضافة الأدوات
        toolbar.addAction('📝 إجازة يومية', self.show_daily_vacation)
        toolbar.addAction('⏱️ إجازة ساعية', self.show_hourly_vacation)
        toolbar.addSeparator()
        toolbar.addAction('🔍 بحث', self.show_search_edit)
        toolbar.addAction('📊 تقارير', self.show_reports)
        toolbar.addSeparator()
        toolbar.addAction('🔄 تحديث', self.refresh_data)
        
    def setup_central_widget(self):
        """إعداد الواجهة المركزية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # العنوان الترحيبي
        if self.user_data:
            welcome_text = f"مرحباً {self.user_data.get('full_name', 'المستخدم')}"
        else:
            welcome_text = "مرحباً بك في النظام"
            
        welcome_label = QLabel(f"🏢 {welcome_text}")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 20px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2c3e50, stop: 1 #34495e);
                border-radius: 12px;
                padding: 20px;
                margin: 10px;
            }
        """)
        layout.addWidget(welcome_label)
        
        # إحصائيات سريعة
        self.create_quick_stats(layout)
        
        # الوظائف الرئيسية
        self.create_main_functions(layout)
        
    def create_quick_stats(self, layout):
        """إنشاء إحصائيات سريعة"""
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 12px;
                padding: 15px;
            }
        """)
        
        stats_layout = QGridLayout(stats_frame)
        stats_layout.setSpacing(10)
        
        # بيانات الإحصائيات
        stats_data = [
            ("📊 الطلبات اليوم", "12", "#3498db"),
            ("✅ المقبولة", "8", "#27ae60"),
            ("❌ المرفوضة", "2", "#e74c3c"),
            ("⏳ معلقة", "2", "#f39c12"),
            ("👥 الموظفين", "45", "#9b59b6"),
            ("📅 هذا الشهر", "156", "#34495e")
        ]
        
        for i, (title, value, color) in enumerate(stats_data):
            stat_card = self.create_stat_card(title, value, color)
            row = i // 3
            col = i % 3
            stats_layout.addWidget(stat_card, row, col)
        
        layout.addWidget(stats_frame)
        
    def create_stat_card(self, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 {color}, stop: 1 rgba(0,0,0,0.1));
                border-radius: 8px;
                padding: 10px;
                margin: 2px;
            }}
        """)
        
        card_layout = QVBoxLayout(card)
        card_layout.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 24px;
                font-weight: bold;
                color: white;
            }
        """)
        
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: white;
            }
        """)
        
        card_layout.addWidget(value_label)
        card_layout.addWidget(title_label)
        
        return card
        
    def create_main_functions(self, layout):
        """إنشاء الوظائف الرئيسية"""
        functions_frame = QFrame()
        functions_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        functions_layout = QGridLayout(functions_frame)
        functions_layout.setSpacing(15)
        
        # الوظائف الرئيسية
        functions = [
            ("📝 طلب إجازة يومية", "إنشاء طلب إجازة لعدة أيام", "#3498db", self.show_daily_vacation),
            ("⏱️ طلب إجازة ساعية", "إنشاء طلب إجازة لساعات محددة", "#e67e22", self.show_hourly_vacation),
            ("🔍 البحث والتعديل", "البحث في الطلبات وتعديلها", "#9b59b6", self.show_search_edit),
            ("📊 التقارير", "عرض التقارير والإحصائيات", "#27ae60", self.show_reports),
            ("👥 إدارة المستخدمين", "إدارة حسابات المستخدمين", "#e74c3c", self.manage_users),
            ("⚙️ إعدادات النظام", "تخصيص إعدادات النظام", "#34495e", self.system_settings)
        ]
        
        for i, (title, description, color, callback) in enumerate(functions):
            button = self.create_function_button(title, description, color, callback)
            row = i // 2
            col = i % 2
            functions_layout.addWidget(button, row, col)
        
        layout.addWidget(functions_frame)
        
    def create_function_button(self, title, description, color, callback):
        """إنشاء زر وظيفة"""
        button = QPushButton()
        button.setFixedHeight(80)
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color}, stop: 1 rgba(0,0,0,0.1));
                border: none;
                border-radius: 10px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-weight: bold;
                text-align: left;
                padding: 15px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 rgba(255,255,255,0.2), stop: 1 {color});
            }}
            QPushButton:pressed {{
                background: {color};
            }}
        """)
        
        button.setText(f"{title}\n{description}")
        button.clicked.connect(callback)
        
        return button
        
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()
        status_bar.setStyleSheet("""
            QStatusBar {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ecf0f1, stop: 1 #bdc3c7);
                border-top: 2px solid #95a5a6;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)
        
        # معلومات المستخدم
        if self.user_data:
            user_info = f"👤 {self.user_data.get('full_name', 'مستخدم')} | {self.user_data.get('role', 'مستخدم')}"
        else:
            user_info = "👤 غير محدد"
            
        status_bar.addPermanentWidget(QLabel(user_info))
        
        # الوقت
        self.time_label = QLabel()
        self.update_time()
        status_bar.addPermanentWidget(self.time_label)
        
        # مؤقت لتحديث الوقت
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # تحديث كل ثانية
        
        status_bar.showMessage("النظام جاهز للاستخدام ✨")
        
    def update_time(self):
        """تحديث الوقت"""
        from datetime import datetime
        current_time = datetime.now().strftime("🕒 %Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
        
    # الوظائف الرئيسية
    def show_daily_vacation(self):
        """عرض نافذة طلب الإجازة اليومية"""
        window = DailyVacationWindow()
        window.exec_()
        
    def show_hourly_vacation(self):
        """عرض نافذة طلب الإجازة الساعية"""
        window = HourlyVacationWindow()
        window.exec_()
        
    def show_search_edit(self):
        """عرض نافذة البحث والتعديل"""
        window = SearchEditWindow()
        window.exec_()
        
    def show_reports(self):
        """عرض نافذة التقارير"""
        window = ReportsWindow()
        window.exec_()
        
    def manage_users(self):
        """إدارة المستخدمين/الموظفين"""
        try:
            from إدارة_الموظفين import EmployeeManagementWindow
            window = EmployeeManagementWindow()
            window.exec_()
        except ImportError:
            QMessageBox.warning(self, "خطأ", "نافذة إدارة الموظفين غير متاحة!")
        
    def system_settings(self):
        """إعدادات النظام"""
        QMessageBox.information(self, "⚙️ إعدادات النظام", 
                              "سيتم فتح نافذة الإعدادات قريباً...")
        
    def import_data(self):
        """استيراد البيانات"""
        QMessageBox.information(self, "📥 استيراد البيانات", 
                              "سيتم فتح نافذة استيراد البيانات قريباً...")
        
    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(self, "📤 تصدير البيانات", 
                              "سيتم فتح نافذة تصدير البيانات قريباً...")
        
    def show_quick_stats(self):
        """عرض إحصائيات سريعة"""
        QMessageBox.information(self, "📈 إحصائيات سريعة", 
                              "📊 إحصائيات النظام:\n\n" +
                              "• إجمالي الطلبات: 156\n" +
                              "• المقبولة: 98 (63%)\n" +
                              "• المرفوضة: 15 (10%)\n" +
                              "• المعلقة: 14 (9%)\n" +
                              "• عدد الموظفين: 45")
        
    def show_user_guide(self):
        """عرض دليل المستخدم"""
        QMessageBox.information(self, "📖 دليل المستخدم", 
                              "📚 دليل استخدام النظام:\n\n" +
                              "1. استخدم القوائم العلوية للوصول للوظائف\n" +
                              "2. انقر على الأزرار الرئيسية لتنفيذ المهام\n" +
                              "3. استخدم شريط الأدوات للوصول السريع\n" +
                              "4. راجع الإحصائيات في الصفحة الرئيسية")
        
    def show_about(self):
        """عرض معلومات البرنامج"""
        QMessageBox.about(self, "ℹ️ حول البرنامج",
                         "🏢 نظام إدارة الإجازات\n" +
                         "الإصدار الشامل المحسّن v2.0\n\n" +
                         "✨ المميزات:\n" +
                         "• واجهة عربية حديثة\n" +
                         "• مقاسات متكيّفة\n" +
                         "• تصميم احترافي\n" +
                         "• سهولة الاستخدام\n\n" +
                         "© 2024 - جميع الحقوق محفوظة")
        
    def refresh_data(self):
        """تحديث البيانات"""
        self.statusBar().showMessage("جاري تحديث البيانات...", 2000)
        QTimer.singleShot(1000, lambda: self.statusBar().showMessage("تم تحديث البيانات ✅", 3000))

def main():
    """الدالة الرئيسية للتشغيل"""
    app = QApplication(sys.argv)
    
    # معلومات النظام
    screen_info = get_screen_info()
    sizes = calculate_optimal_sizes()
    
    print("🚀 تشغيل النظام الشامل المحسّن...")
    print("=" * 60)
    print(f"📺 دقة الشاشة: {screen_info['width']} × {screen_info['height']}")
    print(f"📏 النافذة الرئيسية: {sizes['main_window']['width']} × {sizes['main_window']['height']}")
    print("=" * 60)
    
    # تطبيق الخط العربي
    font = QFont("Sakkal Majalla", 10)
    app.setFont(font)
    
    print("🔐 فتح نافذة تسجيل الدخول...")
    
    # نافذة تسجيل الدخول
    login_window = OptimizedLoginWindow()
    
    if login_window.exec_() == QDialog.Accepted:
        print("✅ تم تسجيل الدخول بنجاح!")
        print("🏢 فتح النظام الرئيسي الشامل...")
        
        # النافذة الرئيسية المحسّنة
        main_window = EnhancedMainWindow(login_window.user_data)
        main_window.show()
        
        print("🎉 النظام الشامل جاهز للاستخدام!")
        print("\n📋 الوظائف المتاحة:")
        print("   📝 طلبات الإجازات (يومية وساعية)")
        print("   🔍 البحث والتعديل المتقدم")
        print("   📊 التقارير والإحصائيات الشاملة")
        print("   👥 إدارة المستخدمين")
        print("   ⚙️ إعدادات النظام")
        print("   📁 استيراد وتصدير البيانات")
        
        return app.exec_()
    else:
        print("❌ تم إلغاء تسجيل الدخول")
        return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except Exception as e:
        print(f"❌ حدث خطأ: {e}")
        input("اضغط Enter للإغلاق...")
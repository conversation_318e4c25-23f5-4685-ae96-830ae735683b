#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة الإجازات
Test script for the vacation management system
"""

from database import VacationDatabase
from datetime import datetime, timedelta
import os

def test_vacation_system():
    """اختبار شامل لنظام إدارة الإجازات"""
    print("🚀 بدء اختبار نظام إدارة الإجازات")
    print("=" * 50)
    
    # إنشاء قاعدة البيانات
    db = VacationDatabase('test_vacation.db')
    print("✅ تم إنشاء قاعدة البيانات بنجاح")
    
    # اختبار تسجيل الدخول
    print("\n📋 اختبار تسجيل الدخول:")
    user = db.authenticate_user('admin', 'admin123')
    if user:
        print(f"✅ تم تسجيل الدخول بنجاح: {user['username']} ({user['role']})")
    else:
        print("❌ فشل في تسجيل الدخول")
        return
    
    # اختبار استيراد الرصيد الابتدائي
    print("\n📊 اختبار استيراد الرصيد الابتدائي:")
    excel_file = 'نموذج_الرصيد_الابتدائي.xlsx'
    if os.path.exists(excel_file):
        success, message = db.import_initial_balance(excel_file)
        print(f"{'✅' if success else '❌'} {message}")
    else:
        print("⚠️ ملف Excel غير موجود، سيتم إضافة بيانات تجريبية")
        # إضافة بيانات تجريبية
        test_employees = [
            ('أحمد محمد علي', 'موظف', 30, '2024-01-01'),
            ('فاطمة عبد الله', 'مشرف', 25, '2024-01-01'),
            ('محمد عبد الرحمن', 'موظف', 30, '2024-01-01')
        ]
        
        for name, rank, days, date in test_employees:
            success, message = db.import_initial_balance_data(name, rank, days, date)
            print(f"{'✅' if success else '❌'} إضافة {name}: {message}")
    
    # اختبار البحث عن الموظفين
    print("\n🔍 اختبار البحث عن الموظفين:")
    search_results = db.search_employee('أحمد')
    print(f"نتائج البحث عن 'أحمد': {search_results}")
    
    # اختبار إضافة طلب إجازة يومية
    print("\n📅 اختبار إضافة طلب إجازة يومية:")
    if search_results:
        employee_name = search_results[0]
        success, message = db.add_daily_request(
            full_name=employee_name,
            employee_id='EMP001',
            job_title='موظف',
            department='الإدارة',
            vacation_type='إجازة اعتيادية',
            start_date='2024-07-15',
            days_count=5
        )
        print(f"{'✅' if success else '❌'} {message}")
    
    # اختبار إضافة طلب إجازة ساعية
    print("\n⏰ اختبار إضافة طلب إجازة ساعية:")
    if search_results:
        employee_name = search_results[0]
        success, message = db.add_hourly_request(
            full_name=employee_name,
            usage_date='2024-07-20',
            hours_count=4
        )
        print(f"{'✅' if success else '❌'} {message}")
    
    # اختبار إضافة إجازة مدرجة
    print("\n➕ اختبار إضافة إجازة مدرجة:")
    if search_results:
        employee_name = search_results[0]
        success, message = db.add_vacation(
            full_name=employee_name,
            date='2024-07-25',
            days_count=3,
            reason='إجازة إضافية'
        )
        print(f"{'✅' if success else '❌'} {message}")
    
    # اختبار حساب الرصيد
    print("\n💰 اختبار حساب رصيد الموظف:")
    if search_results:
        employee_name = search_results[0]
        balance = db.get_employee_balance(employee_name)
        print(f"رصيد الموظف {employee_name}:")
        print(f"  - الرصيد الابتدائي: {balance['initial_balance']} يوم")
        print(f"  - الإجازات المدرجة: {balance['added_vacations']} يوم")
        print(f"  - الإجازات اليومية المستفادة: {balance['daily_used']} يوم")
        print(f"  - الإجازات الساعية المستفادة: {balance['hourly_used']:.2f} يوم")
        print(f"  - الرصيد الصافي: {balance['net_balance']:.2f} يوم")
    
    # اختبار الحصول على جميع الموظفين
    print("\n👥 اختبار الحصول على قائمة الموظفين:")
    all_employees = db.get_all_employees()
    print(f"عدد الموظفين: {len(all_employees)}")
    for i, emp in enumerate(all_employees[:5], 1):  # عرض أول 5 موظفين
        print(f"  {i}. {emp}")
    
    # اختبار الحصول على الطلبات
    print("\n📋 اختبار الحصول على الطلبات:")
    daily_requests = db.get_requests_by_type('daily')
    hourly_requests = db.get_requests_by_type('hourly')
    added_vacations = db.get_requests_by_type('added')
    
    print(f"  - الطلبات اليومية: {len(daily_requests)}")
    print(f"  - الطلبات الساعية: {len(hourly_requests)}")
    print(f"  - الإجازات المدرجة: {len(added_vacations)}")
    
    print("\n🎉 تم الانتهاء من جميع الاختبارات بنجاح!")
    print("=" * 50)
    
    # تنظيف ملف الاختبار
    if os.path.exists('test_vacation.db'):
        os.remove('test_vacation.db')
        print("🧹 تم حذف ملف قاعدة البيانات التجريبية")

if __name__ == "__main__":
    test_vacation_system()

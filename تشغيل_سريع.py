#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل سريع للنظام مع إدارة الموظفين وأنواع الإجازات والتوجيه العربي
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class QuickStartWindow(QMainWindow):
    """نافذة التشغيل السريع"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة التشغيل السريع"""
        self.setWindowTitle("🚀 تشغيل سريع - نظام إدارة الإجازات")
        self.setGeometry(100, 100, 700, 500)
        
        # تطبيق التوجيه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - 700) // 2
        y = (screen.height() - 500) // 2
        self.move(x, y)
        
        # الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # العنوان
        title_label = QLabel("🚀 نظام إدارة الإجازات - تشغيل سريع")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 24px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #e74c3c, stop: 1 #c0392b);
                border-radius: 15px;
                padding: 25px;
                margin: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات النظام
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 12px;
                padding: 20px;
            }
        """)
        info_layout = QVBoxLayout(info_frame)
        
        info_title = QLabel("🆕 الميزات الجديدة:")
        info_title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        info_layout.addWidget(info_title)
        
        features_text = QLabel("""
✅ إدارة شاملة للموظفين مع قوائم منسدلة قابلة للتعديل
✅ إدارة أنواع الإجازات مع إمكانية الإضافة والحذف
✅ التوجيه العربي الكامل لجميع القوائم والنوافذ
✅ حفظ تلقائي للبيانات في ملفات JSON
✅ واجهة حديثة وسهلة الاستخدام
✅ مقاسات ذكية متكيفة مع دقة الشاشة
        """)
        features_text.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 14px;
                color: #34495e;
                line-height: 1.6;
                padding: 10px;
            }
        """)
        info_layout.addWidget(features_text)
        
        layout.addWidget(info_frame)
        
        # أزرار التشغيل
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 12px;
                padding: 20px;
            }
        """)
        buttons_layout = QGridLayout(buttons_frame)
        buttons_layout.setSpacing(15)
        
        # أزرار النظام
        buttons = [
            ("🏢 النظام الكامل", "تشغيل النظام الشامل المحسّن", "#2c3e50", self.run_full_system),
            ("👥 إدارة الموظفين", "إدارة قائمة الموظفين فقط", "#3498db", self.manage_employees),
            ("📋 إدارة أنواع الإجازات", "إدارة أنواع الإجازات فقط", "#8e44ad", self.manage_vacation_types),
            ("🧪 اختبار النظام", "نافذة اختبار شاملة", "#e67e22", self.test_system)
        ]
        
        for i, (title, description, color, callback) in enumerate(buttons):
            button = self.create_button(title, description, color, callback)
            row = i // 2
            col = i % 2
            buttons_layout.addWidget(button, row, col)
        
        layout.addWidget(buttons_frame)
        
        # معلومات الحالة
        status_label = QLabel("📊 النظام جاهز للتشغيل مع جميع المكونات المحدثة")
        status_label.setAlignment(Qt.AlignCenter)
        status_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 14px;
                color: #27ae60;
                background: #d5f4e6;
                border: 1px solid #27ae60;
                border-radius: 8px;
                padding: 10px;
                margin: 10px 0px;
            }
        """)
        layout.addWidget(status_label)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
    
    def create_button(self, title, description, color, callback):
        """إنشاء زر للنظام"""
        button = QPushButton()
        button.setFixedHeight(90)
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color}, stop: 1 rgba(0,0,0,0.1));
                border: none;
                border-radius: 12px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-weight: bold;
                text-align: center;
                padding: 15px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 rgba(255,255,255,0.2), stop: 1 {color});
            }}
            QPushButton:pressed {{
                background: {color};
            }}
        """)
        
        button.setText(f"{title}\n{description}")
        button.clicked.connect(callback)
        
        return button
    
    def run_full_system(self):
        """تشغيل النظام الكامل"""
        try:
            from النظام_الشامل_المحسّن import main as run_system
            self.hide()
            run_system()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تشغيل النظام:\n{e}")
    
    def manage_employees(self):
        """إدارة الموظفين"""
        try:
            from إدارة_الموظفين import EmployeeManagementWindow
            window = EmployeeManagementWindow()
            window.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إدارة الموظفين:\n{e}")
    
    def manage_vacation_types(self):
        """إدارة أنواع الإجازات"""
        try:
            from إدارة_أنواع_الإجازات import VacationTypesManagementWindow
            window = VacationTypesManagementWindow()
            window.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إدارة أنواع الإجازات:\n{e}")
    
    def test_system(self):
        """اختبار النظام"""
        try:
            from اختبار_إدارة_الموظفين import TestMainWindow
            window = TestMainWindow()
            window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الاختبار:\n{e}")

def main():
    """تشغيل سريع"""
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي والتوجيه
    font = QFont("Sakkal Majalla", 10)
    app.setFont(font)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🚀 تشغيل سريع للنظام...")
    print("📋 الميزات المتاحة:")
    print("   👥 إدارة الموظفين مع قوائم منسدلة")
    print("   📋 إدارة أنواع الإجازات")
    print("   📝 طلبات إجازات محسّنة")
    print("   🔍 بحث وتعديل متقدم")
    print("   📊 تقارير شاملة")
    print("   🔄 التوجيه العربي الكامل")
    print("=" * 50)
    
    try:
        # عرض معلومات النظام
        from إدارة_الموظفين import get_employee_names
        from إدارة_أنواع_الإجازات import get_vacation_type_names
        
        employee_count = len(get_employee_names())
        vacation_types_count = len(get_vacation_type_names())
        
        print(f"👥 عدد الموظفين: {employee_count}")
        print(f"📋 عدد أنواع الإجازات: {vacation_types_count}")
        
    except Exception as e:
        print(f"⚠️ تحذير: {e}")
    
    print("🔄 فتح نافذة التشغيل السريع...")
    
    # فتح النافذة
    window = QuickStartWindow()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
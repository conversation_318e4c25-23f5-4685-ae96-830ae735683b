# نظام إدارة الإجازات المتقدم - الإصدار 2.0

## 🎯 نظرة عامة

نظام شامل ومتقدم لإدارة إجازات الموظفين مع ميزات متطورة تشمل التقارير المتقدمة، الإشعارات الذكية، النسخ الاحتياطي التلقائي، وإدارة المستخدمين والصلاحيات.

## ✨ الميزات الجديدة

### 📊 نظام التقارير المتقدم
- تقارير شهرية وسنوية تفصيلية
- تقارير مخصصة حسب المرشحات
- تصدير إلى Excel مع تنسيق احترافي
- تحليلات متقدمة للبيانات

### 🔔 نظام الإشعارات الذكي
- تنبيهات تلقائية للرصيد المنخفض
- إشعارات انتهاء الإجازات
- تنبيهات الطلبات المعلقة
- ملخص يومي للنشاط

### 📈 لوحة التحليلات
- رسوم بيانية نصية تفاعلية
- تحليل الاتجاهات الزمنية
- إحصائيات الأداء
- مقارنات شهرية وسنوية

### 💾 نظام النسخ الاحتياطي
- نسخ احتياطية تلقائية ويدوية
- ضغط البيانات لتوفير المساحة
- التحقق من سلامة النسخ
- استعادة سهلة وآمنة

### 👥 إدارة المستخدمين والصلاحيات
- نظام أدوار متعدد المستويات
- صلاحيات مخصصة لكل مستخدم
- سجل نشاط شامل
- إدارة متقدمة للمستخدمين

## 🚀 التشغيل السريع

### الطريقة الأولى: لوحة التحكم الرئيسية (الموصى بها)
```bash
python master_control_panel.py
```

### الطريقة الثانية: تشغيل الوحدات منفصلة
```bash
# النظام الرئيسي
python main.py

# نظام التقارير
python run_reports.py

# نظام الإشعارات
python notifications_interface.py

# لوحة التحليلات
python analytics_dashboard.py

# نظام النسخ الاحتياطي
python backup_interface.py

# إدارة المستخدمين
python user_management_interface.py
```

## 📋 المتطلبات

### المكتبات الأساسية
```
PyQt5==5.15.10
pandas==2.1.4
openpyxl==3.1.2
reportlab==4.0.7
```

### تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

## 🗂️ هيكل النظام

### الملفات الأساسية
- `database.py` - قاعدة البيانات المحسنة مع الصلاحيات
- `master_control_panel.py` - لوحة التحكم الرئيسية
- `main.py` - النظام الرئيسي الأصلي

### أنظمة التقارير
- `advanced_reports.py` - محرك التقارير المتقدم
- `reports_interface.py` - واجهة التقارير التفاعلية
- `run_reports.py` - مشغل نظام التقارير

### أنظمة الإشعارات
- `notification_system.py` - محرك الإشعارات
- `notifications_interface.py` - واجهة إدارة الإشعارات

### أنظمة التحليلات
- `analytics_dashboard.py` - لوحة التحليلات والرسوم البيانية

### أنظمة النسخ الاحتياطي
- `backup_system.py` - محرك النسخ الاحتياطي
- `backup_interface.py` - واجهة إدارة النسخ

### أنظمة إدارة المستخدمين
- `user_management_interface.py` - واجهة إدارة المستخدمين والصلاحيات

### ملفات الاختبار
- `test_vacation_system.py` - اختبار النظام الأساسي
- `test_advanced_reports.py` - اختبار نظام التقارير
- `quick_start.py` - التشغيل السريع مع فحص المتطلبات

## 🔐 نظام الصلاحيات

### الأدوار الافتراضية

#### 🔴 مدير النظام (admin)
- إدارة المستخدمين والأدوار
- الوصول لجميع البيانات
- إعدادات النظام
- النسخ الاحتياطي والاستعادة

#### 🟡 مدير الموارد البشرية (hr_manager)
- عرض جميع بيانات الموظفين
- الموافقة على الطلبات
- توليد التقارير
- إدارة الموظفين

#### 🟠 مشرف (supervisor)
- عرض بيانات القسم
- الموافقة على طلبات القسم
- تقارير القسم

#### 🟢 موظف (employee)
- عرض البيانات الشخصية
- تقديم الطلبات
- عرض التقارير الشخصية

#### 🔵 مستعرض (viewer)
- عرض محدود للبيانات
- التقارير العامة

### الصلاحيات المتاحة
- `user_management` - إدارة المستخدمين
- `role_management` - إدارة الأدوار
- `system_settings` - إعدادات النظام
- `view_all_data` - عرض جميع البيانات
- `edit_all_data` - تعديل جميع البيانات
- `delete_data` - حذف البيانات
- `generate_reports` - توليد التقارير
- `backup_restore` - النسخ الاحتياطي والاستعادة
- `view_logs` - عرض سجل النشاط
- `approve_requests` - الموافقة على الطلبات
- `manage_employees` - إدارة الموظفين

## 📊 أنواع التقارير

### 📅 التقارير الشهرية
- إحصائيات الإجازات اليومية والساعية
- توزيع أنواع الإجازات
- أكثر الموظفين استخداماً

### 📈 التقارير السنوية
- اتجاهات الإجازات عبر الأشهر
- مقارنات سنوية
- إحصائيات شاملة

### 👤 تقارير الموظفين
- تقرير مفصل لكل موظف
- تاريخ الإجازات
- الرصيد والاستهلاك

### 🔧 التقارير المخصصة
- مرشحات متقدمة
- فترات زمنية محددة
- موظفين محددين

## 🔔 أنواع الإشعارات

### ⚠️ تنبيهات الرصيد
- رصيد منخفض (أقل من 5 أيام)
- رصيد سالب
- تجاوز الحد المسموح

### 📅 تنبيهات الإجازات
- انتهاء الإجازات قريباً
- إجازات منتهية الصلاحية
- تضارب في التواريخ

### 📋 تنبيهات الطلبات
- طلبات جديدة تحتاج موافقة
- طلبات معلقة
- طلبات مرفوضة

### 📊 الملخص اليومي
- نشاط اليوم
- إحصائيات سريعة
- تذكيرات مهمة

## 💾 النسخ الاحتياطي

### أنواع النسخ
- **يدوية**: عند الطلب
- **تلقائية**: كل 24 ساعة (قابل للتخصيص)
- **مضغوطة**: توفير المساحة
- **مجلدات**: سهولة الوصول

### الملفات المشمولة
- قاعدة البيانات الرئيسية
- قاعدة بيانات الإشعارات
- ملفات Excel النموذجية
- إعدادات النظام

### الاستعادة
- استعادة كاملة أو جزئية
- التحقق من سلامة النسخ
- معاينة محتويات النسخة

## 🧪 الاختبار

### اختبار شامل
```bash
python master_control_panel.py
# اختر "اختبار النظام الشامل"
```

### اختبار وحدات منفصلة
```bash
python test_vacation_system.py
python test_advanced_reports.py
```

## 🔧 الإعدادات

### إعدادات الإشعارات
- حد التنبيه للرصيد المنخفض
- فترة التنبيه قبل انتهاء الإجازة
- تفعيل/إلغاء أنواع الإشعارات

### إعدادات النسخ الاحتياطي
- فترة النسخ التلقائي
- عدد النسخ المحفوظة
- نوع الضغط
- مجلد الحفظ

### إعدادات التقارير
- تنسيق التصدير
- الألوان والخطوط
- اللغة والتوطين

## 🚨 استكشاف الأخطاء

### مشاكل شائعة

#### لا يعمل النظام
1. تحقق من تثبيت Python 3.7+
2. تثبيت المكتبات: `pip install -r requirements.txt`
3. استخدم `quick_start.py` للفحص التلقائي

#### خطأ في قاعدة البيانات
1. تحقق من وجود `vacation_system.db`
2. تشغيل `python database.py` لإعادة الإنشاء
3. استعادة من نسخة احتياطية

#### مشاكل الصلاحيات
1. تسجيل الدخول كمدير
2. التحقق من الأدوار والصلاحيات
3. إعادة تعيين كلمة مرور المدير

## 📞 الدعم

### الملفات المرجعية
- `دليل_التشغيل_السريع.md` - دليل المستخدم
- `database_functions_summary.md` - وثائق قاعدة البيانات
- `README.md` - الدليل الأساسي

### سجلات النظام
- سجل النشاط في قاعدة البيانات
- ملفات السجل في مجلد `logs`
- تقارير الأخطاء في الواجهات

## 🎉 الميزات القادمة

- 📱 واجهة ويب متجاوبة
- 📧 إشعارات البريد الإلكتروني
- 📊 رسوم بيانية متقدمة
- 🔄 مزامنة مع أنظمة خارجية
- 🌐 دعم متعدد اللغات

---

**تم تطوير النظام بواسطة:** فريق تطوير نظام إدارة الإجازات  
**الإصدار:** 2.0 المتقدم  
**التاريخ:** 2024  
**الترخيص:** للاستخدام الداخلي

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
النوافذ الفرعية الحديثة لنظام إدارة الإجازات
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime, timedelta
import json

class ModernDialog(QDialog):
    """نافذة حوار حديثة أساسية"""
    
    def __init__(self, title, width=600, height=500):
        super().__init__()
        self.setWindowTitle(title)
        self.setFixedSize(width, height)
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # متغيرات السحب
        self.old_pos = self.pos()
        
        # إعداد الواجهة
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة النافذة"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء الإطار الرئيسي
        main_frame = QFrame()
        main_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef
                );
                border-radius: 20px;
                border: 2px solid #3498db;
            }
        """)
        
        frame_layout = QVBoxLayout(main_frame)
        frame_layout.setContentsMargins(20, 20, 20, 20)
        frame_layout.setSpacing(20)
        
        # شريط العنوان
        self.create_title_bar(frame_layout)
        
        # المحتوى
        self.content_area = QWidget()
        frame_layout.addWidget(self.content_area)
        
        # شريط الأزرار
        self.create_button_bar(frame_layout)
        
        main_layout.addWidget(main_frame)
        
    def create_title_bar(self, layout):
        """إنشاء شريط العنوان"""
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #3498db, stop: 1 #2980b9
                );
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 10px;
            }
        """)
        
        title_layout = QHBoxLayout(title_frame)
        
        # عنوان النافذة
        self.title_label = QLabel(self.windowTitle())
        self.title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: white;
            background: transparent;
        """)
        title_layout.addWidget(self.title_label)
        
        # مساحة فارغة
        title_layout.addStretch()
        
        # زر الإغلاق
        close_button = QPushButton("❌")
        close_button.setFixedSize(30, 30)
        close_button.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                border: none;
                border-radius: 15px;
                color: white;
                font-size: 14px;
            }
            QPushButton:hover {
                background: #e74c3c;
            }
        """)
        close_button.clicked.connect(self.reject)
        title_layout.addWidget(close_button)
        
        layout.addWidget(title_frame)
        
    def create_button_bar(self, layout):
        """إنشاء شريط الأزرار"""
        button_frame = QFrame()
        button_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.5);
                border-radius: 10px;
                padding: 10px;
                margin-top: 10px;
            }
        """)
        
        self.button_layout = QHBoxLayout(button_frame)
        self.button_layout.addStretch()
        
        layout.addWidget(button_frame)
        
    def add_button(self, text, callback, style_class="primary"):
        """إضافة زر للشريط"""
        button = QPushButton(text)
        button.clicked.connect(callback)
        
        if style_class == "primary":
            button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #3498db, stop: 1 #2980b9
                    );
                    border: none;
                    border-radius: 8px;
                    color: white;
                    font-weight: bold;
                    padding: 10px 20px;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #5dade2, stop: 1 #3498db
                    );
                }
            """)
        elif style_class == "secondary":
            button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #95a5a6, stop: 1 #7f8c8d
                    );
                    border: none;
                    border-radius: 8px;
                    color: white;
                    font-weight: bold;
                    padding: 10px 20px;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #b2bec3, stop: 1 #95a5a6
                    );
                }
            """)
        
        self.button_layout.addWidget(button)
        return button
    
    def get_input_style(self):
        """إرجاع أنماط محسّنة لمربعات النص"""
        return """
        QLineEdit {
            font-family: "Sakkal Majalla", "Arial", sans-serif;
            background: white;
            border: 2px solid #bdc3c7;
            border-radius: 12px;
            padding: 15px;
            font-size: 16px;
            font-weight: bold;
            color: #1a1a1a;
            min-height: 50px;
            max-height: 65px;
            selection-background-color: #3498db;
        }
        
        QLineEdit:focus {
            border: 2px solid #3498db;
            background: #ffffff;
            color: #000000;
        }
        
        QLineEdit:hover {
            border: 2px solid #5dade2;
            background: #ffffff;
        }
        
        QLineEdit::placeholder {
            color: #95a5a6;
            font-style: italic;
            font-size: 14px;
        }
        """
        
    def mousePressEvent(self, event):
        """بداية السحب"""
        self.old_pos = event.globalPos()
        
    def mouseMoveEvent(self, event):
        """سحب النافذة"""
        if event.buttons() == Qt.LeftButton:
            delta = QPoint(event.globalPos() - self.old_pos)
            self.move(self.x() + delta.x(), self.y() + delta.y())
            self.old_pos = event.globalPos()

class ModernImportDialog(ModernDialog):
    """نافذة استيراد البيانات الحديثة"""
    
    def __init__(self):
        super().__init__("📥 استيراد الرصيد الابتدائي", 700, 600)
        self.setup_content()
        
    def setup_content(self):
        """إعداد محتوى النافذة"""
        layout = QVBoxLayout(self.content_area)
        layout.setSpacing(20)
        
        # معلومات النافذة
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2ecc71, stop: 1 #27ae60
                );
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        info_layout = QVBoxLayout(info_frame)
        info_label = QLabel("📋 استيراد بيانات الموظفين من ملف Excel")
        info_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
            background: transparent;
        """)
        info_layout.addWidget(info_label)
        
        desc_label = QLabel("اختر ملف Excel يحتوي على أسماء الموظفين والرصيد الابتدائي")
        desc_label.setStyleSheet("""
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            background: transparent;
        """)
        info_layout.addWidget(desc_label)
        
        layout.addWidget(info_frame)
        
        # قسم اختيار الملف
        file_frame = QFrame()
        file_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 20px;
            }
        """)
        
        file_layout = QVBoxLayout(file_frame)
        
        # تسمية
        file_label = QLabel("📁 اختر ملف Excel:")
        file_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        file_layout.addWidget(file_label)
        
        # صف اختيار الملف
        file_row = QHBoxLayout()
        
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("لم يتم اختيار ملف...")
        self.file_path_edit.setMinimumHeight(50)
        self.file_path_edit.setMaximumHeight(60)
        self.file_path_edit.setStyleSheet("""
            QLineEdit {
                background: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                font-size: 16px;
                color: #495057;
                min-height: 50px;
            }
        """)
        file_row.addWidget(self.file_path_edit)
        
        browse_button = QPushButton("📂 تصفح")
        browse_button.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        browse_button.clicked.connect(self.browse_file)
        file_row.addWidget(browse_button)
        
        file_layout.addLayout(file_row)
        
        # معاينة البيانات
        preview_label = QLabel("👀 معاينة البيانات:")
        preview_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px; margin-top: 15px;")
        file_layout.addWidget(preview_label)
        
        self.preview_table = QTableWidget()
        self.preview_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                gridline-color: #dee2e6;
            }
            QHeaderView::section {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 8px;
                font-weight: bold;
                color: #495057;
            }
        """)
        file_layout.addWidget(self.preview_table)
        
        layout.addWidget(file_frame)
        
        # إعداد الأزرار
        self.add_button("💾 استيراد", self.import_data, "primary")
        self.add_button("❌ إلغاء", self.reject, "secondary")
        
    def browse_file(self):
        """تصفح واختيار ملف"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "اختيار ملف Excel", 
            "", 
            "Excel Files (*.xlsx *.xls)"
        )
        
        if file_path:
            self.file_path_edit.setText(file_path)
            self.preview_file(file_path)
            
    def preview_file(self, file_path):
        """معاينة محتوى الملف"""
        try:
            import pandas as pd
            df = pd.read_excel(file_path)
            
            # إعداد الجدول
            self.preview_table.setRowCount(min(5, len(df)))  # أول 5 صفوف
            self.preview_table.setColumnCount(len(df.columns))
            self.preview_table.setHorizontalHeaderLabels(df.columns.tolist())
            
            # ملء البيانات
            for row in range(min(5, len(df))):
                for col in range(len(df.columns)):
                    item = QTableWidgetItem(str(df.iloc[row, col]))
                    self.preview_table.setItem(row, col, item)
                    
            # تعديل حجم الأعمدة
            self.preview_table.resizeColumnsToContents()
            
        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"لا يمكن قراءة الملف:\n{str(e)}")
            
    def import_data(self):
        """تنفيذ استيراد البيانات"""
        file_path = self.file_path_edit.text().strip()
        
        if not file_path:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ملف Excel أولاً")
            return
            
        # هنا سيتم إضافة كود الاستيراد الفعلي
        QMessageBox.information(self, "نجح", f"تم استيراد البيانات من:\n{file_path}")
        self.accept()

class ModernDailyRequestDialog(ModernDialog):
    """نافذة طلب الإجازة اليومية الحديثة"""
    
    def __init__(self):
        super().__init__("📝 طلب إجازة يومية", 800, 700)
        self.setup_content()
        
    def setup_content(self):
        """إعداد محتوى النافذة"""
        layout = QVBoxLayout(self.content_area)
        layout.setSpacing(20)
        
        # معلومات النافذة
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #e74c3c, stop: 1 #c0392b
                );
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        info_layout = QVBoxLayout(info_frame)
        info_label = QLabel("📝 إضافة طلب إجازة يومية جديد")
        info_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
            background: transparent;
        """)
        info_layout.addWidget(info_label)
        
        layout.addWidget(info_frame)
        
        # نموذج البيانات
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 25px;
            }
        """)
        
        form_layout = QGridLayout(form_frame)
        form_layout.setSpacing(15)
        
        # الحقول
        self.create_form_field(form_layout, 0, "👤 الاسم واللقب:", "name_edit", "أدخل الاسم الكامل...")
        self.create_form_field(form_layout, 1, "🆔 رقم القيد:", "emp_id_edit", "رقم القيد...")
        self.create_form_field(form_layout, 2, "💼 الوظيفة:", "job_edit", "المسمى الوظيفي...")
        self.create_form_field(form_layout, 3, "🏢 الفوج/القسم:", "dept_edit", "اسم القسم...")
        
        # نوع الإجازة
        vacation_label = QLabel("📋 نوع الإجازة:")
        vacation_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        form_layout.addWidget(vacation_label, 4, 0)
        
        self.vacation_type_combo = QComboBox()
        self.vacation_type_combo.addItems([
            "سنوية", "مرضية", "طارئة", "أمومة", "أخرى"
        ])
        self.vacation_type_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
            }
        """)
        form_layout.addWidget(self.vacation_type_combo, 4, 1)
        
        # التاريخ
        date_label = QLabel("📅 تاريخ الخروج:")
        date_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        form_layout.addWidget(date_label, 5, 0)
        
        self.date_edit = QDateEdit(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setStyleSheet("""
            QDateEdit {
                background: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
            }
        """)
        form_layout.addWidget(self.date_edit, 5, 1)
        
        # عدد الأيام
        days_label = QLabel("🕐 عدد الأيام:")
        days_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        form_layout.addWidget(days_label, 6, 0)
        
        self.days_spinbox = QSpinBox()
        self.days_spinbox.setRange(1, 365)
        self.days_spinbox.setValue(1)
        self.days_spinbox.setStyleSheet("""
            QSpinBox {
                background: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
            }
        """)
        form_layout.addWidget(self.days_spinbox, 6, 1)
        
        layout.addWidget(form_frame)
        
        # إعداد الأزرار
        self.add_button("💾 حفظ الطلب", self.save_request, "primary")
        self.add_button("❌ إلغاء", self.reject, "secondary")
        
    def create_form_field(self, layout, row, label_text, object_name, placeholder):
        """إنشاء حقل نموذج"""
        label = QLabel(label_text)
        label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        layout.addWidget(label, row, 0)
        
        edit = QLineEdit()
        edit.setObjectName(object_name)
        edit.setPlaceholderText(placeholder)
        edit.setMinimumHeight(50)
        edit.setMaximumHeight(60)
        edit.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                font-size: 16px;
                min-height: 50px;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
        """)
        layout.addWidget(edit, row, 1)
        
        setattr(self, object_name, edit)
        
    def save_request(self):
        """حفظ طلب الإجازة"""
        # التحقق من البيانات
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الموظف")
            return
            
        # هنا سيتم إضافة كود الحفظ الفعلي
        QMessageBox.information(self, "نجح", "تم حفظ طلب الإجازة بنجاح!")
        self.accept()

def test_modern_dialogs():
    """اختبار النوافذ الحديثة"""
    import sys
    
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # اختبار نافذة الاستيراد
    import_dialog = ModernImportDialog()
    import_dialog.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    test_modern_dialogs()
// qmetatype.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LIC<PERSON><PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMetaType
{
%TypeHeaderCode
#include <qmetatype.h>
%End

public:
    enum Type
    {
        UnknownType,
        Void,
        Bool,
        Int,
        UInt,
        LongLong,
        ULongLong,
        Double,
        QChar,
        QVariantMap,
        QVariantList,
        QVariantHash,
        QString,
        QStringList,
        QByteArray,
        QBitArray,
        QDate,
        QTime,
        QDateTime,
        QUrl,
        QLocale,
        QRect,
        QRectF,
        QSize,
        QSizeF,
        QLine,
        QLineF,
        QPoint,
        QPointF,
        QRegExp,
        LastCoreType,
        FirstGuiType,
        QFont,
        QPixmap,
        QBrush,
        QColor,
        QPalette,
        QIcon,
        QImage,
        QPolygon,
        QRegion,
        QBitmap,
        QCursor,
        QSizePolicy,
        QKeySequence,
        QPen,
        QTextLength,
        QTextFormat,
        QMatrix,
        QTransform,
        VoidStar,
        Long,
        Short,
        Char,
        ULong,
        UShort,
        UChar,
        Float,
        QObjectStar,
        QMatrix4x4,
        QVector2D,
        QVector3D,
        QVector4D,
        QQuaternion,
        QEasingCurve,
        QVariant,
        QUuid,
        QModelIndex,
        QPolygonF,
        SChar,
        QRegularExpression,
        QJsonValue,
        QJsonObject,
        QJsonArray,
        QJsonDocument,
%If (Qt_5_4_0 -)
        QByteArrayList,
%End
%If (Qt_5_5_0 -)
        QPersistentModelIndex,
%End
%If (Qt_5_12_0 -)
        QCborSimpleType,
%End
%If (Qt_5_12_0 -)
        QCborValue,
%End
%If (Qt_5_12_0 -)
        QCborArray,
%End
%If (Qt_5_12_0 -)
        QCborMap,
%End
%If (Qt_5_15_0 -)
        QColorSpace,
%End
        User,
    };

    static int type(const char *typeName);
    static const char *typeName(int type);
    static bool isRegistered(int type);
%If (- Qt_5_15_0)
    explicit QMetaType(const int type);
%End
%If (Qt_5_15_0 -)
    explicit QMetaType(const int type = QMetaType::Type::UnknownType);
%End
    ~QMetaType();

    enum TypeFlag
    {
        NeedsConstruction,
        NeedsDestruction,
        MovableType,
        PointerToQObject,
        IsEnumeration,
    };

    typedef QFlags<QMetaType::TypeFlag> TypeFlags;
    static QMetaType::TypeFlags typeFlags(int type);
    QMetaType::TypeFlags flags() const;
    bool isValid() const;
    bool isRegistered() const;
    static const QMetaObject *metaObjectForType(int type);
%If (Qt_5_13_0 -)
    int id() const;
%End
%If (Qt_5_15_0 -)
    QByteArray name() const;
%End

private:
    QMetaType(const QMetaType &other);
};

QFlags<QMetaType::TypeFlag> operator|(QMetaType::TypeFlag f1, QFlags<QMetaType::TypeFlag> f2);
%If (Qt_5_15_0 -)
bool operator==(const QMetaType &a, const QMetaType &b);
%End
%If (Qt_5_15_0 -)
bool operator!=(const QMetaType &a, const QMetaType &b);
%End

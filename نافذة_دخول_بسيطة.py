#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة تسجيل دخول بسيطة وواضحة
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class SimpleLoginWindow(QDialog):
    """نافذة تسجيل دخول بسيطة وواضحة"""
    
    def __init__(self):
        super().__init__()
        self.user_data = None
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة تسجيل الدخول"""
        self.setWindowTitle("🔐 تسجيل الدخول - نظام إدارة الإجازات")
        self.setFixedSize(500, 400)
        
        # الخط
        self.setFont(QFont("Sakkal Majalla", 12))
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        
        # العنوان
        title_label = QLabel("🔐 تسجيل الدخول إلى النظام")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #3498db, stop: 1 #2980b9);
                color: white;
                border-radius: 15px;
                padding: 20px;
                margin: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # نموذج تسجيل الدخول
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 15px;
                padding: 20px;
            }
        """)
        
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(15)
        
        # اسم المستخدم
        username_label = QLabel("👤 اسم المستخدم:")
        username_label.setStyleSheet("""
            font-family: "Sakkal Majalla", "Arial", sans-serif;
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        """)
        form_layout.addWidget(username_label)
        
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم...")
        self.username_edit.setText("admin")
        self.username_edit.setStyleSheet("""
            QLineEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                background: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                color: #000000;
                min-height: 20px;
            }
            QLineEdit:focus {
                border: 2px solid #007bff;
                background: white;
            }
            QLineEdit:hover {
                border: 2px solid #6c757d;
            }
        """)
        form_layout.addWidget(self.username_edit)
        
        # كلمة المرور
        password_label = QLabel("🔐 كلمة المرور:")
        password_label.setStyleSheet("""
            font-family: "Sakkal Majalla", "Arial", sans-serif;
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        """)
        form_layout.addWidget(password_label)
        
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("أدخل كلمة المرور...")
        self.password_edit.setText("admin123")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setStyleSheet("""
            QLineEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                background: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                color: #000000;
                min-height: 20px;
            }
            QLineEdit:focus {
                border: 2px solid #007bff;
                background: white;
            }
            QLineEdit:hover {
                border: 2px solid #6c757d;
            }
        """)
        self.password_edit.returnPressed.connect(self.handle_login)
        form_layout.addWidget(self.password_edit)
        
        main_layout.addWidget(form_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        
        login_button = QPushButton("🚀 تسجيل الدخول")
        login_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #28a745, stop: 1 #20c997);
                border: none;
                border-radius: 10px;
                color: white;
                font-weight: bold;
                font-size: 16px;
                padding: 15px 30px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #34ce57, stop: 1 #28a745);
            }
            QPushButton:pressed {
                background: #1e7e34;
            }
        """)
        login_button.clicked.connect(self.handle_login)
        buttons_layout.addWidget(login_button)
        
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #dc3545, stop: 1 #c82333);
                border: none;
                border-radius: 10px;
                color: white;
                font-weight: bold;
                font-size: 16px;
                padding: 15px 30px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e0506e, stop: 1 #dc3545);
            }
            QPushButton:pressed {
                background: #bd2130;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)
        
        main_layout.addLayout(buttons_layout)
        
        # إضافة مساحة في النهاية
        main_layout.addStretch()
        
        # الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #e3f2fd, stop: 1 #bbdefb);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
        
    def handle_login(self):
        """التعامل مع تسجيل الدخول"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        
        # التحقق من البيانات
        if username == "admin" and password == "admin123":
            self.user_data = {
                'username': username,
                'full_name': 'مدير النظام',
                'role': 'admin'
            }
            QMessageBox.information(self, "✅ نجح تسجيل الدخول", 
                                  f"مرحباً {self.user_data['full_name']}\nتم تسجيل الدخول بنجاح!")
            self.accept()
        else:
            QMessageBox.warning(self, "❌ خطأ في تسجيل الدخول", 
                              "اسم المستخدم أو كلمة المرور غير صحيحة!\n\nاستخدم:\nالمستخدم: admin\nكلمة المرور: admin123")
            self.password_edit.clear()
            self.username_edit.setFocus()

def test_simple_login():
    """اختبار نافذة تسجيل الدخول البسيطة"""
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    font = QFont("Sakkal Majalla", 12)
    app.setFont(font)
    
    window = SimpleLoginWindow()
    
    print("🔍 اختبار نافذة تسجيل الدخول البسيطة:")
    print("👤 اسم المستخدم: admin")
    print("🔐 كلمة المرور: admin123")
    print("📝 تحقق من وضوح مربعات النص!")
    
    if window.exec_() == QDialog.Accepted:
        print("✅ تم تسجيل الدخول بنجاح!")
        return window.user_data
    else:
        print("❌ تم إلغاء تسجيل الدخول")
        return None

if __name__ == "__main__":
    test_simple_login()
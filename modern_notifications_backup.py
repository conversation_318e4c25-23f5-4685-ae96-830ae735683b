#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام الإشعارات المتقدم والنسخ الاحتياطي الذكي
"""

import sys
import os
import json
import sqlite3
import smtplib
import shutil
import zipfile
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from modern_dialogs import ModernDialog

class NotificationManager(QObject):
    """مدير الإشعارات المتقدم"""
    
    notification_sent = pyqtSignal(str, str)  # نوع الإشعار، الرسالة
    
    def __init__(self):
        super().__init__()
        self.setup_notifications()
        
    def setup_notifications(self):
        """إعداد نظام الإشعارات"""
        self.notifications_enabled = True
        self.email_settings = {
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'username': '',
            'password': '',
            'sender_name': 'نظام إدارة الإجازات'
        }
        self.notification_rules = {
            'new_request': True,
            'request_approved': True,
            'request_rejected': True,
            'low_balance': True,
            'balance_expired': True,
            'system_backup': True,
            'overdue_requests': True
        }
        
    def send_notification(self, notification_type, title, message, recipient_email=None):
        """إرسال إشعار"""
        if not self.notifications_enabled:
            return False
            
        # إشعار على سطح المكتب
        self.show_desktop_notification(title, message)
        
        # إشعار بالبريد الإلكتروني
        if recipient_email and self.email_settings['username']:
            self.send_email_notification(title, message, recipient_email)
            
        # تسجيل الإشعار
        self.log_notification(notification_type, title, message)
        
        # إرسال إشارة
        self.notification_sent.emit(notification_type, message)
        
        return True
        
    def show_desktop_notification(self, title, message):
        """عرض إشعار على سطح المكتب"""
        # نافذة إشعار مخصصة
        notification = DesktopNotification(title, message)
        notification.show()
        
    def send_email_notification(self, title, message, recipient_email):
        """إرسال إشعار بالبريد الإلكتروني"""
        try:
            msg = MIMEMultipart()
            msg['From'] = f"{self.email_settings['sender_name']} <{self.email_settings['username']}>"
            msg['To'] = recipient_email
            msg['Subject'] = title
            
            # محتوى HTML
            html_body = f"""
            <html>
            <body style="font-family: "Sakkal Majalla", "Arial", sans-serif; direction: rtl; text-align: right;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 10px; color: white;">
                    <h2 style="margin: 0;">🔔 {title}</h2>
                </div>
                <div style="padding: 20px; background: #f8f9fa; border-radius: 0 0 10px 10px;">
                    <p style="font-size: 16px; line-height: 1.6;">{message}</p>
                    <hr style="border: 1px solid #e9ecef;">
                    <p style="color: #6c757d; font-size: 14px;">
                        📅 تاريخ الإشعار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br>
                        🏢 نظام إدارة الإجازات
                    </p>
                </div>
            </body>
            </html>
            """
            
            msg.attach(MIMEText(html_body, 'html', 'utf-8'))
            
            # إرسال الإيميل
            server = smtplib.SMTP(self.email_settings['smtp_server'], self.email_settings['smtp_port'])
            server.starttls()
            server.login(self.email_settings['username'], self.email_settings['password'])
            server.send_message(msg)
            server.quit()
            
            return True
            
        except Exception as e:
            print(f"خطأ في إرسال الإيميل: {e}")
            return False
            
    def log_notification(self, notification_type, title, message):
        """تسجيل الإشعار في قاعدة البيانات"""
        try:
            conn = sqlite3.connect('vacation_system.db')
            cursor = conn.cursor()
            
            # إنشاء جدول الإشعارات إذا لم يكن موجوداً
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    type TEXT,
                    title TEXT,
                    message TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    read_status INTEGER DEFAULT 0
                )
            ''')
            
            cursor.execute('''
                INSERT INTO notifications (type, title, message)
                VALUES (?, ?, ?)
            ''', (notification_type, title, message))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تسجيل الإشعار: {e}")
            
    def check_pending_notifications(self):
        """فحص الإشعارات المعلقة"""
        # فحص الطلبات الجديدة
        self.check_new_requests()
        
        # فحص الأرصدة المنخفضة
        self.check_low_balances()
        
        # فحص الطلبات المتأخرة
        self.check_overdue_requests()
        
    def check_new_requests(self):
        """فحص الطلبات الجديدة"""
        if not self.notification_rules['new_request']:
            return
            
        # هنا سيتم فحص الطلبات الجديدة من قاعدة البيانات
        # ومقارنتها بآخر فحص
        pass
        
    def check_low_balances(self):
        """فحص الأرصدة المنخفضة"""
        if not self.notification_rules['low_balance']:
            return
            
        # فحص الموظفين ذوي الأرصدة المنخفضة
        low_balance_employees = []  # من قاعدة البيانات
        
        if low_balance_employees:
            message = f"تحذير: يوجد {len(low_balance_employees)} موظف برصيد منخفض"
            self.send_notification('low_balance', 'تحذير رصيد منخفض', message)
            
    def check_overdue_requests(self):
        """فحص الطلبات المتأخرة"""
        if not self.notification_rules['overdue_requests']:
            return
            
        # فحص الطلبات المتأخرة في المراجعة
        overdue_requests = []  # من قاعدة البيانات
        
        if overdue_requests:
            message = f"تنبيه: يوجد {len(overdue_requests)} طلب متأخر في المراجعة"
            self.send_notification('overdue_requests', 'طلبات متأخرة', message)

class DesktopNotification(QWidget):
    """نافذة إشعار سطح المكتب"""
    
    def __init__(self, title, message):
        super().__init__()
        self.title = title
        self.message = message
        self.setup_notification()
        
    def setup_notification(self):
        """إعداد نافذة الإشعار"""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setFixedSize(350, 120)
        
        # تحديد موقع النافذة
        screen = QApplication.desktop().screenGeometry()
        self.move(screen.width() - 370, 50)
        
        # التصميم
        self.setStyleSheet("""
            QWidget {
                background: rgba(0, 0, 0, 0);
            }
        """)
        
        # المحتوى
        self.create_content()
        
        # تأثير الظهور
        self.fade_in()
        
        # إخفاء تلقائي بعد 5 ثوان
        QTimer.singleShot(5000, self.fade_out)
        
    def create_content(self):
        """إنشاء محتوى الإشعار"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # الإطار الرئيسي
        main_frame = QFrame()
        main_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #667eea, stop: 1 #764ba2
                );
                border-radius: 10px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                padding: 15px;
            }
        """)
        
        content_layout = QVBoxLayout(main_frame)
        
        # العنوان
        title_label = QLabel(self.title)
        title_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        """)
        content_layout.addWidget(title_label)
        
        # الرسالة
        message_label = QLabel(self.message)
        message_label.setStyleSheet("""
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.4;
        """)
        message_label.setWordWrap(True)
        content_layout.addWidget(message_label)
        
        # زر الإغلاق
        close_btn = QPushButton("✕")
        close_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: white;
                font-weight: bold;
                border-radius: 10px;
                padding: 5px;
                max-width: 20px;
                max-height: 20px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.3);
            }
        """)
        close_btn.clicked.connect(self.close)
        
        # تخطيط الزر
        btn_layout = QHBoxLayout()
        btn_layout.addStretch()
        btn_layout.addWidget(close_btn)
        content_layout.addLayout(btn_layout)
        
        layout.addWidget(main_frame)
        
    def fade_in(self):
        """تأثير الظهور"""
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)
        
        self.fade_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.start()
        
    def fade_out(self):
        """تأثير الاختفاء"""
        if hasattr(self, 'fade_animation'):
            self.fade_animation.setDirection(QPropertyAnimation.Backward)
            self.fade_animation.finished.connect(self.close)
            self.fade_animation.start()
        else:
            self.close()
            
    def mousePressEvent(self, event):
        """النقر لإغلاق الإشعار"""
        if event.button() == Qt.LeftButton:
            self.close()

class BackupManager(QObject):
    """مدير النسخ الاحتياطي الذكي"""
    
    backup_started = pyqtSignal()
    backup_progress = pyqtSignal(int, str)
    backup_completed = pyqtSignal(bool, str)
    
    def __init__(self):
        super().__init__()
        self.setup_backup()
        
    def setup_backup(self):
        """إعداد نظام النسخ الاحتياطي"""
        self.backup_settings = {
            'auto_backup': True,
            'backup_frequency': 'daily',  # daily, weekly, monthly
            'backup_location': 'backups',
            'max_backups': 10,
            'compress_backups': True,
            'include_logs': True,
            'include_settings': True
        }
        
        # إنشاء مجلد النسخ الاحتياطي
        os.makedirs(self.backup_settings['backup_location'], exist_ok=True)
        
    def create_backup(self, backup_type='manual'):
        """إنشاء نسخة احتياطية"""
        self.backup_started.emit()
        
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"backup_{backup_type}_{timestamp}"
            
            if self.backup_settings['compress_backups']:
                backup_path = os.path.join(self.backup_settings['backup_location'], f"{backup_name}.zip")
                self.create_compressed_backup(backup_path)
            else:
                backup_path = os.path.join(self.backup_settings['backup_location'], backup_name)
                self.create_folder_backup(backup_path)
                
            # تنظيف النسخ القديمة
            self.cleanup_old_backups()
            
            # إشعار بالنجاح
            self.backup_completed.emit(True, backup_path)
            
            return True, backup_path
            
        except Exception as e:
            self.backup_completed.emit(False, str(e))
            return False, str(e)
            
    def create_compressed_backup(self, backup_path):
        """إنشاء نسخة احتياطية مضغوطة"""
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            total_files = self.count_backup_files()
            current_file = 0
            
            # قاعدة البيانات
            if os.path.exists('vacation_system.db'):
                zipf.write('vacation_system.db', 'database/vacation_system.db')
                current_file += 1
                progress = int((current_file / total_files) * 100)
                self.backup_progress.emit(progress, "نسخ قاعدة البيانات...")
                
            # ملفات الإعدادات
            if self.backup_settings['include_settings']:
                settings_files = ['settings.json', 'config.ini']
                for file in settings_files:
                    if os.path.exists(file):
                        zipf.write(file, f'settings/{file}')
                        current_file += 1
                        progress = int((current_file / total_files) * 100)
                        self.backup_progress.emit(progress, f"نسخ {file}...")
                        
            # ملفات السجلات
            if self.backup_settings['include_logs']:
                log_files = ['app.log', 'error.log', 'access.log']
                for file in log_files:
                    if os.path.exists(file):
                        zipf.write(file, f'logs/{file}')
                        current_file += 1
                        progress = int((current_file / total_files) * 100)
                        self.backup_progress.emit(progress, f"نسخ {file}...")
                        
            # ملفات البيانات
            data_files = ['employees.xlsx', 'example_data.xlsx']
            for file in data_files:
                if os.path.exists(file):
                    zipf.write(file, f'data/{file}')
                    current_file += 1
                    progress = int((current_file / total_files) * 100)
                    self.backup_progress.emit(progress, f"نسخ {file}...")
                    
            # معلومات النسخة الاحتياطية
            backup_info = {
                'backup_date': datetime.now().isoformat(),
                'backup_type': 'compressed',
                'files_count': current_file,
                'database_size': os.path.getsize('vacation_system.db') if os.path.exists('vacation_system.db') else 0,
                'system_version': '3.0',
                'backup_settings': self.backup_settings
            }
            
            zipf.writestr('backup_info.json', json.dumps(backup_info, ensure_ascii=False, indent=2))
            
    def create_folder_backup(self, backup_path):
        """إنشاء نسخة احتياطية كمجلد"""
        os.makedirs(backup_path, exist_ok=True)
        
        # قاعدة البيانات
        if os.path.exists('vacation_system.db'):
            shutil.copy2('vacation_system.db', os.path.join(backup_path, 'vacation_system.db'))
            self.backup_progress.emit(25, "نسخ قاعدة البيانات...")
            
        # ملفات الإعدادات
        if self.backup_settings['include_settings']:
            settings_dir = os.path.join(backup_path, 'settings')
            os.makedirs(settings_dir, exist_ok=True)
            
            settings_files = ['settings.json', 'config.ini']
            for file in settings_files:
                if os.path.exists(file):
                    shutil.copy2(file, os.path.join(settings_dir, file))
                    
            self.backup_progress.emit(50, "نسخ الإعدادات...")
            
        # ملفات السجلات
        if self.backup_settings['include_logs']:
            logs_dir = os.path.join(backup_path, 'logs')
            os.makedirs(logs_dir, exist_ok=True)
            
            log_files = ['app.log', 'error.log', 'access.log']
            for file in log_files:
                if os.path.exists(file):
                    shutil.copy2(file, os.path.join(logs_dir, file))
                    
            self.backup_progress.emit(75, "نسخ السجلات...")
            
        # ملفات البيانات
        data_dir = os.path.join(backup_path, 'data')
        os.makedirs(data_dir, exist_ok=True)
        
        data_files = ['employees.xlsx', 'example_data.xlsx']
        for file in data_files:
            if os.path.exists(file):
                shutil.copy2(file, os.path.join(data_dir, file))
                
        self.backup_progress.emit(100, "اكتمال النسخ الاحتياطي!")
        
    def count_backup_files(self):
        """حساب عدد الملفات للنسخ الاحتياطي"""
        count = 0
        
        # قاعدة البيانات
        if os.path.exists('vacation_system.db'):
            count += 1
            
        # ملفات الإعدادات
        if self.backup_settings['include_settings']:
            settings_files = ['settings.json', 'config.ini']
            count += sum(1 for file in settings_files if os.path.exists(file))
            
        # ملفات السجلات
        if self.backup_settings['include_logs']:
            log_files = ['app.log', 'error.log', 'access.log']
            count += sum(1 for file in log_files if os.path.exists(file))
            
        # ملفات البيانات
        data_files = ['employees.xlsx', 'example_data.xlsx']
        count += sum(1 for file in data_files if os.path.exists(file))
        
        return max(count, 1)  # تجنب القسمة على صفر
        
    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backup_files = []
            
            # العثور على جميع ملفات النسخ الاحتياطي
            for file in os.listdir(self.backup_settings['backup_location']):
                if file.startswith('backup_'):
                    file_path = os.path.join(self.backup_settings['backup_location'], file)
                    backup_files.append((file_path, os.path.getmtime(file_path)))
                    
            # ترتيب حسب التاريخ
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # حذف النسخ الزائدة
            if len(backup_files) > self.backup_settings['max_backups']:
                for file_path, _ in backup_files[self.backup_settings['max_backups']:]:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                        
        except Exception as e:
            print(f"خطأ في تنظيف النسخ الاحتياطية: {e}")
            
    def restore_backup(self, backup_path):
        """استعادة نسخة احتياطية"""
        try:
            if backup_path.endswith('.zip'):
                return self.restore_compressed_backup(backup_path)
            else:
                return self.restore_folder_backup(backup_path)
                
        except Exception as e:
            return False, str(e)
            
    def restore_compressed_backup(self, backup_path):
        """استعادة نسخة احتياطية مضغوطة"""
        with zipfile.ZipFile(backup_path, 'r') as zipf:
            # استخراج قاعدة البيانات
            if 'database/vacation_system.db' in zipf.namelist():
                zipf.extract('database/vacation_system.db', '.')
                shutil.move('database/vacation_system.db', 'vacation_system.db')
                shutil.rmtree('database', ignore_errors=True)
                
            # استخراج الإعدادات
            for file in zipf.namelist():
                if file.startswith('settings/'):
                    zipf.extract(file, '.')
                    filename = os.path.basename(file)
                    shutil.move(file, filename)
                    
            # تنظيف المجلدات المؤقتة
            shutil.rmtree('settings', ignore_errors=True)
            shutil.rmtree('logs', ignore_errors=True)
            shutil.rmtree('data', ignore_errors=True)
            
        return True, "تم استعادة النسخة الاحتياطية بنجاح"
        
    def restore_folder_backup(self, backup_path):
        """استعادة نسخة احتياطية من مجلد"""
        # قاعدة البيانات
        db_path = os.path.join(backup_path, 'vacation_system.db')
        if os.path.exists(db_path):
            shutil.copy2(db_path, 'vacation_system.db')
            
        # الإعدادات
        settings_dir = os.path.join(backup_path, 'settings')
        if os.path.exists(settings_dir):
            for file in os.listdir(settings_dir):
                shutil.copy2(os.path.join(settings_dir, file), file)
                
        return True, "تم استعادة النسخة الاحتياطية بنجاح"
        
    def get_backup_info(self, backup_path):
        """الحصول على معلومات النسخة الاحتياطية"""
        try:
            if backup_path.endswith('.zip'):
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    if 'backup_info.json' in zipf.namelist():
                        info_content = zipf.read('backup_info.json').decode('utf-8')
                        return json.loads(info_content)
                        
            else:
                info_file = os.path.join(backup_path, 'backup_info.json')
                if os.path.exists(info_file):
                    with open(info_file, 'r', encoding='utf-8') as f:
                        return json.load(f)
                        
        except Exception as e:
            print(f"خطأ في قراءة معلومات النسخة الاحتياطية: {e}")
            
        return None
        
    def schedule_auto_backup(self):
        """جدولة النسخ الاحتياطي التلقائي"""
        if not self.backup_settings['auto_backup']:
            return
            
        # هنا سيتم إضافة جدولة النسخ الاحتياطي التلقائي
        # حسب التكرار المحدد (يومي، أسبوعي، شهري)
        pass

class ModernNotificationsWindow(ModernDialog):
    """نافذة إدارة الإشعارات المتقدمة"""
    
    def __init__(self):
        super().__init__("🔔 إدارة الإشعارات المتقدمة", 800, 600)
        self.notification_manager = NotificationManager()
        self.setup_notifications_content()
        
    def setup_notifications_content(self):
        """إعداد محتوى الإشعارات"""
        layout = QVBoxLayout(self.content_area)
        layout.setSpacing(20)
        
        # إعدادات الإشعارات
        self.create_notification_settings(layout)
        
        # سجل الإشعارات
        self.create_notifications_log(layout)
        
        # اختبار الإشعارات
        self.create_test_section(layout)
        
        # إعداد الأزرار
        self.add_button("💾 حفظ الإعدادات", self.save_settings, "primary")
        self.add_button("🔔 اختبار الإشعار", self.test_notification, "secondary")
        self.add_button("🗑️ مسح السجل", self.clear_log, "danger")
        self.add_button("❌ إغلاق", self.accept, "secondary")
        
    def create_notification_settings(self, layout):
        """إنشاء إعدادات الإشعارات"""
        settings_frame = QFrame()
        settings_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #667eea, stop: 1 #764ba2
                );
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        settings_layout = QVBoxLayout(settings_frame)
        
        # العنوان
        title_label = QLabel("⚙️ إعدادات الإشعارات")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: white;
            margin-bottom: 15px;
        """)
        settings_layout.addWidget(title_label)
        
        # الإعدادات في شبكة
        settings_grid = QGridLayout()
        
        # تفعيل الإشعارات
        self.enable_notifications = QCheckBox("تفعيل الإشعارات")
        self.enable_notifications.setChecked(True)
        self.enable_notifications.setStyleSheet("""
            QCheckBox {
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        settings_grid.addWidget(self.enable_notifications, 0, 0)
        
        # إشعارات الطلبات الجديدة
        self.notify_new_requests = QCheckBox("إشعار عند الطلبات الجديدة")
        self.notify_new_requests.setChecked(True)
        self.notify_new_requests.setStyleSheet("""
            QCheckBox {
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        settings_grid.addWidget(self.notify_new_requests, 0, 1)
        
        # إشعارات الرصيد المنخفض
        self.notify_low_balance = QCheckBox("إشعار عند انخفاض الرصيد")
        self.notify_low_balance.setChecked(True)
        self.notify_low_balance.setStyleSheet("""
            QCheckBox {
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        settings_grid.addWidget(self.notify_low_balance, 1, 0)
        
        # إشعارات النسخ الاحتياطي
        self.notify_backup = QCheckBox("إشعار عند النسخ الاحتياطي")
        self.notify_backup.setChecked(True)
        self.notify_backup.setStyleSheet("""
            QCheckBox {
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        settings_grid.addWidget(self.notify_backup, 1, 1)
        
        settings_layout.addLayout(settings_grid)
        layout.addWidget(settings_frame)
        
    def create_notifications_log(self, layout):
        """إنشاء سجل الإشعارات"""
        log_frame = QFrame()
        log_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 20px;
            }
        """)
        
        log_layout = QVBoxLayout(log_frame)
        
        # العنوان
        log_title = QLabel("📋 سجل الإشعارات")
        log_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        log_layout.addWidget(log_title)
        
        # جدول السجل
        self.log_table = QTableWidget()
        self.log_table.setColumnCount(4)
        self.log_table.setHorizontalHeaderLabels(["النوع", "العنوان", "الرسالة", "التاريخ"])
        self.log_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f1f3f4;
            }
            QHeaderView::section {
                background: #667eea;
                color: white;
                border: none;
                padding: 10px;
                font-weight: bold;
            }
        """)
        
        # تحميل السجل
        self.load_notifications_log()
        
        log_layout.addWidget(self.log_table)
        layout.addWidget(log_frame)
        
    def create_test_section(self, layout):
        """إنشاء قسم اختبار الإشعارات"""
        test_frame = QFrame()
        test_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 10px;
                border: 2px solid #dee2e6;
                padding: 20px;
            }
        """)
        
        test_layout = QVBoxLayout(test_frame)
        
        # العنوان
        test_title = QLabel("🧪 اختبار الإشعارات")
        test_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        test_layout.addWidget(test_title)
        
        # أزرار الاختبار
        test_buttons_layout = QHBoxLayout()
        
        test_types = [
            ("🔔 إشعار عادي", "normal"),
            ("⚠️ تحذير", "warning"),
            ("❌ خطأ", "error"),
            ("✅ نجح", "success")
        ]
        
        for text, notification_type in test_types:
            btn = QPushButton(text)
            btn.setStyleSheet("""
                QPushButton {
                    background: #3498db;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    font-weight: bold;
                    margin: 5px;
                }
                QPushButton:hover {
                    background: #2980b9;
                }
            """)
            btn.clicked.connect(lambda checked, t=notification_type: self.test_notification_type(t))
            test_buttons_layout.addWidget(btn)
            
        test_layout.addLayout(test_buttons_layout)
        layout.addWidget(test_frame)
        
    def load_notifications_log(self):
        """تحميل سجل الإشعارات"""
        # بيانات وهمية للاختبار
        sample_notifications = [
            ["طلب جديد", "طلب إجازة جديد", "تم تقديم طلب إجازة من أحمد محمد", "2024-11-21 10:30:00"],
            ["رصيد منخفض", "تحذير رصيد", "رصيد فاطمة عبد الله أقل من 5 أيام", "2024-11-21 09:15:00"],
            ["نسخ احتياطي", "نسخ احتياطي مكتمل", "تم إنشاء نسخة احتياطية بنجاح", "2024-11-21 08:00:00"],
            ["طلب متأخر", "طلب متأخر في المراجعة", "طلب محمد عبد الرحمن متأخر 3 أيام", "2024-11-21 07:45:00"],
        ]
        
        self.log_table.setRowCount(len(sample_notifications))
        
        for row, row_data in enumerate(sample_notifications):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                
                # تلوين حسب النوع
                if col == 0:
                    if "تحذير" in value or "متأخر" in value:
                        item.setBackground(QColor(255, 243, 205))
                    elif "جديد" in value:
                        item.setBackground(QColor(212, 237, 218))
                    elif "نسخ" in value:
                        item.setBackground(QColor(209, 236, 241))
                        
                self.log_table.setItem(row, col, item)
                
        self.log_table.resizeColumnsToContents()
        
    def test_notification_type(self, notification_type):
        """اختبار نوع إشعار محدد"""
        messages = {
            "normal": ("إشعار عادي", "هذا إشعار عادي للاختبار"),
            "warning": ("تحذير", "هذا تحذير للاختبار"),
            "error": ("خطأ", "هذا إشعار خطأ للاختبار"),
            "success": ("نجح", "هذا إشعار نجح للاختبار")
        }
        
        title, message = messages.get(notification_type, ("اختبار", "إشعار اختبار"))
        self.notification_manager.send_notification(notification_type, title, message)
        
    def test_notification(self):
        """اختبار إشعار عام"""
        self.notification_manager.send_notification(
            "test", 
            "اختبار الإشعارات", 
            "هذا إشعار اختبار لتجربة النظام"
        )
        
    def save_settings(self):
        """حفظ إعدادات الإشعارات"""
        settings = {
            'enable_notifications': self.enable_notifications.isChecked(),
            'notify_new_requests': self.notify_new_requests.isChecked(),
            'notify_low_balance': self.notify_low_balance.isChecked(),
            'notify_backup': self.notify_backup.isChecked()
        }
        
        # حفظ الإعدادات
        self.notification_manager.notifications_enabled = settings['enable_notifications']
        self.notification_manager.notification_rules.update({
            'new_request': settings['notify_new_requests'],
            'low_balance': settings['notify_low_balance'],
            'system_backup': settings['notify_backup']
        })
        
        QMessageBox.information(self, "تم", "تم حفظ إعدادات الإشعارات بنجاح!")
        
    def clear_log(self):
        """مسح سجل الإشعارات"""
        reply = QMessageBox.question(
            self, "تأكيد", 
            "هل تريد مسح سجل الإشعارات؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.log_table.setRowCount(0)
            QMessageBox.information(self, "تم", "تم مسح سجل الإشعارات")

class ModernBackupWindow(ModernDialog):
    """نافذة إدارة النسخ الاحتياطي المتقدمة"""
    
    def __init__(self):
        super().__init__("💾 إدارة النسخ الاحتياطي المتقدمة", 850, 700)
        self.backup_manager = BackupManager()
        self.setup_backup_content()
        
    def setup_backup_content(self):
        """إعداد محتوى النسخ الاحتياطي"""
        layout = QVBoxLayout(self.content_area)
        layout.setSpacing(20)
        
        # إعدادات النسخ الاحتياطي
        self.create_backup_settings(layout)
        
        # قائمة النسخ الاحتياطية
        self.create_backup_list(layout)
        
        # شريط التقدم
        self.create_progress_section(layout)
        
        # إعداد الأزرار
        self.add_button("💾 إنشاء نسخة احتياطية", self.create_backup, "primary")
        self.add_button("🔄 استعادة نسخة", self.restore_backup, "warning")
        self.add_button("🗑️ حذف نسخة", self.delete_backup, "danger")
        self.add_button("❌ إغلاق", self.accept, "secondary")
        
        # ربط الإشارات
        self.backup_manager.backup_started.connect(self.on_backup_started)
        self.backup_manager.backup_progress.connect(self.on_backup_progress)
        self.backup_manager.backup_completed.connect(self.on_backup_completed)
        
    def create_backup_settings(self, layout):
        """إنشاء إعدادات النسخ الاحتياطي"""
        settings_frame = QFrame()
        settings_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #27ae60, stop: 1 #229954
                );
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        settings_layout = QVBoxLayout(settings_frame)
        
        # العنوان
        title_label = QLabel("⚙️ إعدادات النسخ الاحتياطي")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: white;
            margin-bottom: 15px;
        """)
        settings_layout.addWidget(title_label)
        
        # الإعدادات
        settings_grid = QGridLayout()
        
        # النسخ الاحتياطي التلقائي
        self.auto_backup = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        self.auto_backup.setChecked(True)
        self.auto_backup.setStyleSheet("""
            QCheckBox {
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        settings_grid.addWidget(self.auto_backup, 0, 0)
        
        # ضغط النسخ
        self.compress_backup = QCheckBox("ضغط النسخ الاحتياطية")
        self.compress_backup.setChecked(True)
        self.compress_backup.setStyleSheet("""
            QCheckBox {
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        settings_grid.addWidget(self.compress_backup, 0, 1)
        
        # تكرار النسخ
        frequency_label = QLabel("تكرار النسخ:")
        frequency_label.setStyleSheet("color: white; font-weight: bold;")
        settings_grid.addWidget(frequency_label, 1, 0)
        
        self.frequency_combo = QComboBox()
        self.frequency_combo.addItems(["يومي", "أسبوعي", "شهري"])
        self.frequency_combo.setCurrentText("يومي")
        self.frequency_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: none;
                border-radius: 8px;
                padding: 8px;
                font-weight: bold;
            }
        """)
        settings_grid.addWidget(self.frequency_combo, 1, 1)
        
        # عدد النسخ المحفوظة
        max_backups_label = QLabel("عدد النسخ المحفوظة:")
        max_backups_label.setStyleSheet("color: white; font-weight: bold;")
        settings_grid.addWidget(max_backups_label, 2, 0)
        
        self.max_backups_spin = QSpinBox()
        self.max_backups_spin.setRange(1, 50)
        self.max_backups_spin.setValue(10)
        self.max_backups_spin.setStyleSheet("""
            QSpinBox {
                background: white;
                border: none;
                border-radius: 8px;
                padding: 8px;
                font-weight: bold;
            }
        """)
        settings_grid.addWidget(self.max_backups_spin, 2, 1)
        
        settings_layout.addLayout(settings_grid)
        layout.addWidget(settings_frame)
        
    def create_backup_list(self, layout):
        """إنشاء قائمة النسخ الاحتياطية"""
        list_frame = QFrame()
        list_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 20px;
            }
        """)
        
        list_layout = QVBoxLayout(list_frame)
        
        # العنوان
        list_title = QLabel("📋 النسخ الاحتياطية المتاحة")
        list_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        list_layout.addWidget(list_title)
        
        # جدول النسخ
        self.backup_table = QTableWidget()
        self.backup_table.setColumnCount(5)
        self.backup_table.setHorizontalHeaderLabels(["اسم النسخة", "التاريخ", "الحجم", "النوع", "الحالة"])
        self.backup_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f1f3f4;
            }
            QHeaderView::section {
                background: #27ae60;
                color: white;
                border: none;
                padding: 10px;
                font-weight: bold;
            }
        """)
        
        # تحميل قائمة النسخ
        self.load_backup_list()
        
        list_layout.addWidget(self.backup_table)
        layout.addWidget(list_frame)
        
    def create_progress_section(self, layout):
        """إنشاء قسم التقدم"""
        progress_frame = QFrame()
        progress_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 10px;
                border: 2px solid #dee2e6;
                padding: 20px;
            }
        """)
        
        progress_layout = QVBoxLayout(progress_frame)
        
        # العنوان
        progress_title = QLabel("📊 تقدم العملية")
        progress_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        progress_layout.addWidget(progress_title)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #27ae60;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                background: white;
            }
            QProgressBar::chunk {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #27ae60, stop: 1 #229954
                );
                border-radius: 6px;
            }
        """)
        progress_layout.addWidget(self.progress_bar)
        
        # رسالة التقدم
        self.progress_label = QLabel("جاهز للنسخ الاحتياطي")
        self.progress_label.setStyleSheet("""
            font-size: 14px;
            color: #2c3e50;
            margin-top: 10px;
        """)
        progress_layout.addWidget(self.progress_label)
        
        layout.addWidget(progress_frame)
        
    def load_backup_list(self):
        """تحميل قائمة النسخ الاحتياطية"""
        # بيانات وهمية للاختبار
        sample_backups = [
            ["backup_manual_20241121_143000.zip", "2024-11-21 14:30:00", "2.3 MB", "يدوي", "مكتمل"],
            ["backup_auto_20241121_080000.zip", "2024-11-21 08:00:00", "2.1 MB", "تلقائي", "مكتمل"],
            ["backup_manual_20241120_160000.zip", "2024-11-20 16:00:00", "2.2 MB", "يدوي", "مكتمل"],
            ["backup_auto_20241120_080000.zip", "2024-11-20 08:00:00", "2.0 MB", "تلقائي", "مكتمل"],
        ]
        
        self.backup_table.setRowCount(len(sample_backups))
        
        for row, row_data in enumerate(sample_backups):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                
                # تلوين حسب النوع
                if col == 3:  # عمود النوع
                    if value == "تلقائي":
                        item.setBackground(QColor(209, 236, 241))
                    elif value == "يدوي":
                        item.setBackground(QColor(212, 237, 218))
                        
                # تلوين حسب الحالة
                elif col == 4:  # عمود الحالة
                    if value == "مكتمل":
                        item.setBackground(QColor(212, 237, 218))
                    elif value == "فشل":
                        item.setBackground(QColor(248, 215, 218))
                        
                self.backup_table.setItem(row, col, item)
                
        self.backup_table.resizeColumnsToContents()
        
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        reply = QMessageBox.question(
            self, "تأكيد", 
            "هل تريد إنشاء نسخة احتياطية جديدة؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # تطبيق الإعدادات
            self.backup_manager.backup_settings.update({
                'auto_backup': self.auto_backup.isChecked(),
                'compress_backups': self.compress_backup.isChecked(),
                'backup_frequency': self.frequency_combo.currentText(),
                'max_backups': self.max_backups_spin.value()
            })
            
            # إنشاء النسخة
            self.backup_manager.create_backup('manual')
            
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        current_row = self.backup_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد نسخة احتياطية للاستعادة")
            return
            
        backup_name = self.backup_table.item(current_row, 0).text()
        
        reply = QMessageBox.critical(
            self, "تحذير", 
            f"هل تريد استعادة النسخة الاحتياطية؟\n\n"
            f"النسخة: {backup_name}\n\n"
            "تحذير: سيتم استبدال البيانات الحالية!",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            backup_path = os.path.join(self.backup_manager.backup_settings['backup_location'], backup_name)
            success, message = self.backup_manager.restore_backup(backup_path)
            
            if success:
                QMessageBox.information(self, "نجح", message)
            else:
                QMessageBox.critical(self, "خطأ", message)
                
    def delete_backup(self):
        """حذف نسخة احتياطية"""
        current_row = self.backup_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد نسخة احتياطية للحذف")
            return
            
        backup_name = self.backup_table.item(current_row, 0).text()
        
        reply = QMessageBox.question(
            self, "تأكيد الحذف", 
            f"هل تريد حذف النسخة الاحتياطية؟\n\n{backup_name}",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            backup_path = os.path.join(self.backup_manager.backup_settings['backup_location'], backup_name)
            try:
                if os.path.exists(backup_path):
                    os.remove(backup_path)
                    QMessageBox.information(self, "تم", "تم حذف النسخة الاحتياطية")
                    self.load_backup_list()
                else:
                    QMessageBox.warning(self, "خطأ", "النسخة الاحتياطية غير موجودة")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"لا يمكن حذف النسخة:\n{str(e)}")
                
    def on_backup_started(self):
        """عند بدء النسخ الاحتياطي"""
        self.progress_bar.setValue(0)
        self.progress_label.setText("جاري إنشاء النسخة الاحتياطية...")
        
    def on_backup_progress(self, value, message):
        """تحديث تقدم النسخ الاحتياطي"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)
        
    def on_backup_completed(self, success, message):
        """عند اكتمال النسخ الاحتياطي"""
        if success:
            self.progress_bar.setValue(100)
            self.progress_label.setText("تم إنشاء النسخة الاحتياطية بنجاح")
            QMessageBox.information(self, "نجح", f"تم إنشاء النسخة الاحتياطية:\n{message}")
            self.load_backup_list()
        else:
            self.progress_label.setText("فشل في إنشاء النسخة الاحتياطية")
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{message}")

def test_notifications_backup():
    """اختبار نوافذ الإشعارات والنسخ الاحتياطي"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # اختبار نافذة الإشعارات
    notifications_window = ModernNotificationsWindow()
    notifications_window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    test_notifications_backup()
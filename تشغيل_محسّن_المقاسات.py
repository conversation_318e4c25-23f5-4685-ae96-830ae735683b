#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل النظام بمقاسات محسّنة ومناسبة لجميع الشاشات
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# إضافة المسار الحالي
sys.path.append(os.path.dirname(__file__))

# استيراد الواجهات المحسّنة
from إصلاح_مقاسات_النوافذ import OptimizedLoginWindow, OptimizedMainWindow, get_screen_info, calculate_optimal_sizes

def show_system_info():
    """عرض معلومات النظام والشاشة"""
    print("🚀 تشغيل نظام إدارة الإجازات المحسّن...")
    print("=" * 50)
    
    # معلومات الشاشة
    screen_info = get_screen_info()
    print(f"📺 معلومات الشاشة:")
    print(f"   📐 الدقة: {screen_info['width']} × {screen_info['height']} بكسل")
    print(f"   📏 المساحة المتاحة: {screen_info['available_width']} × {screen_info['available_height']} بكسل")
    
    # المقاسات المحسوبة
    sizes = calculate_optimal_sizes()
    print(f"\n📊 المقاسات المحسوبة:")
    print(f"   🏠 النافذة الرئيسية: {sizes['main_window']['width']} × {sizes['main_window']['height']}")
    print(f"   🔐 نافذة تسجيل الدخول: {sizes['login_window']['width']} × {sizes['login_window']['height']}")
    print(f"   📝 نوافذ الطلبات: {sizes['request_window']['width']} × {sizes['request_window']['height']}")
    
    print("\n✨ التحسينات المطبقة:")
    print("   • مقاسات متكيّفة مع حجم الشاشة")
    print("   • نوافذ مرتبة في وسط الشاشة")
    print("   • خط Sakkal Majalla واضح")
    print("   • مربعات نص بأحجام مناسبة")
    print("   • ألوان متباينة للوضوح")
    print("=" * 50)

def main():
    """الدالة الرئيسية للتشغيل"""
    app = QApplication(sys.argv)
    
    # عرض معلومات النظام
    show_system_info()
    
    # تطبيق الخط العربي
    font = QFont("Sakkal Majalla", 10)
    app.setFont(font)
    
    print("🔐 فتح نافذة تسجيل الدخول...")
    
    # نافذة تسجيل الدخول المحسّنة
    login_window = OptimizedLoginWindow()
    
    if login_window.exec_() == QDialog.Accepted:
        print("✅ تم تسجيل الدخول بنجاح!")
        print("🏢 فتح النافذة الرئيسية...")
        
        # النافذة الرئيسية المحسّنة
        main_window = OptimizedMainWindow(login_window.user_data)
        main_window.show()
        
        print("🎉 النظام جاهز للاستخدام بمقاسات محسّنة!")
        print("\n📋 الوظائف المتاحة:")
        print("   📝 طلب إجازة يومية")
        print("   ⏱️ طلب إجازة ساعية")
        print("   🔍 البحث والتعديل")
        print("   📊 التقارير والإحصائيات")
        print("   👥 إدارة المستخدمين")
        print("   ⚙️ الإعدادات")
        
        return app.exec_()
    else:
        print("❌ تم إلغاء تسجيل الدخول")
        return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except Exception as e:
        print(f"❌ حدث خطأ: {e}")
        input("اضغط Enter للإغلاق...")
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط لنظام إدارة الإجازات
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد المكتبات"""
    try:
        print("اختبار استيراد المكتبات...")
        
        import pandas as pd
        print("✓ pandas - تم")
        
        import openpyxl
        print("✓ openpyxl - تم")
        
        from PyQt5.QtWidgets import QApplication
        print("✓ PyQt5 - تم")
        
        import sqlite3
        print("✓ sqlite3 - تم")
        
        from database import VacationDatabase
        print("✓ database.py - تم")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في الاستيراد: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    try:
        print("\nاختبار قاعدة البيانات...")
        
        from database import VacationDatabase
        
        # إنشاء قاعدة البيانات
        db = VacationDatabase('test_db.db')
        print("✓ إنشاء قاعدة البيانات - تم")
        
        # اختبار تسجيل الدخول
        user = db.authenticate_user('admin', 'admin123')
        if user:
            print("✓ تسجيل الدخول - تم")
        else:
            print("✗ تسجيل الدخول - فشل")
            
        # اختبار إضافة طلب إجازة
        success, message = db.add_daily_request(
            'محمد أحمد', '12345', 'موظف', 'قسم الإدارة', 
            'سنوية', '2024-07-10', 5
        )
        
        if success:
            print("✓ إضافة طلب إجازة - تم")
        else:
            print(f"✗ إضافة طلب إجازة - فشل: {message}")
            
        # اختبار حساب الرصيد
        balance = db.get_employee_balance('محمد أحمد')
        print(f"✓ حساب الرصيد - تم: {balance}")
        
        # تنظيف
        os.remove('test_db.db')
        print("✓ تنظيف ملف الاختبار - تم")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في قاعدة البيانات: {e}")
        return False

def test_excel():
    """اختبار ملف Excel"""
    try:
        print("\nاختبار ملف Excel...")
        
        import pandas as pd
        
        # قراءة ملف Excel النموذجي
        df = pd.read_excel('نموذج_الرصيد_الابتدائي.xlsx')
        print("✓ قراءة ملف Excel - تم")
        print(f"عدد الصفوف: {len(df)}")
        print(f"الأعمدة: {list(df.columns)}")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في ملف Excel: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء اختبار نظام إدارة الإجازات")
    print("=" * 50)
    
    tests = [
        ("استيراد المكتبات", test_imports),
        ("قاعدة البيانات", test_database),
        ("ملف Excel", test_excel)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار: {test_name}")
        result = test_func()
        results.append((test_name, result))
        
        if result:
            print(f"✅ {test_name} - نجح")
        else:
            print(f"❌ {test_name} - فشل")
    
    print("\n" + "=" * 50)
    print("📊 نتائج الاختبارات:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  {test_name}: {status}")
    
    print(f"\nالمجموع: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! البرنامج جاهز للتشغيل")
        return True
    else:
        print("⚠️  يوجد اختبارات فاشلة، يرجى مراجعة الأخطاء")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أنماط الواجهة الحديثة والعصرية
"""

def get_modern_stylesheet():
    """إرجاع الأنماط العصرية للواجهة"""
    return """
    /* الخط العام */
    * {
        font-family: "Sakkal Majalla", "Arial", sans-serif;
    }
    
    /* الخلفية الرئيسية */
    QMainWindow {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 1, y2: 1,
            stop: 0 #1e3c72, stop: 1 #2a5298
        );
        font-family: "Sakkal Majalla", "Arial", sans-serif;
    }
    
    /* نافذة مركزية */
    QWidget#central_widget {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 1, y2: 1,
            stop: 0 #f8f9fa, stop: 1 #e9ecef
        );
        border-radius: 15px;
        margin: 10px;
    }
    
    /* شريط العنوان */
    QLabel#title_label {
        font-size: 32px;
        font-weight: bold;
        color: #2c3e50;
        background: qlineargradient(
            x1: 0, y1: 0, x2: 1, y2: 0,
            stop: 0 #3498db, stop: 1 #2980b9
        );
        border-radius: 10px;
        padding: 15px;
        margin: 10px;
        color: white;
        text-align: center;
    }
    
    /* شريط الترحيب */
    QLabel#welcome_label {
        font-size: 18px;
        color: #34495e;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 8px;
        padding: 10px;
        margin: 5px;
    }
    
    /* الأزرار الرئيسية */
    QPushButton {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #3498db, stop: 1 #2980b9
        );
        border: none;
        border-radius: 15px;
        color: white;
        font-size: 14px;
        font-weight: bold;
        padding: 15px;
        margin: 8px;
        min-height: 50px;
        text-align: center;
    }
    
    QPushButton:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #5dade2, stop: 1 #3498db
        );
        transform: translateY(-2px);
    }
    
    QPushButton:pressed {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #2980b9, stop: 1 #1f618d
        );
    }
    
    /* أزرار خاصة */
    QPushButton#import_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #27ae60, stop: 1 #229954
        );
    }
    
    QPushButton#import_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #58d68d, stop: 1 #27ae60
        );
    }
    
    QPushButton#daily_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #e74c3c, stop: 1 #c0392b
        );
    }
    
    QPushButton#daily_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #ec7063, stop: 1 #e74c3c
        );
    }
    
    QPushButton#hourly_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #f39c12, stop: 1 #e67e22
        );
    }
    
    QPushButton#hourly_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #f7dc6f, stop: 1 #f39c12
        );
    }
    
    QPushButton#add_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #9b59b6, stop: 1 #8e44ad
        );
    }
    
    QPushButton#add_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #bb8fce, stop: 1 #9b59b6
        );
    }
    
    QPushButton#report_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #1abc9c, stop: 1 #16a085
        );
    }
    
    QPushButton#report_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #7fb3d3, stop: 1 #1abc9c
        );
    }
    
    QPushButton#search_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #34495e, stop: 1 #2c3e50
        );
    }
    
    QPushButton#search_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #5d6d7e, stop: 1 #34495e
        );
    }
    
    QPushButton#edit_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #ff6b6b, stop: 1 #ee5a52
        );
    }
    
    QPushButton#edit_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #ff8a80, stop: 1 #ff6b6b
        );
    }
    
    QPushButton#delete_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #e91e63, stop: 1 #c2185b
        );
    }
    
    QPushButton#delete_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #f06292, stop: 1 #e91e63
        );
    }
    
    /* النوافذ الفرعية */
    QDialog {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 1, y2: 1,
            stop: 0 #f8f9fa, stop: 1 #e9ecef
        );
        border-radius: 15px;
    }
    
    /* الحقول النصية المحسّنة */
    QLineEdit {
        font-family: "Sakkal Majalla", "Arial", sans-serif;
        background: white;
        border: 2px solid #bdc3c7;
        border-radius: 12px;
        padding: 15px;
        font-size: 16px;
        font-weight: bold;
        color: #1a1a1a;
        min-height: 50px;
        max-height: 65px;
        selection-background-color: #3498db;
    }
    
    QLineEdit:focus {
        border: 2px solid #3498db;
        background: #ffffff;
        color: #000000;
    }
    
    QLineEdit:hover {
        border: 2px solid #5dade2;
        background: #ffffff;
    }
    
    QLineEdit::placeholder {
        color: #95a5a6;
        font-style: italic;
        font-size: 14px;
    }
    
    /* مناطق النص المتعددة */
    QTextEdit {
        font-family: "Sakkal Majalla", "Arial", sans-serif;
        background: white;
        border: 2px solid #bdc3c7;
        border-radius: 12px;
        padding: 15px;
        font-size: 16px;
        font-weight: bold;
        color: #1a1a1a;
        min-height: 80px;
        max-height: 120px;
        selection-background-color: #3498db;
    }
    
    QTextEdit:focus {
        border: 2px solid #3498db;
        background: #ffffff;
        color: #000000;
    }
    
    QTextEdit:hover {
        border: 2px solid #5dade2;
        background: #ffffff;
    }
    
    /* حقول الأرقام */
    QSpinBox, QDoubleSpinBox {
        font-family: "Sakkal Majalla", "Arial", sans-serif;
        background: white;
        border: 2px solid #bdc3c7;
        border-radius: 12px;
        padding: 15px;
        font-size: 16px;
        font-weight: bold;
        color: #1a1a1a;
        min-height: 50px;
        max-height: 65px;
    }
    
    /* حقول التواريخ والأوقات */
    QDateEdit, QTimeEdit, QDateTimeEdit {
        font-family: "Sakkal Majalla", "Arial", sans-serif;
        background: white;
        border: 2px solid #bdc3c7;
        border-radius: 12px;
        padding: 15px;
        font-size: 16px;
        font-weight: bold;
        color: #1a1a1a;
        min-height: 50px;
        max-height: 65px;
    }
    
    /* القوائم المنسدلة المحسّنة */
    QComboBox {
        font-family: "Sakkal Majalla", "Arial", sans-serif;
        background: white;
        border: 2px solid #bdc3c7;
        border-radius: 12px;
        padding: 15px;
        font-size: 16px;
        font-weight: bold;
        color: #1a1a1a;
        min-height: 50px;
        max-height: 65px;
    }
    
    QComboBox:focus {
        border: 2px solid #3498db;
        background: #ffffff;
    }
    
    QComboBox:hover {
        border: 2px solid #5dade2;
        background: #ffffff;
    }
    
    QComboBox::drop-down {
        border: none;
        width: 30px;
    }
    
    QComboBox::down-arrow {
        image: url(down_arrow.png);
        width: 12px;
        height: 12px;
    }
    
    /* التسميات المحسّنة */
    QLabel {
        font-family: "Sakkal Majalla", "Arial", sans-serif;
        color: #2c3e50;
        font-size: 16px;
        font-weight: bold;
    }
    
    /* الجداول */
    QTableWidget {
        background: white;
        border: 2px solid #bdc3c7;
        border-radius: 10px;
        gridline-color: #ecf0f1;
        font-size: 13px;
    }
    
    QTableWidget::item {
        padding: 10px;
        border-bottom: 1px solid #ecf0f1;
    }
    
    QTableWidget::item:selected {
        background: #3498db;
        color: white;
    }
    
    QHeaderView::section {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #34495e, stop: 1 #2c3e50
        );
        color: white;
        font-weight: bold;
        padding: 15px;
        border: none;
        border-radius: 5px;
    }
    
    /* شريط التمرير */
    QScrollBar:vertical {
        background: #ecf0f1;
        width: 12px;
        border-radius: 6px;
    }
    
    QScrollBar::handle:vertical {
        background: #3498db;
        border-radius: 6px;
        min-height: 20px;
    }
    
    QScrollBar::handle:vertical:hover {
        background: #2980b9;
    }
    
    /* شريط الحالة */
    QStatusBar {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 1, y2: 0,
            stop: 0 #34495e, stop: 1 #2c3e50
        );
        color: white;
        font-weight: bold;
        padding: 8px;
        border-radius: 5px;
    }
    
    /* قائمة الطعام */
    QMenuBar {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 1, y2: 0,
            stop: 0 #2c3e50, stop: 1 #34495e
        );
        color: white;
        font-weight: bold;
        padding: 5px;
    }
    
    QMenuBar::item {
        background: transparent;
        padding: 8px 15px;
        border-radius: 5px;
    }
    
    QMenuBar::item:selected {
        background: #3498db;
    }
    
    QMenu {
        background: white;
        border: 2px solid #bdc3c7;
        border-radius: 8px;
        padding: 5px;
    }
    
    QMenu::item {
        padding: 10px 20px;
        border-radius: 5px;
    }
    
    QMenu::item:selected {
        background: #3498db;
        color: white;
    }
    
    /* مربعات الاختيار */
    QCheckBox {
        color: #2c3e50;
        font-size: 14px;
        spacing: 8px;
    }
    
    QCheckBox::indicator {
        width: 18px;
        height: 18px;
        border: 2px solid #bdc3c7;
        border-radius: 4px;
        background: white;
    }
    
    QCheckBox::indicator:checked {
        background: #3498db;
        border: 2px solid #2980b9;
    }
    
    /* أزرار الراديو */
    QRadioButton {
        color: #2c3e50;
        font-size: 14px;
        spacing: 8px;
    }
    
    QRadioButton::indicator {
        width: 18px;
        height: 18px;
        border: 2px solid #bdc3c7;
        border-radius: 9px;
        background: white;
    }
    
    QRadioButton::indicator:checked {
        background: #3498db;
        border: 2px solid #2980b9;
    }
    
    /* التبويبات */
    QTabWidget::pane {
        border: 2px solid #bdc3c7;
        border-radius: 10px;
        background: white;
    }
    
    QTabBar::tab {
        background: #ecf0f1;
        border: 2px solid #bdc3c7;
        border-bottom: none;
        border-radius: 8px 8px 0 0;
        padding: 12px 20px;
        margin-right: 2px;
        font-weight: bold;
        color: #2c3e50;
    }
    
    QTabBar::tab:selected {
        background: #3498db;
        color: white;
    }
    
    QTabBar::tab:hover {
        background: #d5dbdb;
    }
    
    /* شريط التقدم */
    QProgressBar {
        border: 2px solid #bdc3c7;
        border-radius: 10px;
        background: #ecf0f1;
        text-align: center;
        font-weight: bold;
        color: #2c3e50;
    }
    
    QProgressBar::chunk {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #3498db, stop: 1 #2980b9
        );
        border-radius: 8px;
    }
    """

def get_professional_login_stylesheet():
    """أنماط نافذة تسجيل الدخول الاحترافية"""
    return """
    QDialog {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 1, y2: 1,
            stop: 0 #1e3c72, stop: 1 #2a5298
        );
        border: 3px solid #34495e;
        border-radius: 25px;
        font-family: "Sakkal Majalla", "Arial", sans-serif;
    }
    
    QLabel#title_label {
        font-family: "Sakkal Majalla", "Arial", sans-serif;
        font-size: 32px;
        font-weight: bold;
        color: white;
        background: qlineargradient(
            x1: 0, y1: 0, x2: 1, y2: 0,
            stop: 0 #3498db, stop: 1 #2980b9
        );
        border: 2px solid #2c3e50;
        border-radius: 20px;
        padding: 25px;
        margin: 15px;
        text-align: center;
    }
    
    QLabel#subtitle_label {
        font-family: "Sakkal Majalla", "Arial", sans-serif;
        font-size: 18px;
        color: white;
        background: rgba(52, 73, 94, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 10px;
        padding: 12px;
        margin: 5px;
        text-align: center;
    }
    
    QLineEdit {
        font-family: "Sakkal Majalla", "Arial", sans-serif;
        background: white;
        border: 3px solid #34495e;
        border-radius: 18px;
        padding: 22px;
        font-size: 18px;
        font-weight: bold;
        color: #1a1a1a;
        min-height: 60px;
        max-height: 70px;
        selection-background-color: #3498db;
        margin: 10px 0px;
    }
    
    QLineEdit:focus {
        border: 3px solid #e67e22;
        background: #ffffff;
        color: #000000;
    }
    
    QLineEdit:hover {
        border: 3px solid #5dade2;
        background: #ffffff;
        color: #000000;
    }
    
    QLineEdit::placeholder {
        color: #95a5a6;
        font-style: italic;
        font-size: 16px;
    }
    
    QPushButton {
        font-family: "Sakkal Majalla", "Arial", sans-serif;
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #27ae60, stop: 1 #229954
        );
        border: 3px solid #1e8449;
        border-radius: 20px;
        color: white;
        font-size: 18px;
        font-weight: bold;
        padding: 18px;
        margin: 12px;
        min-height: 60px;
    }
    
    QPushButton:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #58d68d, stop: 1 #27ae60
        );
        border: 3px solid #27ae60;
    }
    
    QPushButton:pressed {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #1e8449, stop: 1 #145a32
        );
        border: 3px solid #145a32;
    }
    
    /* أزرار إضافية */
    QPushButton#cancel_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #e74c3c, stop: 1 #c0392b
        );
        border: 3px solid #a93226;
    }
    
    QPushButton#cancel_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #ec7063, stop: 1 #e74c3c
        );
        border: 3px solid #e74c3c;
    }
    """

def get_login_stylesheet():
    """أنماط نافذة تسجيل الدخول القديمة (للتوافق)"""
    return get_professional_login_stylesheet()

def get_card_style():
    """أنماط البطاقات"""
    return """
    QFrame {
        background: white;
        border-radius: 15px;
        border: 1px solid #e0e0e0;
        padding: 15px;
        margin: 10px;
    }
    
    QFrame:hover {
        border: 1px solid #3498db;
        background: #f8f9fa;
    }
    """

def get_icon_button_style():
    """أنماط الأزرار مع الأيقونات"""
    return """
    QPushButton {
        text-align: left;
        padding-left: 20px;
        border: none;
        border-radius: 12px;
        background: white;
        color: #2c3e50;
        font-size: 14px;
        font-weight: bold;
        min-height: 50px;
        margin: 5px;
        border: 2px solid #e0e0e0;
    }
    
    QPushButton:hover {
        background: #f8f9fa;
        border: 2px solid #3498db;
        color: #3498db;
    }
    
    QPushButton:pressed {
        background: #e3f2fd;
        border: 2px solid #2980b9;
        color: #2980b9;
    }
    """
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أنماط الواجهة الحديثة والعصرية
"""

def get_modern_stylesheet():
    """إرجاع الأنماط العصرية للواجهة"""
    return """
    /* الخلفية الرئيسية */
    QMainWindow {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 1, y2: 1,
            stop: 0 #1e3c72, stop: 1 #2a5298
        );
    }
    
    /* نافذة مركزية */
    QWidget#central_widget {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 1, y2: 1,
            stop: 0 #f8f9fa, stop: 1 #e9ecef
        );
        border-radius: 15px;
        margin: 10px;
    }
    
    /* شريط العنوان */
    QLabel#title_label {
        font-size: 32px;
        font-weight: bold;
        color: #2c3e50;
        background: qlineargradient(
            x1: 0, y1: 0, x2: 1, y2: 0,
            stop: 0 #3498db, stop: 1 #2980b9
        );
        border-radius: 10px;
        padding: 15px;
        margin: 10px;
        color: white;
        text-align: center;
    }
    
    /* شريط الترحيب */
    QLabel#welcome_label {
        font-size: 18px;
        color: #34495e;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 8px;
        padding: 10px;
        margin: 5px;
    }
    
    /* الأزرار الرئيسية */
    QPushButton {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #3498db, stop: 1 #2980b9
        );
        border: none;
        border-radius: 15px;
        color: white;
        font-size: 14px;
        font-weight: bold;
        padding: 15px;
        margin: 8px;
        min-height: 50px;
        text-align: center;
    }
    
    QPushButton:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #5dade2, stop: 1 #3498db
        );
        transform: translateY(-2px);
    }
    
    QPushButton:pressed {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #2980b9, stop: 1 #1f618d
        );
    }
    
    /* أزرار خاصة */
    QPushButton#import_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #27ae60, stop: 1 #229954
        );
    }
    
    QPushButton#import_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #58d68d, stop: 1 #27ae60
        );
    }
    
    QPushButton#daily_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #e74c3c, stop: 1 #c0392b
        );
    }
    
    QPushButton#daily_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #ec7063, stop: 1 #e74c3c
        );
    }
    
    QPushButton#hourly_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #f39c12, stop: 1 #e67e22
        );
    }
    
    QPushButton#hourly_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #f7dc6f, stop: 1 #f39c12
        );
    }
    
    QPushButton#add_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #9b59b6, stop: 1 #8e44ad
        );
    }
    
    QPushButton#add_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #bb8fce, stop: 1 #9b59b6
        );
    }
    
    QPushButton#report_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #1abc9c, stop: 1 #16a085
        );
    }
    
    QPushButton#report_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #7fb3d3, stop: 1 #1abc9c
        );
    }
    
    QPushButton#search_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #34495e, stop: 1 #2c3e50
        );
    }
    
    QPushButton#search_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #5d6d7e, stop: 1 #34495e
        );
    }
    
    QPushButton#edit_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #ff6b6b, stop: 1 #ee5a52
        );
    }
    
    QPushButton#edit_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #ff8a80, stop: 1 #ff6b6b
        );
    }
    
    QPushButton#delete_button {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #e91e63, stop: 1 #c2185b
        );
    }
    
    QPushButton#delete_button:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #f06292, stop: 1 #e91e63
        );
    }
    
    /* النوافذ الفرعية */
    QDialog {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 1, y2: 1,
            stop: 0 #f8f9fa, stop: 1 #e9ecef
        );
        border-radius: 15px;
    }
    
    /* الحقول النصية */
    QLineEdit {
        background: white;
        border: 2px solid #bdc3c7;
        border-radius: 10px;
        padding: 10px;
        font-size: 14px;
        color: #2c3e50;
    }
    
    QLineEdit:focus {
        border: 2px solid #3498db;
        background: #f8f9fa;
    }
    
    /* القوائم المنسدلة */
    QComboBox {
        background: white;
        border: 2px solid #bdc3c7;
        border-radius: 10px;
        padding: 10px;
        font-size: 14px;
        color: #2c3e50;
        min-height: 20px;
    }
    
    QComboBox:focus {
        border: 2px solid #3498db;
    }
    
    QComboBox::drop-down {
        border: none;
        width: 30px;
    }
    
    QComboBox::down-arrow {
        image: url(down_arrow.png);
        width: 12px;
        height: 12px;
    }
    
    /* التسميات */
    QLabel {
        color: #2c3e50;
        font-size: 14px;
        font-weight: bold;
    }
    
    /* الجداول */
    QTableWidget {
        background: white;
        border: 2px solid #bdc3c7;
        border-radius: 10px;
        gridline-color: #ecf0f1;
        font-size: 13px;
    }
    
    QTableWidget::item {
        padding: 10px;
        border-bottom: 1px solid #ecf0f1;
    }
    
    QTableWidget::item:selected {
        background: #3498db;
        color: white;
    }
    
    QHeaderView::section {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #34495e, stop: 1 #2c3e50
        );
        color: white;
        font-weight: bold;
        padding: 15px;
        border: none;
        border-radius: 5px;
    }
    
    /* شريط التمرير */
    QScrollBar:vertical {
        background: #ecf0f1;
        width: 12px;
        border-radius: 6px;
    }
    
    QScrollBar::handle:vertical {
        background: #3498db;
        border-radius: 6px;
        min-height: 20px;
    }
    
    QScrollBar::handle:vertical:hover {
        background: #2980b9;
    }
    
    /* شريط الحالة */
    QStatusBar {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 1, y2: 0,
            stop: 0 #34495e, stop: 1 #2c3e50
        );
        color: white;
        font-weight: bold;
        padding: 8px;
        border-radius: 5px;
    }
    
    /* قائمة الطعام */
    QMenuBar {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 1, y2: 0,
            stop: 0 #2c3e50, stop: 1 #34495e
        );
        color: white;
        font-weight: bold;
        padding: 5px;
    }
    
    QMenuBar::item {
        background: transparent;
        padding: 8px 15px;
        border-radius: 5px;
    }
    
    QMenuBar::item:selected {
        background: #3498db;
    }
    
    QMenu {
        background: white;
        border: 2px solid #bdc3c7;
        border-radius: 8px;
        padding: 5px;
    }
    
    QMenu::item {
        padding: 10px 20px;
        border-radius: 5px;
    }
    
    QMenu::item:selected {
        background: #3498db;
        color: white;
    }
    
    /* مربعات الاختيار */
    QCheckBox {
        color: #2c3e50;
        font-size: 14px;
        spacing: 8px;
    }
    
    QCheckBox::indicator {
        width: 18px;
        height: 18px;
        border: 2px solid #bdc3c7;
        border-radius: 4px;
        background: white;
    }
    
    QCheckBox::indicator:checked {
        background: #3498db;
        border: 2px solid #2980b9;
    }
    
    /* أزرار الراديو */
    QRadioButton {
        color: #2c3e50;
        font-size: 14px;
        spacing: 8px;
    }
    
    QRadioButton::indicator {
        width: 18px;
        height: 18px;
        border: 2px solid #bdc3c7;
        border-radius: 9px;
        background: white;
    }
    
    QRadioButton::indicator:checked {
        background: #3498db;
        border: 2px solid #2980b9;
    }
    
    /* التبويبات */
    QTabWidget::pane {
        border: 2px solid #bdc3c7;
        border-radius: 10px;
        background: white;
    }
    
    QTabBar::tab {
        background: #ecf0f1;
        border: 2px solid #bdc3c7;
        border-bottom: none;
        border-radius: 8px 8px 0 0;
        padding: 12px 20px;
        margin-right: 2px;
        font-weight: bold;
        color: #2c3e50;
    }
    
    QTabBar::tab:selected {
        background: #3498db;
        color: white;
    }
    
    QTabBar::tab:hover {
        background: #d5dbdb;
    }
    
    /* شريط التقدم */
    QProgressBar {
        border: 2px solid #bdc3c7;
        border-radius: 10px;
        background: #ecf0f1;
        text-align: center;
        font-weight: bold;
        color: #2c3e50;
    }
    
    QProgressBar::chunk {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #3498db, stop: 1 #2980b9
        );
        border-radius: 8px;
    }
    """

def get_login_stylesheet():
    """أنماط نافذة تسجيل الدخول"""
    return """
    QDialog {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 1, y2: 1,
            stop: 0 #667eea, stop: 1 #764ba2
        );
        border-radius: 20px;
    }
    
    QLabel#title_label {
        font-size: 28px;
        font-weight: bold;
        color: white;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 20px;
        margin: 10px;
    }
    
    QLabel#subtitle_label {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.8);
        background: transparent;
        padding: 5px;
    }
    
    QLineEdit {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 15px;
        padding: 18px;
        font-size: 18px;
        color: #2c3e50;
        min-height: 50px;
    }
    
    QLineEdit:focus {
        border: 2px solid #3498db;
        background: white;
    }
    
    QPushButton {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #2ecc71, stop: 1 #27ae60
        );
        border: none;
        border-radius: 15px;
        color: white;
        font-size: 18px;
        font-weight: bold;
        padding: 15px;
        margin: 10px;
    }
    
    QPushButton:hover {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #58d68d, stop: 1 #2ecc71
        );
    }
    
    QPushButton:pressed {
        background: qlineargradient(
            x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #27ae60, stop: 1 #229954
        );
    }
    """

def get_card_style():
    """أنماط البطاقات"""
    return """
    QFrame {
        background: white;
        border-radius: 15px;
        border: 1px solid #e0e0e0;
        padding: 15px;
        margin: 10px;
    }
    
    QFrame:hover {
        border: 1px solid #3498db;
        background: #f8f9fa;
    }
    """

def get_icon_button_style():
    """أنماط الأزرار مع الأيقونات"""
    return """
    QPushButton {
        text-align: left;
        padding-left: 20px;
        border: none;
        border-radius: 12px;
        background: white;
        color: #2c3e50;
        font-size: 14px;
        font-weight: bold;
        min-height: 50px;
        margin: 5px;
        border: 2px solid #e0e0e0;
    }
    
    QPushButton:hover {
        background: #f8f9fa;
        border: 2px solid #3498db;
        color: #3498db;
    }
    
    QPushButton:pressed {
        background: #e3f2fd;
        border: 2px solid #2980b9;
        color: #2980b9;
    }
    """
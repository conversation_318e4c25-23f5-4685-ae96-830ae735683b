#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح مقاسات النوافذ لتكون مناسبة للشاشات المختلفة
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

def get_screen_info():
    """الحصول على معلومات الشاشة"""
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    screen = app.primaryScreen()
    screen_geometry = screen.geometry()
    
    return {
        'width': screen_geometry.width(),
        'height': screen_geometry.height(),
        'available_width': screen.availableGeometry().width(),
        'available_height': screen.availableGeometry().height()
    }

def calculate_optimal_sizes():
    """حساب المقاسات المثلى للنوافذ"""
    screen_info = get_screen_info()
    
    # مقاسات محسوبة بناءً على حجم الشاشة
    window_width = min(900, int(screen_info['available_width'] * 0.7))
    window_height = min(600, int(screen_info['available_height'] * 0.8))
    
    dialog_width = min(500, int(screen_info['available_width'] * 0.4))
    dialog_height = min(400, int(screen_info['available_height'] * 0.5))
    
    return {
        'main_window': {
            'width': window_width,
            'height': window_height,
            'min_width': min(800, window_width - 100),
            'min_height': min(500, window_height - 100)
        },
        'login_window': {
            'width': dialog_width,
            'height': dialog_height
        },
        'request_window': {
            'width': min(600, int(screen_info['available_width'] * 0.5)),
            'height': min(500, int(screen_info['available_height'] * 0.6))
        }
    }

class OptimizedLoginWindow(QDialog):
    """نافذة تسجيل دخول محسّنة المقاسات"""
    
    def __init__(self):
        super().__init__()
        self.user_data = None
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة تسجيل الدخول"""
        sizes = calculate_optimal_sizes()
        login_size = sizes['login_window']
        
        self.setWindowTitle("🔐 تسجيل الدخول")
        self.setFixedSize(login_size['width'], login_size['height'])
        
        # وضع النافذة في الوسط
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - login_size['width']) // 2
        y = (screen.height() - login_size['height']) // 2
        self.move(x, y)
        
        # الخط
        self.setFont(QFont("Sakkal Majalla", 10))
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # العنوان
        title_label = QLabel("🔐 تسجيل الدخول")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 20px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #3498db, stop: 1 #2980b9);
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # نموذج تسجيل الدخول
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(10)
        
        # اسم المستخدم
        username_label = QLabel("👤 اسم المستخدم:")
        username_label.setStyleSheet("""
            font-family: "Sakkal Majalla", "Arial", sans-serif;
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
        """)
        form_layout.addWidget(username_label)
        
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم...")
        self.username_edit.setText("admin")
        self.username_edit.setStyleSheet("""
            QLineEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                background: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                color: #000000;
                min-height: 15px;
                max-height: 25px;
            }
            QLineEdit:focus {
                border: 2px solid #007bff;
                background: white;
            }
        """)
        form_layout.addWidget(self.username_edit)
        
        # كلمة المرور
        password_label = QLabel("🔐 كلمة المرور:")
        password_label.setStyleSheet("""
            font-family: "Sakkal Majalla", "Arial", sans-serif;
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
        """)
        form_layout.addWidget(password_label)
        
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("أدخل كلمة المرور...")
        self.password_edit.setText("admin123")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setStyleSheet("""
            QLineEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                background: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                color: #000000;
                min-height: 15px;
                max-height: 25px;
            }
            QLineEdit:focus {
                border: 2px solid #007bff;
                background: white;
            }
        """)
        self.password_edit.returnPressed.connect(self.handle_login)
        form_layout.addWidget(self.password_edit)
        
        main_layout.addWidget(form_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        login_button = QPushButton("🚀 دخول")
        login_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #28a745, stop: 1 #20c997);
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px 20px;
                min-width: 80px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #34ce57, stop: 1 #28a745);
            }
        """)
        login_button.clicked.connect(self.handle_login)
        buttons_layout.addWidget(login_button)
        
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #dc3545, stop: 1 #c82333);
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px 20px;
                min-width: 80px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e0506e, stop: 1 #dc3545);
            }
        """)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)
        
        main_layout.addLayout(buttons_layout)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #e3f2fd, stop: 1 #bbdefb);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
        
    def handle_login(self):
        """التعامل مع تسجيل الدخول"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        
        if username == "admin" and password == "admin123":
            self.user_data = {
                'username': username,
                'full_name': 'مدير النظام',
                'role': 'admin'
            }
            QMessageBox.information(self, "✅ نجح", "تم تسجيل الدخول بنجاح!")
            self.accept()
        else:
            QMessageBox.warning(self, "❌ خطأ", 
                              "بيانات تسجيل الدخول غير صحيحة!\n\nالمستخدم: admin\nكلمة المرور: admin123")

class OptimizedMainWindow(QMainWindow):
    """النافذة الرئيسية محسّنة المقاسات"""
    
    def __init__(self, user_data=None):
        super().__init__()
        self.user_data = user_data
        self.init_ui()
        
    def init_ui(self):
        """إعداد الواجهة الرئيسية"""
        sizes = calculate_optimal_sizes()
        main_size = sizes['main_window']
        
        self.setWindowTitle("🏢 نظام إدارة الإجازات")
        self.setGeometry(50, 50, main_size['width'], main_size['height'])
        self.setMinimumSize(main_size['min_width'], main_size['min_height'])
        
        # وضع النافذة في الوسط
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - main_size['width']) // 2
        y = (screen.height() - main_size['height']) // 2
        self.move(x, y)
        
        # الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # العنوان
        if self.user_data:
            welcome_text = f"مرحباً {self.user_data.get('full_name', 'المستخدم')}"
        else:
            welcome_text = "مرحباً بك في النظام"
            
        title_label = QLabel(f"🏢 {welcome_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2c3e50, stop: 1 #34495e);
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        layout.addWidget(title_label)
        
        # منطقة الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        buttons_layout = QGridLayout(buttons_frame)
        buttons_layout.setSpacing(10)
        
        # أزرار الوظائف
        buttons = [
            ("📝 طلب إجازة يومية", 0, 0),
            ("⏱️ طلب إجازة ساعية", 0, 1),
            ("🔍 البحث والتعديل", 1, 0),
            ("📊 التقارير", 1, 1),
            ("👥 إدارة المستخدمين", 2, 0),
            ("⚙️ الإعدادات", 2, 1)
        ]
        
        for text, row, col in buttons:
            button = QPushButton(text)
            button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #3498db, stop: 1 #2980b9);
                    border: none;
                    border-radius: 8px;
                    color: white;
                    font-weight: bold;
                    font-size: 14px;
                    padding: 15px;
                    min-height: 20px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #5dade2, stop: 1 #3498db);
                }
            """)
            button.clicked.connect(lambda checked, t=text: self.handle_button_click(t))
            buttons_layout.addWidget(button, row, col)
        
        layout.addWidget(buttons_frame)
        
        # شريط الحالة
        self.statusBar().showMessage("النظام جاهز للاستخدام")
        
        # الأنماط العامة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
        
    def handle_button_click(self, button_text):
        """التعامل مع النقر على الأزرار"""
        QMessageBox.information(self, "معلومات", f"تم النقر على: {button_text}")

def main():
    """تشغيل النظام بمقاسات محسّنة"""
    app = QApplication(sys.argv)
    
    # معلومات الشاشة
    screen_info = get_screen_info()
    print(f"📺 معلومات الشاشة:")
    print(f"   العرض: {screen_info['width']}px")
    print(f"   الارتفاع: {screen_info['height']}px")
    print(f"   المساحة المتاحة: {screen_info['available_width']}x{screen_info['available_height']}")
    
    # المقاسات المحسوبة
    sizes = calculate_optimal_sizes()
    print(f"\n📏 المقاسات المحسوبة:")
    print(f"   النافذة الرئيسية: {sizes['main_window']['width']}x{sizes['main_window']['height']}")
    print(f"   نافذة تسجيل الدخول: {sizes['login_window']['width']}x{sizes['login_window']['height']}")
    
    # تطبيق الخط العربي
    font = QFont("Sakkal Majalla", 10)
    app.setFont(font)
    
    # نافذة تسجيل الدخول
    login_window = OptimizedLoginWindow()
    
    if login_window.exec_() == QDialog.Accepted:
        print("✅ تم تسجيل الدخول بنجاح!")
        
        # النافذة الرئيسية
        main_window = OptimizedMainWindow(login_window.user_data)
        main_window.show()
        
        print("🎉 النظام جاهز بمقاسات محسّنة!")
        return app.exec_()
    else:
        print("❌ تم إلغاء تسجيل الدخول")
        return 0

if __name__ == "__main__":
    sys.exit(main())
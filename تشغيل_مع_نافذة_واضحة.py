#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل النظام مع نافذة تسجيل دخول واضحة
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# استيراد النافذة البسيطة
from نافذة_دخول_بسيطة import SimpleLoginWindow

# استيراد النظام الرئيسي
from modern_main_window import ModernMainWindow

def main():
    """تشغيل النظام مع نافذة دخول واضحة"""
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    font = QFont("Sakkal Majalla", 12)
    app.setFont(font)
    
    print("🚀 بدء تشغيل نظام إدارة الإجازات...")
    print("🔐 فتح نافذة تسجيل الدخول الواضحة...")
    
    # عرض نافذة تسجيل الدخول البسيطة
    login_window = SimpleLoginWindow()
    
    if login_window.exec_() == QDialog.Accepted:
        print("✅ تم تسجيل الدخول بنجاح!")
        print("🏢 فتح النظام الرئيسي...")
        
        # إنشاء النظام الرئيسي
        main_system = ModernMainWindow()
        main_system.user_data = login_window.user_data
        main_system.show()
        
        print("🎉 النظام جاهز للاستخدام!")
        print("=" * 50)
        print("📋 الوظائف المتاحة:")
        print("• طلب إجازة يومية")
        print("• طلب إجازة ساعية") 
        print("• البحث والتعديل")
        print("• التقارير والإحصائيات")
        print("• إدارة المستخدمين")
        print("• الإعدادات")
        
        return app.exec_()
    else:
        print("❌ تم إلغاء تسجيل الدخول")
        return 0

if __name__ == "__main__":
    sys.exit(main())
@echo off
chcp 65001 > nul
title نظام إدارة الإجازات المتقدم - الإصدار 2.0

echo.
echo 🎯 نظام إدارة الإجازات المتقدم - الإصدار 2.0
echo ================================================
echo.
echo ✨ الميزات الجديدة:
echo    📊 نظام التقارير المتقدم
echo    🔔 الإشعارات الذكية  
echo    📈 لوحة التحليلات
echo    💾 النسخ الاحتياطي التلقائي
echo    👥 إدارة المستخدمين والصلاحيات
echo.
echo ================================================
echo.

REM التحقق من وجود Python
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo 💡 يرجى تثبيت Python 3.7 أو أحدث من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo.

REM التحقق من وجود الملفات الأساسية
if not exist "master_control_panel.py" (
    echo ❌ ملف لوحة التحكم الرئيسية غير موجود
    echo 💡 تأكد من وجود جميع ملفات النظام
    pause
    exit /b 1
)

echo ✅ ملفات النظام موجودة
echo.

REM تشغيل لوحة التحكم الرئيسية
echo 🚀 تشغيل لوحة التحكم الرئيسية...
echo.
python master_control_panel.py

echo.
echo 👋 شكراً لاستخدام نظام إدارة الإجازات المتقدم!
pause

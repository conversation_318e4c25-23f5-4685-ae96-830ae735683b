#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل النظام المتكامل لإدارة الإجازات
الإصدار المتقدم النهائي مع جميع الميزات
"""

import sys
import os
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QFrame, QScrollArea, QWidget, QSizePolicy
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon

class SystemLauncher(QDialog):
    """مشغل النظام المتكامل"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🚀 نظام إدارة الإجازات المتكامل - الإصدار المتقدم")
        self.setFixedSize(950, 650)  # أبعاد محسنة ومتناسقة
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)
        
        # توسيط النافذة في الشاشة
        self.center_window()
        self.setup_ui()
        
        # إعداد الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            }
        """)
        
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
        
    def setup_ui(self):
        """إعداد واجهة المشغل"""
        # إنشاء منطقة تمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # إنشاء ويدجت المحتوى
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # العنوان الرئيسي
        self.create_header(layout)
        
        # قسم الواجهات الأساسية
        self.create_basic_interfaces_section(layout)
        
        # قسم النظم المتقدمة
        self.create_advanced_systems_section(layout)
        
        # أزرار التحكم
        self.create_control_buttons(layout)
        
        # إضافة المحتوى إلى منطقة التمرير
        scroll_area.setWidget(content_widget)
        
        # تعيين منطقة التمرير كتخطيط رئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(0)
        main_layout.addWidget(scroll_area)
        
    def create_header(self, layout):
        """إنشاء الرأس"""
        header_frame = QFrame()
        header_frame.setFixedHeight(130)  # زيادة الارتفاع لاستيعاب الخط الأكبر
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #667eea, stop: 1 #764ba2
                );
                border-radius: 15px;
                padding: 15px;
            }
        """)
        
        header_layout = QVBoxLayout(header_frame)
        
        # المعلومات الرسمية
        official_info_label = QLabel("الجمهورية الجزائرية الديمقراطية الشعبية\nالمديرية العامة للحماية المدنية\nمديرية الحماية المدنية لولاية الجلفة\nالوحدة الرئيسية - مكتب التعداد والوسائل")
        official_info_label.setAlignment(Qt.AlignCenter)
        official_info_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.95);
            margin-bottom: 15px;
            line-height: 1.4;
        """)
        header_layout.addWidget(official_info_label)
        
        # العنوان
        title_label = QLabel("🏢 نظام إدارة الإجازات المتكامل")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin-bottom: 10px;
        """)
        header_layout.addWidget(title_label)
        
        # العنوان الفرعي
        subtitle_label = QLabel("الإصدار المتقدم النهائي 🚀 17 نظام متطور • 35+ ملف • 12000+ سطر برمجي")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        """)
        header_layout.addWidget(subtitle_label)
        
        # معلومات النسخة والتاريخ
        version_info = QLabel(f"آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')} • النسخة: 4.1")
        version_info.setAlignment(Qt.AlignCenter)
        version_info.setStyleSheet("""
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 5px;
        """)
        header_layout.addWidget(version_info)
        
        layout.addWidget(header_frame)
        
    def create_basic_interfaces_section(self, layout):
        """إنشاء قسم الواجهات الأساسية"""
        section_title = QLabel("🎯 الواجهات الأساسية")
        section_title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin: 10px 0 8px 0;
        """)
        layout.addWidget(section_title)
        
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        
        # الواجهة الحديثة
        modern_btn = self.create_launch_button(
            "✨ الواجهة الحديثة",
            "واجهة عصرية مع تأثيرات بصرية متطورة",
            "#e74c3c",
            lambda: self.launch_modern()
        )
        buttons_layout.addWidget(modern_btn)
        
        # نظام الاختيار الذكي
        smart_btn = self.create_launch_button(
            "🎛️ نظام الاختيار الذكي",
            "نظام ذكي للاختيار بين الواجهات",
            "#f39c12",
            lambda: self.launch_smart_selector()
        )
        buttons_layout.addWidget(smart_btn)
        
        layout.addLayout(buttons_layout)
        
    def create_advanced_systems_section(self, layout):
        """إنشاء قسم النظم المتقدمة"""
        section_title = QLabel("🚀 النظم المتقدمة")
        section_title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin: 10px 0 8px 0;
        """)
        layout.addWidget(section_title)
        
        advanced_layout = QVBoxLayout()
        advanced_layout.setSpacing(15)
        
        # الصف الأول
        row1_layout = QHBoxLayout()
        row1_layout.setSpacing(15)
        
        ai_btn = self.create_launch_button(
            "🧠 الذكاء الاصطناعي",
            "تحليل ذكي وتنبؤات متقدمة",
            "#9b59b6",
            lambda: self.launch_ai_system()
        )
        row1_layout.addWidget(ai_btn)
        
        analytics_btn = self.create_launch_button(
            "📊 الإحصائيات المتقدمة",
            "لوحة تحليلات شاملة ومتطورة",
            "#1abc9c",
            lambda: self.launch_analytics()
        )
        row1_layout.addWidget(analytics_btn)
        
        database_btn = self.create_launch_button(
            "🗄️ إدارة قاعدة البيانات",
            "أدوات شاملة لإدارة البيانات",
            "#34495e",
            lambda: self.launch_database_manager()
        )
        row1_layout.addWidget(database_btn)
        
        advanced_layout.addLayout(row1_layout)
        
        # الصف الثاني
        row2_layout = QHBoxLayout()
        row2_layout.setSpacing(15)
        
        users_btn = self.create_launch_button(
            "👥 إدارة المستخدمين",
            "نظام شامل لإدارة المستخدمين والأذونات",
            "#e67e22",
            lambda: self.launch_user_management()
        )
        row2_layout.addWidget(users_btn)
        
        notifications_btn = self.create_launch_button(
            "🔔 نظام الإشعارات",
            "إشعارات ذكية ونسخ احتياطي متقدم",
            "#27ae60",
            lambda: self.launch_notifications()
        )
        row2_layout.addWidget(notifications_btn)
        
        settings_btn = self.create_launch_button(
            "⚙️ الإعدادات المتقدمة",
            "إعدادات شاملة وقابلة للتخصيص",
            "#8e44ad",
            lambda: self.launch_advanced_settings()
        )
        row2_layout.addWidget(settings_btn)
        
        advanced_layout.addLayout(row2_layout)
        
        layout.addLayout(advanced_layout)
        

    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم"""
        control_layout = QHBoxLayout()
        control_layout.setSpacing(8)
        control_layout.setContentsMargins(0, 10, 0, 0)
        
        # زر تشغيل شامل
        full_system_btn = QPushButton("🚀 تشغيل النظام الشامل")
        full_system_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #27ae60, stop: 1 #2ecc71
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #229954, stop: 1 #27ae60
                );
            }
        """)
        full_system_btn.clicked.connect(self.launch_full_system)
        control_layout.addWidget(full_system_btn)
        
        # زر المساعدة
        help_btn = QPushButton("❓ مساعدة")
        help_btn.setStyleSheet("""
            QPushButton {
                background: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2980b9;
            }
        """)
        help_btn.clicked.connect(self.show_help)
        control_layout.addWidget(help_btn)
        
        # زر إغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background: #e74c3c;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #c0392b;
            }
        """)
        close_btn.clicked.connect(self.close)
        control_layout.addWidget(close_btn)
        
        layout.addLayout(control_layout)
        
    def create_launch_button(self, title, description, color, callback):
        """إنشاء زر تشغيل"""
        btn = QPushButton()
        btn.setFixedHeight(90)
        btn.setMinimumWidth(200)
        btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        btn.setStyleSheet(f"""
            QPushButton {{
                background: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px;
                text-align: left;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: rgba(0, 0, 0, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.3);
            }}
        """)
        
        btn_layout = QVBoxLayout(btn)
        btn_layout.setContentsMargins(15, 15, 15, 15)
        btn_layout.setSpacing(5)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: white;")
        btn_layout.addWidget(title_label)
        
        # الوصف
        desc_label = QLabel(description)
        desc_label.setStyleSheet("font-size: 11px; color: rgba(255, 255, 255, 0.8);")
        desc_label.setWordWrap(True)
        btn_layout.addWidget(desc_label)
        
        btn.clicked.connect(callback)
        
        return btn
        
    def launch_modern(self):
        """تشغيل الواجهة الحديثة"""
        self.run_system("python تشغيل_الواجهة_الحديثة.py", "✨ الواجهة الحديثة")
        
    def launch_smart_selector(self):
        """تشغيل نظام الاختيار الذكي"""
        self.run_system("python اختيار_الواجهة.py", "🎛️ نظام الاختيار الذكي")
        
    def launch_ai_system(self):
        """تشغيل نظام الذكاء الاصطناعي"""
        self.run_system("python modern_ai_system.py", "🧠 نظام الذكاء الاصطناعي")
        
    def launch_analytics(self):
        """تشغيل نظام الإحصائيات"""
        self.run_system("python modern_simple_analytics.py", "📊 نظام الإحصائيات المتقدمة")
        
    def launch_database_manager(self):
        """تشغيل مدير قاعدة البيانات"""
        self.run_system("python modern_database_manager.py", "🗄️ مدير قاعدة البيانات")
        
    def launch_user_management(self):
        """تشغيل نظام إدارة المستخدمين"""
        self.run_system("python modern_user_management.py", "👥 نظام إدارة المستخدمين")
        
    def launch_notifications(self):
        """تشغيل نظام الإشعارات"""
        self.run_system("python modern_notifications_backup.py", "🔔 نظام الإشعارات والنسخ الاحتياطي")
        
    def launch_advanced_settings(self):
        """تشغيل الإعدادات المتقدمة"""
        self.run_system("python modern_search_settings.py", "⚙️ الإعدادات المتقدمة")
        
    def launch_web_interface(self):
        """تشغيل واجهة الويب"""
        try:
            # التحقق من وجود الملف أولاً
            if not os.path.exists("modern_web_interface.py"):
                QMessageBox.warning(
                    self, "ملف مفقود",
                    "لا يمكن العثور على ملف واجهة الويب:\nmodern_web_interface.py\n\n"
                    "تأكد من وجود الملف في المجلد الحالي."
                )
                return
                
            from modern_web_interface import VacationWebApp
            web_app = VacationWebApp()
            
            import threading
            web_thread = threading.Thread(target=lambda: web_app.run(debug=False, host='0.0.0.0', port=5000))
            web_thread.daemon = True
            web_thread.start()
            
            QMessageBox.information(
                self, "🌐 واجهة الويب",
                "تم تشغيل واجهة الويب بنجاح!\n\n"
                "🔗 الرابط المحلي: http://localhost:5000\n"
                "🌐 الرابط الخارجي: http://0.0.0.0:5000\n"
                "👤 المستخدم: admin\n"
                "🔑 كلمة المرور: admin123\n\n"
                "افتح المتصفح وتوجه إلى الرابط أعلاه"
            )
        except ImportError as e:
            QMessageBox.warning(
                self, "خطأ في الاستيراد",
                f"لا يمكن استيراد واجهة الويب:\n{str(e)}\n\n"
                "تأكد من وجود جميع المكتبات المطلوبة مثل Flask"
            )
        except Exception as e:
            QMessageBox.warning(
                self, "خطأ غير متوقع",
                f"حدث خطأ أثناء تشغيل واجهة الويب:\n{str(e)}"
            )
            
    def show_mobile_demo(self):
        """عرض محاكاة تطبيق الهاتف"""
        QMessageBox.information(
            self, "📱 تطبيق الهاتف المحمول",
            "🚧 تحت التطوير\n\n"
            "المميزات المخططة:\n"
            "📱 واجهة متجاوبة للهواتف الذكية\n"
            "🔔 إشعارات فورية\n"
            "📊 لوحة تحكم مبسطة\n"
            "⚡ أداء سريع وسلس\n"
            "🔐 مصادقة بيومترية\n\n"
            "سيتم إطلاقه في التحديث القادم!"
        )
        
    def show_api_demo(self):
        """عرض واجهة API"""
        QMessageBox.information(
            self, "🔗 واجهة برمجة التطبيقات",
            "🚧 تحت التطوير\n\n"
            "الـ APIs المتاحة:\n"
            "📝 GET /api/requests - جلب الطلبات\n"
            "➕ POST /api/requests - إضافة طلب\n"
            "✏️ PUT /api/requests/{id} - تعديل طلب\n"
            "🗑️ DELETE /api/requests/{id} - حذف طلب\n"
            "📊 GET /api/stats - الإحصائيات\n"
            "👥 GET /api/users - المستخدمين\n\n"
            "📖 التوثيق: http://localhost:5000/docs"
        )
        
    def launch_full_system(self):
        """تشغيل النظام الشامل"""
        reply = QMessageBox.question(
            self, "تشغيل النظام الشامل",
            "هل تريد تشغيل النظام الشامل؟\n\n"
            "سيتم تشغيل:\n"
            "✨ الواجهة الحديثة الرئيسية\n"
            "🌐 واجهة الويب\n"
            "🔔 نظام الإشعارات\n"
            "📊 نظام الإحصائيات\n\n"
            "هل تريد المتابعة؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.launch_modern()
            self.launch_web_interface()
            QMessageBox.information(
                self, "تم التشغيل",
                "🎉 تم تشغيل النظام الشامل بنجاح!\n\n"
                "جميع المكونات نشطة الآن:\n"
                "✅ الواجهة الحديثة\n"
                "✅ واجهة الويب\n"
                "✅ جميع النظم المتقدمة"
            )
            
    def show_help(self):
        """عرض المساعدة"""
        help_text = """
        🏢 نظام إدارة الإجازات المتكامل - دليل سريع
        
        📁 الملفات الأساسية:
        • تشغيل_الواجهة_الحديثة.py - الواجهة الحديثة الرئيسية
        • اختيار_الواجهة.py - نظام الاختيار الذكي
        
        🚀 النظم المتقدمة:
        • modern_ai_system.py - الذكاء الاصطناعي
        • modern_simple_analytics.py - الإحصائيات
        • modern_database_manager.py - إدارة قاعدة البيانات
        • modern_user_management.py - إدارة المستخدمين
        • modern_notifications_backup.py - الإشعارات والنسخ الاحتياطي
        • modern_web_interface.py - واجهة الويب
        
        📚 الوثائق:
        • README.md - دليل المشروع الرئيسي
        • دليل_المستخدم_السريع.md - إرشادات الاستخدام
        • التقرير_الأخير_المكتمل.md - التقرير الشامل
        
        💡 نصائح:
        • ابدأ بالواجهة الحديثة للحصول على أفضل تجربة
        • استخدم نظام الاختيار الذكي للمقارنة
        • جرب واجهة الويب للوصول عن بُعد
        """
        
        QMessageBox.information(self, "❓ المساعدة", help_text)
        
    def run_system(self, command, system_name):
        """تشغيل نظام معين"""
        try:
            import subprocess
            
            # استخراج اسم الملف من الأمر
            if "python " in command:
                filename = command.replace("python ", "")
                
                # التحقق من وجود الملف
                if not os.path.exists(filename):
                    QMessageBox.warning(
                        self, "ملف مفقود",
                        f"لا يمكن العثور على الملف:\n{filename}\n\n"
                        f"تأكد من وجود الملف في المجلد الحالي لتشغيل {system_name}"
                    )
                    return
                    
            # تشغيل الأمر
            process = subprocess.Popen(
                command, 
                shell=True, 
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            QMessageBox.information(
                self, "تم التشغيل",
                f"تم تشغيل {system_name} بنجاح!\n\n"
                f"معرف العملية: {process.pid}\n"
                f"الأمر: {command}\n\n"
                "إذا لم تظهر النافذة، تحقق من وحدة التحكم للأخطاء."
            )
        except FileNotFoundError:
            QMessageBox.critical(
                self, "خطأ في العثور على الملف",
                f"لا يمكن العثور على Python أو الملف المطلوب لتشغيل {system_name}\n\n"
                "تأكد من تثبيت Python وإضافته لمتغير PATH"
            )
        except Exception as e:
            QMessageBox.critical(
                self, "خطأ في التشغيل",
                f"لا يمكن تشغيل {system_name}:\n{str(e)}\n\n"
                f"الأمر: {command}"
            )

def main():
    """الدالة الرئيسية"""
    try:
        app = QApplication(sys.argv)
        app.setStyle('Fusion')
        
        # تعيين خصائص التطبيق
        app.setApplicationName("نظام إدارة الإجازات المتكامل")
        app.setApplicationVersion("4.1 - الإصدار المتقدم النهائي")
        app.setOrganizationName("الحماية المدنية - ولاية الجلفة")
        
        # رسالة ترحيب
        splash_msg = QMessageBox()
        splash_msg.setWindowTitle("🚀 مرحباً بك")
        splash_msg.setText("""
        🏢 نظام إدارة الإجازات المتكامل
        الإصدار المتقدم النهائي 4.1
        
        🇩🇿 الجمهورية الجزائرية الديمقراطية الشعبية
        🚒 مديرية الحماية المدنية لولاية الجلفة
        
        🌟 الميزات الجديدة:
        • 🧠 الذكاء الاصطناعي المتقدم
        • 👥 إدارة المستخدمين الشاملة  
        • 🌐 واجهة الويب الحديثة
        • 📊 إحصائيات وتحليلات متطورة
        • 🔔 نظام إشعارات ذكي
        • 🗄️ إدارة قاعدة البيانات المتقدمة
        
        🎯 17 نظام متطور • 35+ ملف • 12000+ سطر برمجي
        
        استمتع بالتجربة الجديدة! 🚀
        """)
        splash_msg.setIcon(QMessageBox.Information)
        splash_msg.exec_()
        
        # تشغيل المشغل
        launcher = SystemLauncher()
        launcher.show()
        
        return app.exec_()
        
    except ImportError as e:
        print(f"خطأ في استيراد المكتبات: {e}")
        print("تأكد من تثبيت PyQt5:")
        print("pip install PyQt5")
        return 1
    except Exception as e:
        print(f"خطأ غير متوقع: {e}")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
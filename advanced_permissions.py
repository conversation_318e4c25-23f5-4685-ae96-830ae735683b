#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الأذونات والصلاحيات المتقدم
Advanced Permissions and Authorization System
"""

import sqlite3
import hashlib
import json
from datetime import datetime, timedelta

class AdvancedPermissions:
    def __init__(self, db_path='vacation_system.db'):
        self.db_path = db_path
        self.init_permissions_system()
    
    def init_permissions_system(self):
        """إنشاء نظام الصلاحيات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT,
                email TEXT,
                role_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                last_login TEXT,
                FOREIGN KEY (role_id) REFERENCES roles (id)
            )
        ''')
        
        # جدول الأدوار
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                permissions TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول جلسات المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY,
                user_id INTEGER,
                session_token TEXT UNIQUE,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                expires_at TEXT,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # جدول سجل النشاط
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY,
                user_id INTEGER,
                action TEXT,
                details TEXT,
                ip_address TEXT,
                timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # إنشاء الأدوار الافتراضية
        self.create_default_roles(cursor)
        
        # إنشاء المستخدم الافتراضي
        self.create_default_admin(cursor)
        
        conn.commit()
        conn.close()
    
    def create_default_roles(self, cursor):
        """إنشاء الأدوار الافتراضية"""
        default_roles = [
            {
                'name': 'super_admin',
                'description': 'مدير النظام الرئيسي',
                'permissions': [
                    'user_management', 'role_management', 'system_settings',
                    'view_all_data', 'edit_all_data', 'delete_data',
                    'generate_reports', 'backup_restore', 'view_logs',
                    'approve_requests', 'manage_employees', 'financial_reports'
                ]
            },
            {
                'name': 'hr_manager',
                'description': 'مدير الموارد البشرية',
                'permissions': [
                    'view_all_data', 'edit_all_data', 'generate_reports',
                    'approve_requests', 'manage_employees', 'view_logs'
                ]
            },
            {
                'name': 'department_manager',
                'description': 'مدير القسم',
                'permissions': [
                    'view_department_data', 'approve_department_requests',
                    'generate_department_reports', 'manage_department_employees'
                ]
            },
            {
                'name': 'supervisor',
                'description': 'مشرف',
                'permissions': [
                    'view_team_data', 'approve_team_requests',
                    'generate_team_reports'
                ]
            },
            {
                'name': 'employee',
                'description': 'موظف',
                'permissions': [
                    'view_own_data', 'submit_requests', 'view_own_reports'
                ]
            },
            {
                'name': 'viewer',
                'description': 'مستعرض',
                'permissions': [
                    'view_public_data', 'view_public_reports'
                ]
            }
        ]
        
        for role in default_roles:
            cursor.execute('SELECT id FROM roles WHERE name = ?', (role['name'],))
            if not cursor.fetchone():
                cursor.execute('''
                    INSERT INTO roles (name, description, permissions)
                    VALUES (?, ?, ?)
                ''', (role['name'], role['description'], json.dumps(role['permissions'])))
    
    def create_default_admin(self, cursor):
        """إنشاء المستخدم الافتراضي"""
        cursor.execute('SELECT id FROM users WHERE username = ?', ('admin',))
        if not cursor.fetchone():
            # الحصول على معرف دور المدير
            cursor.execute('SELECT id FROM roles WHERE name = ?', ('super_admin',))
            admin_role_id = cursor.fetchone()[0]
            
            # إنشاء كلمة مرور مشفرة
            password_hash = self.hash_password('admin123')
            
            cursor.execute('''
                INSERT INTO users (username, password_hash, full_name, role_id)
                VALUES (?, ?, ?, ?)
            ''', ('admin', password_hash, 'مدير النظام', admin_role_id))
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate_user(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        password_hash = self.hash_password(password)
        
        cursor.execute('''
            SELECT u.id, u.username, u.full_name, r.name as role_name, r.permissions
            FROM users u
            JOIN roles r ON u.role_id = r.id
            WHERE u.username = ? AND u.password_hash = ? AND u.is_active = 1
        ''', (username, password_hash))
        
        user = cursor.fetchone()
        
        if user:
            user_id, username, full_name, role_name, permissions_json = user
            permissions = json.loads(permissions_json)
            
            # تحديث آخر تسجيل دخول
            cursor.execute('''
                UPDATE users SET last_login = ? WHERE id = ?
            ''', (datetime.now().isoformat(), user_id))
            
            # تسجيل النشاط
            self.log_activity(cursor, user_id, 'login', f'تسجيل دخول ناجح')
            
            conn.commit()
            conn.close()
            
            return {
                'id': user_id,
                'username': username,
                'full_name': full_name,
                'role': role_name,
                'permissions': permissions
            }
        
        conn.close()
        return None
    
    def create_user(self, username, password, full_name, email, role_name):
        """إنشاء مستخدم جديد"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # الحصول على معرف الدور
            cursor.execute('SELECT id FROM roles WHERE name = ?', (role_name,))
            role = cursor.fetchone()
            if not role:
                return False, "الدور غير موجود"
            
            role_id = role[0]
            password_hash = self.hash_password(password)
            
            cursor.execute('''
                INSERT INTO users (username, password_hash, full_name, email, role_id)
                VALUES (?, ?, ?, ?, ?)
            ''', (username, password_hash, full_name, email, role_id))
            
            conn.commit()
            return True, "تم إنشاء المستخدم بنجاح"
            
        except sqlite3.IntegrityError:
            return False, "اسم المستخدم موجود مسبقاً"
        finally:
            conn.close()
    
    def check_permission(self, user_permissions, required_permission):
        """فحص الصلاحية"""
        return required_permission in user_permissions
    
    def log_activity(self, cursor, user_id, action, details, ip_address='127.0.0.1'):
        """تسجيل النشاط"""
        cursor.execute('''
            INSERT INTO activity_log (user_id, action, details, ip_address)
            VALUES (?, ?, ?, ?)
        ''', (user_id, action, details, ip_address))
    
    def get_user_activity(self, user_id, limit=50):
        """الحصول على نشاط المستخدم"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT action, details, timestamp
            FROM activity_log
            WHERE user_id = ?
            ORDER BY timestamp DESC
            LIMIT ?
        ''', (user_id, limit))
        
        activities = cursor.fetchall()
        conn.close()
        
        return activities
    
    def get_all_users(self):
        """الحصول على جميع المستخدمين"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT u.id, u.username, u.full_name, u.email, r.name as role_name,
                   u.is_active, u.created_at, u.last_login
            FROM users u
            JOIN roles r ON u.role_id = r.id
            ORDER BY u.created_at DESC
        ''')
        
        users = cursor.fetchall()
        conn.close()
        
        return users
    
    def get_all_roles(self):
        """الحصول على جميع الأدوار"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id, name, description, permissions FROM roles')
        roles = cursor.fetchall()
        conn.close()
        
        return roles
    
    def update_user_status(self, user_id, is_active):
        """تحديث حالة المستخدم"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE users SET is_active = ? WHERE id = ?
        ''', (is_active, user_id))
        
        conn.commit()
        conn.close()
    
    def change_user_password(self, user_id, new_password):
        """تغيير كلمة مرور المستخدم"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        password_hash = self.hash_password(new_password)
        
        cursor.execute('''
            UPDATE users SET password_hash = ? WHERE id = ?
        ''', (password_hash, user_id))
        
        conn.commit()
        conn.close()
    
    def create_custom_role(self, name, description, permissions):
        """إنشاء دور مخصص"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO roles (name, description, permissions)
                VALUES (?, ?, ?)
            ''', (name, description, json.dumps(permissions)))
            
            conn.commit()
            return True, "تم إنشاء الدور بنجاح"
            
        except sqlite3.IntegrityError:
            return False, "اسم الدور موجود مسبقاً"
        finally:
            conn.close()

def demo_permissions_system():
    """عرض توضيحي لنظام الصلاحيات"""
    print("🔐 نظام إدارة الأذونات والصلاحيات المتقدم")
    print("=" * 60)
    
    # إنشاء النظام
    perm_system = AdvancedPermissions()
    
    print("✅ تم إنشاء نظام الصلاحيات")
    
    # اختبار تسجيل الدخول
    print("\n🔍 اختبار تسجيل الدخول:")
    user = perm_system.authenticate_user('admin', 'admin123')
    
    if user:
        print(f"✅ تم تسجيل الدخول بنجاح")
        print(f"👤 المستخدم: {user['full_name']}")
        print(f"🎭 الدور: {user['role']}")
        print(f"🔑 الصلاحيات: {len(user['permissions'])} صلاحية")
        
        # عرض بعض الصلاحيات
        print("\n📋 بعض الصلاحيات المتاحة:")
        for i, perm in enumerate(user['permissions'][:5], 1):
            print(f"  {i}. {perm}")
        if len(user['permissions']) > 5:
            print(f"  ... و {len(user['permissions']) - 5} صلاحيات أخرى")
    
    # عرض جميع الأدوار
    print("\n🎭 الأدوار المتاحة:")
    roles = perm_system.get_all_roles()
    for role in roles:
        role_id, name, description, permissions_json = role
        permissions = json.loads(permissions_json)
        print(f"  📌 {description} ({name}): {len(permissions)} صلاحية")
    
    # عرض المستخدمين
    print("\n👥 المستخدمين المسجلين:")
    users = perm_system.get_all_users()
    for user_data in users:
        user_id, username, full_name, email, role_name, is_active, created_at, last_login = user_data
        status = "🟢 نشط" if is_active else "🔴 معطل"
        print(f"  👤 {full_name} (@{username}) - {role_name} {status}")
    
    print("\n🎉 نظام الصلاحيات يعمل بشكل مثالي!")
    print("💡 يمكن الآن إدارة المستخدمين والأدوار بشكل متقدم")

if __name__ == "__main__":
    demo_permissions_system()

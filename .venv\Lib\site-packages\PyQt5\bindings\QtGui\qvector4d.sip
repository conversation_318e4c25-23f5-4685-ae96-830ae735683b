// qvector4d.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qvector4d.h>
%End

class QVector4D
{
%TypeHeaderCode
#include <qvector4d.h>
%End

%PickleCode
    sipRes = Py_BuildValue((char *)"dddd", (double)sipCpp->x(),
            (double)sipCpp->y(), (double)sipCpp->z(), (double)sipCpp->w());
%End

public:
    QVector4D();
    QVector4D(float xpos, float ypos, float zpos, float wpos);
    explicit QVector4D(const QPoint &point);
    explicit QVector4D(const QPointF &point);
    QVector4D(const QVector2D &vector);
    QVector4D(const QVector2D &vector, float zpos, float wpos);
    QVector4D(const QVector3D &vector);
    QVector4D(const QVector3D &vector, float wpos);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        PyObject *x = PyFloat_FromDouble(sipCpp->x());
        PyObject *y = PyFloat_FromDouble(sipCpp->y());
        PyObject *z = PyFloat_FromDouble(sipCpp->z());
        PyObject *w = PyFloat_FromDouble(sipCpp->w());
        
        if (x && y && z && w)
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromFormat("PyQt5.QtGui.QVector4D(%R, %R, %R, %R)", x,
                    y, z, w);
        #else
            sipRes = PyString_FromString("PyQt5.QtGui.QVector4D(");
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(x));
            PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(y));
            PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(z));
            PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(w));
            PyString_ConcatAndDel(&sipRes, PyString_FromString(")"));
        #endif
        }
        
        Py_XDECREF(x);
        Py_XDECREF(y);
        Py_XDECREF(z);
        Py_XDECREF(w);
%End

    float length() const;
    float lengthSquared() const;
    QVector4D normalized() const;
    void normalize();
    static float dotProduct(const QVector4D &v1, const QVector4D &v2);
    QVector2D toVector2D() const;
    QVector2D toVector2DAffine() const;
    QVector3D toVector3D() const;
    QVector3D toVector3DAffine() const;
    bool isNull() const;
    float x() const;
    float y() const;
    float z() const;
    float w() const;
    void setX(float aX);
    void setY(float aY);
    void setZ(float aZ);
    void setW(float aW);
    QVector4D &operator+=(const QVector4D &vector);
    QVector4D &operator-=(const QVector4D &vector);
    QVector4D &operator*=(float factor);
    QVector4D &operator*=(const QVector4D &vector);
    QVector4D &operator/=(float divisor);
%If (Qt_5_5_0 -)
    QVector4D &operator/=(const QVector4D &vector);
%End
    QPoint toPoint() const;
    QPointF toPointF() const;
%If (Qt_5_2_0 -)
    float operator[](int i) const;
%End
};

bool operator==(const QVector4D &v1, const QVector4D &v2);
bool operator!=(const QVector4D &v1, const QVector4D &v2);
const QVector4D operator+(const QVector4D &v1, const QVector4D &v2);
const QVector4D operator-(const QVector4D &v1, const QVector4D &v2);
const QVector4D operator*(float factor, const QVector4D &vector);
const QVector4D operator*(const QVector4D &vector, float factor);
const QVector4D operator*(const QVector4D &v1, const QVector4D &v2);
const QVector4D operator-(const QVector4D &vector);
const QVector4D operator/(const QVector4D &vector, float divisor);
%If (Qt_5_5_0 -)
const QVector4D operator/(const QVector4D &vector, const QVector4D &divisor);
%End
bool qFuzzyCompare(const QVector4D &v1, const QVector4D &v2);
QDataStream &operator<<(QDataStream &, const QVector4D & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QVector4D & /Constrained/) /ReleaseGIL/;

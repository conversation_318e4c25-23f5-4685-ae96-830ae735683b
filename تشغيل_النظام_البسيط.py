#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل النظام البسيط - قائمة منسدلة للأسماء + إدراج يدوي للأيام والساعات
"""

import sys
import os

def main():
    """تشغيل النظام"""
    print("🚀 تشغيل نظام إدارة الإجازات")
    print("=" * 40)
    print("✅ قائمة منسدلة للأسماء")
    print("✅ إدراج يدوي للأيام والساعات") 
    print("✅ مقاسات محسّنة ومناسبة")
    print("✅ توجيه عربي كامل")
    print("=" * 40)
    
    # التحقق من وجود الملف
    if not os.path.exists("نظام_قائمة_الأسماء_المحسّن.py"):
        print("❌ خطأ: ملف النظام غير موجود!")
        print("تأكد من وجود الملف: نظام_قائمة_الأسماء_المحسّن.py")
        input("اضغط Enter للخروج...")
        return 1
    
    try:
        print("🔄 تحميل النظام...")
        
        # تشغيل النظام
        import نظام_قائمة_الأسماء_المحسّن
        
        print("✅ تم تشغيل النظام بنجاح!")
        return 0
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد النظام: {e}")
        print("تأكد من تثبيت PyQt5:")
        print("pip install PyQt5")
        input("اضغط Enter للخروج...")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
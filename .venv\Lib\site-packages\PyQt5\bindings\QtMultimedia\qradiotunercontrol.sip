// qradiotunercontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QRadioTunerControl : public QMediaControl
{
%TypeHeaderCode
#include <qradiotunercontrol.h>
%End

public:
    virtual ~QRadioTunerControl();
    virtual QRadioTuner::State state() const = 0;
    virtual QRadioTuner::Band band() const = 0;
    virtual void setBand(QRadioTuner::Band b) = 0;
    virtual bool isBandSupported(QRadioTuner::Band b) const = 0;
    virtual int frequency() const = 0;
    virtual int frequencyStep(QRadioTuner::Band b) const = 0;
    virtual QPair<int, int> frequencyRange(QRadioTuner::Band b) const = 0;
    virtual void setFrequency(int frequency) = 0;
    virtual bool isStereo() const = 0;
    virtual QRadioTuner::StereoMode stereoMode() const = 0;
    virtual void setStereoMode(QRadioTuner::StereoMode mode) = 0;
    virtual int signalStrength() const = 0;
    virtual int volume() const = 0;
    virtual void setVolume(int volume) = 0;
    virtual bool isMuted() const = 0;
    virtual void setMuted(bool muted) = 0;
    virtual bool isSearching() const = 0;
    virtual bool isAntennaConnected() const;
    virtual void searchForward() = 0;
    virtual void searchBackward() = 0;
    virtual void searchAllStations(QRadioTuner::SearchMode searchMode = QRadioTuner::SearchFast) = 0;
    virtual void cancelSearch() = 0;
    virtual void start() = 0;
    virtual void stop() = 0;
    virtual QRadioTuner::Error error() const = 0;
    virtual QString errorString() const = 0;

signals:
    void stateChanged(QRadioTuner::State state);
    void bandChanged(QRadioTuner::Band band);
    void frequencyChanged(int frequency);
    void stereoStatusChanged(bool stereo);
    void searchingChanged(bool searching);
    void signalStrengthChanged(int signalStrength);
    void volumeChanged(int volume);
    void mutedChanged(bool muted);
    void error(QRadioTuner::Error err);
    void stationFound(int frequency, QString stationId);
    void antennaConnectedChanged(bool connectionStatus);

protected:
    explicit QRadioTunerControl(QObject *parent /TransferThis/ = 0);
};

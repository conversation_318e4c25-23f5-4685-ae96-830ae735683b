#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة نظام التقارير المتقدم
Advanced Reports Interface
"""

import sys
import os
from datetime import datetime, timedelta
from advanced_reports import AdvancedReportsManager
from database import VacationDatabase

class ReportsInterface:
    def __init__(self):
        self.reports_manager = AdvancedReportsManager()
        self.db = VacationDatabase()
    
    def display_menu(self):
        """عرض القائمة الرئيسية"""
        print("\n" + "="*60)
        print("🎯 نظام التقارير المتقدم لإدارة الإجازات")
        print("="*60)
        print("1. تقرير شهري")
        print("2. تقرير سنوي") 
        print("3. تقرير موظف مفصل")
        print("4. تقرير مخصص")
        print("5. نسخة احتياطية شاملة")
        print("6. عرض إحصائيات سريعة")
        print("7. البحث عن موظف")
        print("0. خروج")
        print("="*60)
    
    def get_user_input(self, prompt, input_type=str, required=True):
        """الحصول على مدخلات المستخدم مع التحقق"""
        while True:
            try:
                value = input(f"{prompt}: ").strip()
                
                if not value and required:
                    print("⚠️ هذا الحقل مطلوب")
                    continue
                
                if not value and not required:
                    return None
                
                if input_type == int:
                    return int(value)
                elif input_type == float:
                    return float(value)
                else:
                    return value
                    
            except ValueError:
                print(f"⚠️ يرجى إدخال {input_type.__name__} صحيح")
    
    def get_date_input(self, prompt, required=True):
        """الحصول على تاريخ من المستخدم"""
        while True:
            date_str = self.get_user_input(f"{prompt} (YYYY-MM-DD)", required=required)
            
            if not date_str and not required:
                return None
            
            try:
                datetime.strptime(date_str, '%Y-%m-%d')
                return date_str
            except ValueError:
                print("⚠️ تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD")
    
    def monthly_report(self):
        """إنشاء تقرير شهري"""
        print("\n📊 إنشاء تقرير شهري")
        print("-" * 30)
        
        year = self.get_user_input("السنة", int)
        month = self.get_user_input("الشهر (1-12)", int)
        
        if month < 1 or month > 12:
            print("⚠️ الشهر يجب أن يكون بين 1 و 12")
            return
        
        output_path = self.get_user_input("مسار الحفظ (اتركه فارغاً للمسار الافتراضي)", required=False)
        
        print("⏳ جاري إنشاء التقرير...")
        success, message = self.reports_manager.generate_monthly_excel_report(year, month, output_path)
        
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
    
    def yearly_report(self):
        """إنشاء تقرير سنوي"""
        print("\n📈 إنشاء تقرير سنوي")
        print("-" * 30)
        
        year = self.get_user_input("السنة", int)
        output_path = self.get_user_input("مسار الحفظ (اتركه فارغاً للمسار الافتراضي)", required=False)
        
        print("⏳ جاري إنشاء التقرير...")
        success, message = self.reports_manager.generate_yearly_excel_report(year, output_path)
        
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
    
    def employee_report(self):
        """إنشاء تقرير موظف مفصل"""
        print("\n👤 إنشاء تقرير موظف مفصل")
        print("-" * 30)
        
        # عرض قائمة الموظفين
        employees = self.db.get_all_employees()
        if not employees:
            print("⚠️ لا توجد بيانات موظفين")
            return
        
        print("الموظفين المتاحين:")
        for i, emp in enumerate(employees[:10], 1):
            print(f"  {i}. {emp}")
        
        if len(employees) > 10:
            print(f"  ... و {len(employees) - 10} موظف آخر")
        
        full_name = self.get_user_input("اسم الموظف")
        
        # التحقق من وجود الموظف
        if full_name not in employees:
            search_results = self.db.search_employee(full_name)
            if search_results:
                print("نتائج البحث:")
                for i, result in enumerate(search_results, 1):
                    print(f"  {i}. {result}")
                
                choice = self.get_user_input("اختر رقم الموظف", int) - 1
                if 0 <= choice < len(search_results):
                    full_name = search_results[choice]
                else:
                    print("⚠️ اختيار غير صحيح")
                    return
            else:
                print("⚠️ لم يتم العثور على الموظف")
                return
        
        start_date = self.get_date_input("تاريخ البداية (اختياري)", required=False)
        end_date = self.get_date_input("تاريخ النهاية (اختياري)", required=False)
        output_path = self.get_user_input("مسار الحفظ (اتركه فارغاً للمسار الافتراضي)", required=False)
        
        print("⏳ جاري إنشاء التقرير...")
        success, message = self.reports_manager.generate_employee_excel_report(
            full_name, start_date, end_date, output_path)
        
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
    
    def custom_report(self):
        """إنشاء تقرير مخصص"""
        print("\n🔧 إنشاء تقرير مخصص")
        print("-" * 30)
        
        filters = {}
        
        # فترة زمنية
        print("📅 تحديد الفترة الزمنية:")
        filters['start_date'] = self.get_date_input("تاريخ البداية (اختياري)", required=False)
        filters['end_date'] = self.get_date_input("تاريخ النهاية (اختياري)", required=False)
        
        # نوع البيانات
        print("\n📋 نوع البيانات المطلوبة:")
        include_daily = self.get_user_input("تضمين الإجازات اليومية؟ (y/n)", required=False)
        include_hourly = self.get_user_input("تضمين الإجازات الساعية؟ (y/n)", required=False)
        
        filters['include_daily'] = include_daily.lower() in ['y', 'yes', 'نعم', 'ن'] if include_daily else True
        filters['include_hourly'] = include_hourly.lower() in ['y', 'yes', 'نعم', 'ن'] if include_hourly else True
        
        # موظفين محددين
        specific_employees = self.get_user_input("موظفين محددين (مفصولين بفاصلة، اختياري)", required=False)
        if specific_employees:
            filters['employee_names'] = [name.strip() for name in specific_employees.split(',')]
        
        output_path = self.get_user_input("مسار الحفظ (اتركه فارغاً للمسار الافتراضي)", required=False)
        
        print("⏳ جاري إنشاء التقرير المخصص...")
        success, message = self.reports_manager.generate_custom_report(filters, output_path)
        
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
    
    def backup_database(self):
        """إنشاء نسخة احتياطية"""
        print("\n💾 إنشاء نسخة احتياطية شاملة")
        print("-" * 30)
        
        output_path = self.get_user_input("مسار الحفظ (اتركه فارغاً للمسار الافتراضي)", required=False)
        
        print("⏳ جاري إنشاء النسخة الاحتياطية...")
        success, message = self.reports_manager.export_database_backup(output_path)
        
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
    
    def quick_stats(self):
        """عرض إحصائيات سريعة"""
        print("\n📊 إحصائيات سريعة")
        print("-" * 30)
        
        # إحصائيات عامة
        employees = self.db.get_all_employees()
        daily_requests = self.db.get_requests_by_type('daily')
        hourly_requests = self.db.get_requests_by_type('hourly')
        added_vacations = self.db.get_requests_by_type('added')
        
        print(f"👥 عدد الموظفين: {len(employees)}")
        print(f"📅 الطلبات اليومية: {len(daily_requests)}")
        print(f"⏰ الطلبات الساعية: {len(hourly_requests)}")
        print(f"➕ الإجازات المدرجة: {len(added_vacations)}")
        
        # إحصائيات الشهر الحالي
        current_date = datetime.now()
        current_year = current_date.year
        current_month = current_date.month
        
        monthly_data = self.db.get_monthly_report(current_year, current_month)
        
        print(f"\n📊 إحصائيات الشهر الحالي ({current_year}-{current_month:02d}):")
        print(f"  📅 طلبات يومية: {monthly_data['daily_stats'][0]}")
        print(f"  📅 أيام يومية: {monthly_data['daily_stats'][1]}")
        print(f"  ⏰ طلبات ساعية: {monthly_data['hourly_stats'][0]}")
        print(f"  ⏰ ساعات: {monthly_data['hourly_stats'][1]}")
    
    def search_employee(self):
        """البحث عن موظف"""
        print("\n🔍 البحث عن موظف")
        print("-" * 30)
        
        search_term = self.get_user_input("كلمة البحث")
        results = self.db.search_employee(search_term)
        
        if results:
            print(f"تم العثور على {len(results)} نتيجة:")
            for i, emp in enumerate(results, 1):
                print(f"  {i}. {emp}")
                
                # عرض رصيد الموظف
                balance = self.db.get_employee_balance(emp)
                print(f"     💰 الرصيد الصافي: {balance['net_balance']:.2f} يوم")
        else:
            print("⚠️ لم يتم العثور على نتائج")
    
    def run(self):
        """تشغيل الواجهة"""
        print("🎯 مرحباً بك في نظام التقارير المتقدم!")
        
        while True:
            try:
                self.display_menu()
                choice = self.get_user_input("اختر العملية المطلوبة", int)
                
                if choice == 0:
                    print("👋 شكراً لاستخدام نظام التقارير!")
                    break
                elif choice == 1:
                    self.monthly_report()
                elif choice == 2:
                    self.yearly_report()
                elif choice == 3:
                    self.employee_report()
                elif choice == 4:
                    self.custom_report()
                elif choice == 5:
                    self.backup_database()
                elif choice == 6:
                    self.quick_stats()
                elif choice == 7:
                    self.search_employee()
                else:
                    print("⚠️ اختيار غير صحيح")
                
                input("\nاضغط Enter للمتابعة...")
                
            except KeyboardInterrupt:
                print("\n\n👋 تم إيقاف البرنامج")
                break
            except Exception as e:
                print(f"\n❌ خطأ غير متوقع: {e}")
                input("اضغط Enter للمتابعة...")

if __name__ == "__main__":
    interface = ReportsInterface()
    interface.run()

// qmediaplaylist.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMediaPlaylist : public QObject, public QMediaBindableInterface
{
%TypeHeaderCode
#include <qmediaplaylist.h>
%End

public:
    enum PlaybackMode
    {
        CurrentItemOnce,
        CurrentItemInLoop,
        Sequential,
        Loop,
        Random,
    };

    enum Error
    {
        NoError,
        FormatError,
        FormatNotSupportedError,
        NetworkError,
        AccessDeniedError,
    };

%If (Qt_5_6_1 -)
    explicit QMediaPlaylist(QObject *parent /TransferThis/ = 0);
%End
%If (- Qt_5_6_1)
    QMediaPlaylist(QObject *parent /TransferThis/ = 0);
%End
    virtual ~QMediaPlaylist();
    virtual QMediaObject *mediaObject() const;
    QMediaPlaylist::PlaybackMode playbackMode() const;
    void setPlaybackMode(QMediaPlaylist::PlaybackMode mode);
    int currentIndex() const;
    QMediaContent currentMedia() const;
    int nextIndex(int steps = 1) const;
    int previousIndex(int steps = 1) const;
    QMediaContent media(int index) const;
    int mediaCount() const;
    bool isEmpty() const;
    bool isReadOnly() const;
    bool addMedia(const QMediaContent &content);
    bool addMedia(const QList<QMediaContent> &items);
    bool insertMedia(int index, const QMediaContent &content);
    bool insertMedia(int index, const QList<QMediaContent> &items);
    bool removeMedia(int pos);
    bool removeMedia(int start, int end);
    bool clear();
    void load(const QNetworkRequest &request, const char *format = 0) /ReleaseGIL/;
    void load(const QUrl &location, const char *format = 0) /ReleaseGIL/;
    void load(QIODevice *device, const char *format = 0) /ReleaseGIL/;
    bool save(const QUrl &location, const char *format = 0) /ReleaseGIL/;
    bool save(QIODevice *device, const char *format) /ReleaseGIL/;
    QMediaPlaylist::Error error() const;
    QString errorString() const;
%If (Qt_5_7_0 -)
    bool moveMedia(int from, int to);
%End

public slots:
    void shuffle();
    void next();
    void previous();
    void setCurrentIndex(int index);

signals:
    void currentIndexChanged(int index);
    void playbackModeChanged(QMediaPlaylist::PlaybackMode mode);
    void currentMediaChanged(const QMediaContent &);
    void mediaAboutToBeInserted(int start, int end);
    void mediaInserted(int start, int end);
    void mediaAboutToBeRemoved(int start, int end);
    void mediaRemoved(int start, int end);
    void mediaChanged(int start, int end);
    void loaded();
    void loadFailed();

protected:
    virtual bool setMediaObject(QMediaObject *object);
};

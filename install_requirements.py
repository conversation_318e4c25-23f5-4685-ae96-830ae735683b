#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تثبيت المكتبات المطلوبة لنظام إدارة الإجازات
Install Required Libraries for Vacation Management System
"""

import subprocess
import sys
import os

def install_package(package):
    """تثبيت مكتبة واحدة"""
    try:
        print(f"🔄 تثبيت {package}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                              capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print(f"✅ تم تثبيت {package} بنجاح")
            return True
        else:
            print(f"❌ فشل تثبيت {package}")
            print(f"خطأ: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ انتهت مهلة تثبيت {package}")
        return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت {package}: {e}")
        return False

def check_package(package):
    """فحص وجود مكتبة"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    print("🎯 تثبيت المكتبات المطلوبة لنظام إدارة الإجازات")
    print("="*60)
    
    # قائمة المكتبات المطلوبة
    required_packages = [
        ("pandas", "pandas"),
        ("openpyxl", "openpyxl"), 
        ("PyQt5", "PyQt5"),
        ("reportlab", "reportlab")
    ]
    
    installed_count = 0
    total_count = len(required_packages)
    
    print("🔍 فحص المكتبات المثبتة:")
    print("-" * 40)
    
    for package_name, import_name in required_packages:
        if check_package(import_name):
            print(f"✅ {package_name} - مثبت")
            installed_count += 1
        else:
            print(f"❌ {package_name} - غير مثبت")
    
    print("-" * 40)
    print(f"📊 المثبت: {installed_count}/{total_count}")
    
    if installed_count == total_count:
        print("🎉 جميع المكتبات مثبتة!")
        print("✅ النظام جاهز للتشغيل")
        return True
    
    print()
    print("🔄 بدء تثبيت المكتبات المفقودة...")
    print("-" * 40)
    
    success_count = 0
    
    for package_name, import_name in required_packages:
        if not check_package(import_name):
            if install_package(package_name):
                success_count += 1
            print()
    
    print("="*60)
    print("📋 ملخص التثبيت:")
    print(f"✅ نجح: {success_count}")
    print(f"❌ فشل: {total_count - installed_count - success_count}")
    
    # فحص نهائي
    print()
    print("🔍 فحص نهائي:")
    final_installed = 0
    
    for package_name, import_name in required_packages:
        if check_package(import_name):
            print(f"✅ {package_name}")
            final_installed += 1
        else:
            print(f"❌ {package_name}")
    
    if final_installed == total_count:
        print()
        print("🎉 تم تثبيت جميع المكتبات بنجاح!")
        print("✅ النظام جاهز للتشغيل")
        print()
        print("🚀 يمكنك الآن تشغيل البرنامج:")
        print("  python master_control_panel.py")
        print("  python main.py")
        print("  python simple_demo.py")
        return True
    else:
        print()
        print("⚠️ لم يتم تثبيت جميع المكتبات")
        print("💡 جرب تثبيتها يدوياً:")
        
        for package_name, import_name in required_packages:
            if not check_package(import_name):
                print(f"  pip install {package_name}")
        
        return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التثبيت")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    
    input("\nاضغط Enter للخروج...")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
لوحة الإحصائيات المبسطة (بدون matplotlib)
"""

import sys
import os
import sqlite3
import json
from datetime import datetime, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from modern_dialogs import ModernDialog

class SimpleAnalyticsEngine:
    """محرك الإحصائيات المبسط"""
    
    def __init__(self, db_path='vacation_system.db'):
        self.db_path = db_path
        
    def get_summary_statistics(self):
        """الحصول على الإحصائيات الملخصة"""
        # بيانات وهمية للاختبار
        return {
            'total_employees': 156,
            'total_requests': 245,
            'approved_requests': 213,
            'pending_requests': 22,
            'rejected_requests': 10,
            'approval_rate': 87.0,
            'avg_response_time': 1.2,
            'total_vacation_days': 1245,
            'avg_vacation_days': 8.5,
            'active_employees': 134,
            'efficiency_rate': 92.0
        }
        
    def get_department_statistics(self):
        """الحصول على إحصائيات الأقسام"""
        return [
            {'name': 'التقنية', 'employees': 25, 'requests': 18, 'approved': 16, 'avg_balance': 22.5},
            {'name': 'المالية', 'employees': 15, 'requests': 12, 'approved': 11, 'avg_balance': 28.3},
            {'name': 'التسويق', 'employees': 20, 'requests': 15, 'approved': 13, 'avg_balance': 19.8},
            {'name': 'الموارد البشرية', 'employees': 8, 'requests': 6, 'approved': 6, 'avg_balance': 30.0},
            {'name': 'الإدارة', 'employees': 12, 'requests': 8, 'approved': 7, 'avg_balance': 25.5},
        ]
        
    def get_monthly_trends(self, months=6):
        """الحصول على الاتجاهات الشهرية"""
        import random
        current_date = datetime.now()
        trends = []
        
        for i in range(months):
            month_date = current_date - timedelta(days=30*i)
            month_name = month_date.strftime('%Y-%m')
            
            data = {
                'month': month_name,
                'requests': random.randint(20, 50),
                'approved': random.randint(15, 45),
                'efficiency': random.uniform(85, 95)
            }
            trends.append(data)
            
        return list(reversed(trends))
        
    def get_vacation_types_summary(self):
        """الحصول على ملخص أنواع الإجازات"""
        return {
            'يومية': {'count': 160, 'percentage': 65},
            'ساعية': {'count': 50, 'percentage': 20},
            'إضافية': {'count': 25, 'percentage': 10},
            'طوارئ': {'count': 10, 'percentage': 5}
        }

class ModernSimpleAnalyticsWindow(ModernDialog):
    """نافذة الإحصائيات المبسطة"""
    
    def __init__(self):
        super().__init__("📊 لوحة الإحصائيات والتحليلات", 1100, 750)
        self.analytics_engine = SimpleAnalyticsEngine()
        self.setup_analytics_content()
        
    def setup_analytics_content(self):
        """إعداد محتوى الإحصائيات"""
        layout = QVBoxLayout(self.content_area)
        layout.setSpacing(20)
        
        # شريط التحكم
        self.create_control_bar(layout)
        
        # بطاقات الإحصائيات الرئيسية
        self.create_main_stats(layout)
        
        # تبويبات التفاصيل
        self.create_details_tabs(layout)
        
        # إعداد الأزرار
        self.add_button("🔄 تحديث البيانات", self.refresh_data, "primary")
        self.add_button("📤 تصدير التقرير", self.export_report, "success")
        self.add_button("📧 إرسال التقرير", self.email_report, "secondary")
        self.add_button("❌ إغلاق", self.accept, "secondary")
        
    def create_control_bar(self, layout):
        """إنشاء شريط التحكم"""
        control_frame = QFrame()
        control_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #667eea, stop: 1 #764ba2
                );
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        control_layout = QHBoxLayout(control_frame)
        
        # العنوان والتاريخ
        info_layout = QVBoxLayout()
        
        title_label = QLabel("📊 لوحة الإحصائيات والتحليلات")
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: white;
        """)
        info_layout.addWidget(title_label)
        
        date_label = QLabel(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        date_label.setStyleSheet("""
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            margin-top: 5px;
        """)
        info_layout.addWidget(date_label)
        
        control_layout.addLayout(info_layout)
        control_layout.addStretch()
        
        # إعدادات العرض
        settings_layout = QVBoxLayout()
        
        # اختيار الفترة
        period_layout = QHBoxLayout()
        period_label = QLabel("الفترة:")
        period_label.setStyleSheet("color: white; font-weight: bold;")
        period_layout.addWidget(period_label)
        
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "آخر 30 يوم", "آخر 3 أشهر", "آخر 6 أشهر", 
            "السنة الحالية", "مخصص"
        ])
        self.period_combo.setCurrentText("آخر 3 أشهر")
        self.period_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: none;
                border-radius: 6px;
                padding: 8px;
                font-weight: bold;
                min-width: 130px;
            }
        """)
        period_layout.addWidget(self.period_combo)
        
        settings_layout.addLayout(period_layout)
        
        # اختيار التصفية
        filter_layout = QHBoxLayout()
        filter_label = QLabel("التصفية:")
        filter_label.setStyleSheet("color: white; font-weight: bold;")
        filter_layout.addWidget(filter_label)
        
        self.filter_combo = QComboBox()
        self.filter_combo.addItems([
            "جميع الأقسام", "التقنية", "المالية", "التسويق", 
            "الموارد البشرية", "الإدارة"
        ])
        self.filter_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: none;
                border-radius: 6px;
                padding: 8px;
                font-weight: bold;
                min-width: 130px;
            }
        """)
        filter_layout.addWidget(self.filter_combo)
        
        settings_layout.addLayout(filter_layout)
        
        control_layout.addLayout(settings_layout)
        
        layout.addWidget(control_frame)
        
    def create_main_stats(self, layout):
        """إنشاء بطاقات الإحصائيات الرئيسية"""
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 20px;
            }
        """)
        
        stats_layout = QVBoxLayout(stats_frame)
        
        # عنوان القسم
        stats_title = QLabel("📋 الإحصائيات الرئيسية")
        stats_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        stats_layout.addWidget(stats_title)
        
        # بطاقات الإحصائيات
        cards_layout = QHBoxLayout()
        
        # البيانات من المحرك
        stats_data = self.analytics_engine.get_summary_statistics()
        
        # بطاقات مختلفة
        cards_info = [
            ("👥", "إجمالي الموظفين", stats_data['total_employees'], "#3498db"),
            ("📝", "إجمالي الطلبات", stats_data['total_requests'], "#e74c3c"),
            ("✅", "معدل الموافقة", f"{stats_data['approval_rate']:.1f}%", "#27ae60"),
            ("⏱️", "متوسط وقت الرد", f"{stats_data['avg_response_time']:.1f} يوم", "#f39c12"),
            ("📅", "متوسط أيام الإجازة", f"{stats_data['avg_vacation_days']:.1f}", "#9b59b6"),
            ("🎯", "كفاءة النظام", f"{stats_data['efficiency_rate']:.1f}%", "#1abc9c")
        ]
        
        for icon, title, value, color in cards_info:
            card = self.create_stats_card(icon, title, str(value), color)
            cards_layout.addWidget(card)
            
        stats_layout.addLayout(cards_layout)
        layout.addWidget(stats_frame)
        
    def create_stats_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: {color};
                border-radius: 10px;
                padding: 20px;
                min-width: 150px;
                min-height: 100px;
            }}
        """)
        
        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(8)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 28px; color: white;")
        card_layout.addWidget(icon_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: white;
        """)
        card_layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 12px;
            color: rgba(255, 255, 255, 0.9);
        """)
        title_label.setWordWrap(True)
        card_layout.addWidget(title_label)
        
        return card
        
    def create_details_tabs(self, layout):
        """إنشاء تبويبات التفاصيل"""
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                background: white;
            }
            QTabBar::tab {
                background: #f8f9fa;
                border: 2px solid #e0e0e0;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
                color: #2c3e50;
                min-width: 100px;
            }
            QTabBar::tab:selected {
                background: #667eea;
                color: white;
            }
        """)
        
        # تبويب الأقسام
        self.create_departments_tab()
        
        # تبويب الاتجاهات
        self.create_trends_tab()
        
        # تبويب أنواع الإجازات
        self.create_types_tab()
        
        # تبويب التقرير التفصيلي
        self.create_detailed_report_tab()
        
        layout.addWidget(self.tabs)
        
    def create_departments_tab(self):
        """إنشاء تبويب الأقسام"""
        departments_widget = QWidget()
        departments_layout = QVBoxLayout(departments_widget)
        departments_layout.setSpacing(15)
        
        # عنوان
        dept_title = QLabel("🏢 إحصائيات الأقسام التفصيلية")
        dept_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        departments_layout.addWidget(dept_title)
        
        # جدول الأقسام
        self.departments_table = QTableWidget()
        self.departments_table.setColumnCount(6)
        self.departments_table.setHorizontalHeaderLabels([
            "القسم", "عدد الموظفين", "إجمالي الطلبات", "طلبات مقبولة", 
            "معدل الموافقة", "متوسط الرصيد"
        ])
        self.departments_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f1f3f4;
                font-size: 13px;
            }
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #667eea, stop: 1 #764ba2
                );
                color: white;
                border: none;
                padding: 12px;
                font-weight: bold;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #f1f3f4;
            }
        """)
        
        # تحميل بيانات الأقسام
        self.load_departments_data()
        
        departments_layout.addWidget(self.departments_table)
        
        self.tabs.addTab(departments_widget, "🏢 الأقسام")
        
    def load_departments_data(self):
        """تحميل بيانات الأقسام"""
        departments = self.analytics_engine.get_department_statistics()
        
        self.departments_table.setRowCount(len(departments))
        
        for row, dept in enumerate(departments):
            # اسم القسم
            name_item = QTableWidgetItem(dept['name'])
            name_item.setBackground(QColor(240, 248, 255))
            self.departments_table.setItem(row, 0, name_item)
            
            # عدد الموظفين
            employees_item = QTableWidgetItem(str(dept['employees']))
            self.departments_table.setItem(row, 1, employees_item)
            
            # إجمالي الطلبات
            requests_item = QTableWidgetItem(str(dept['requests']))
            self.departments_table.setItem(row, 2, requests_item)
            
            # طلبات مقبولة
            approved_item = QTableWidgetItem(str(dept['approved']))
            approved_item.setBackground(QColor(212, 237, 218))
            self.departments_table.setItem(row, 3, approved_item)
            
            # معدل الموافقة
            approval_rate = (dept['approved'] / dept['requests']) * 100 if dept['requests'] > 0 else 0
            rate_item = QTableWidgetItem(f"{approval_rate:.1f}%")
            if approval_rate >= 90:
                rate_item.setBackground(QColor(212, 237, 218))
            elif approval_rate >= 75:
                rate_item.setBackground(QColor(255, 243, 205))
            else:
                rate_item.setBackground(QColor(248, 215, 218))
            self.departments_table.setItem(row, 4, rate_item)
            
            # متوسط الرصيد
            balance_item = QTableWidgetItem(f"{dept['avg_balance']:.1f}")
            if dept['avg_balance'] > 25:
                balance_item.setBackground(QColor(212, 237, 218))
            elif dept['avg_balance'] > 15:
                balance_item.setBackground(QColor(255, 243, 205))
            else:
                balance_item.setBackground(QColor(248, 215, 218))
            self.departments_table.setItem(row, 5, balance_item)
            
        self.departments_table.resizeColumnsToContents()
        
    def create_trends_tab(self):
        """إنشاء تبويب الاتجاهات"""
        trends_widget = QWidget()
        trends_layout = QVBoxLayout(trends_widget)
        trends_layout.setSpacing(15)
        
        # عنوان
        trends_title = QLabel("📈 الاتجاهات الشهرية")
        trends_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        trends_layout.addWidget(trends_title)
        
        # جدول الاتجاهات
        self.trends_table = QTableWidget()
        self.trends_table.setColumnCount(4)
        self.trends_table.setHorizontalHeaderLabels([
            "الشهر", "إجمالي الطلبات", "طلبات مقبولة", "كفاءة النظام"
        ])
        self.trends_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f1f3f4;
                font-size: 13px;
            }
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f093fb, stop: 1 #f5576c
                );
                color: white;
                border: none;
                padding: 12px;
                font-weight: bold;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #f1f3f4;
            }
        """)
        
        # تحميل بيانات الاتجاهات
        self.load_trends_data()
        
        trends_layout.addWidget(self.trends_table)
        
        # رسم بياني نصي بسيط
        chart_frame = self.create_simple_chart()
        trends_layout.addWidget(chart_frame)
        
        self.tabs.addTab(trends_widget, "📈 الاتجاهات")
        
    def load_trends_data(self):
        """تحميل بيانات الاتجاهات"""
        trends = self.analytics_engine.get_monthly_trends()
        
        self.trends_table.setRowCount(len(trends))
        
        for row, trend in enumerate(trends):
            # الشهر
            month_item = QTableWidgetItem(trend['month'])
            month_item.setBackground(QColor(240, 248, 255))
            self.trends_table.setItem(row, 0, month_item)
            
            # إجمالي الطلبات
            requests_item = QTableWidgetItem(str(trend['requests']))
            self.trends_table.setItem(row, 1, requests_item)
            
            # طلبات مقبولة
            approved_item = QTableWidgetItem(str(trend['approved']))
            approved_item.setBackground(QColor(212, 237, 218))
            self.trends_table.setItem(row, 2, approved_item)
            
            # كفاءة النظام
            efficiency_item = QTableWidgetItem(f"{trend['efficiency']:.1f}%")
            if trend['efficiency'] >= 90:
                efficiency_item.setBackground(QColor(212, 237, 218))
            elif trend['efficiency'] >= 80:
                efficiency_item.setBackground(QColor(255, 243, 205))
            else:
                efficiency_item.setBackground(QColor(248, 215, 218))
            self.trends_table.setItem(row, 3, efficiency_item)
            
        self.trends_table.resizeColumnsToContents()
        
    def create_simple_chart(self):
        """إنشاء رسم بياني نصي بسيط"""
        chart_frame = QFrame()
        chart_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
                margin-top: 15px;
            }
        """)
        
        chart_layout = QVBoxLayout(chart_frame)
        
        chart_title = QLabel("📊 رسم بياني للطلبات الشهرية")
        chart_title.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        chart_layout.addWidget(chart_title)
        
        # رسم بياني نصي بسيط
        trends = self.analytics_engine.get_monthly_trends()
        
        for trend in trends:
            bar_layout = QHBoxLayout()
            
            # اسم الشهر
            month_label = QLabel(trend['month'])
            month_label.setStyleSheet("font-weight: bold; color: #2c3e50; min-width: 80px;")
            bar_layout.addWidget(month_label)
            
            # شريط الطلبات
            requests_bar = QProgressBar()
            requests_bar.setMaximum(50)  # أقصى قيمة متوقعة
            requests_bar.setValue(trend['requests'])
            requests_bar.setFormat(f"{trend['requests']} طلب")
            requests_bar.setStyleSheet("""
                QProgressBar {
                    border: 2px solid #3498db;
                    border-radius: 5px;
                    text-align: center;
                    font-weight: bold;
                    background: white;
                }
                QProgressBar::chunk {
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 1, y2: 0,
                        stop: 0 #3498db, stop: 1 #2980b9
                    );
                    border-radius: 3px;
                }
            """)
            bar_layout.addWidget(requests_bar)
            
            chart_layout.addLayout(bar_layout)
            
        return chart_frame
        
    def create_types_tab(self):
        """إنشاء تبويب أنواع الإجازات"""
        types_widget = QWidget()
        types_layout = QVBoxLayout(types_widget)
        types_layout.setSpacing(15)
        
        # عنوان
        types_title = QLabel("📊 توزيع أنواع الإجازات")
        types_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        types_layout.addWidget(types_title)
        
        # بطاقات أنواع الإجازات
        types_data = self.analytics_engine.get_vacation_types_summary()
        colors = ['#3498db', '#27ae60', '#f39c12', '#e74c3c']
        
        for i, (vacation_type, data) in enumerate(types_data.items()):
            type_card = self.create_vacation_type_card(
                vacation_type, data['count'], data['percentage'], colors[i % len(colors)]
            )
            types_layout.addWidget(type_card)
            
        self.tabs.addTab(types_widget, "📊 أنواع الإجازات")
        
    def create_vacation_type_card(self, type_name, count, percentage, color):
        """إنشاء بطاقة نوع إجازة"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 {color}, stop: 1 rgba(0, 0, 0, 0.1)
                );
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 10px;
            }}
        """)
        
        card_layout = QHBoxLayout(card)
        
        # معلومات النوع
        info_layout = QVBoxLayout()
        
        type_label = QLabel(f"📝 إجازة {type_name}")
        type_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
        """)
        info_layout.addWidget(type_label)
        
        details_label = QLabel(f"العدد: {count} طلب • النسبة: {percentage}%")
        details_label.setStyleSheet("""
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        """)
        info_layout.addWidget(details_label)
        
        card_layout.addLayout(info_layout)
        
        card_layout.addStretch()
        
        # شريط النسبة المئوية
        percentage_bar = QProgressBar()
        percentage_bar.setMaximum(100)
        percentage_bar.setValue(percentage)
        percentage_bar.setFormat(f"{percentage}%")
        percentage_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                color: white;
                background: rgba(255, 255, 255, 0.2);
                min-width: 150px;
            }
            QProgressBar::chunk {
                background: rgba(255, 255, 255, 0.8);
                border-radius: 6px;
            }
        """)
        card_layout.addWidget(percentage_bar)
        
        return card
        
    def create_detailed_report_tab(self):
        """إنشاء تبويب التقرير التفصيلي"""
        report_widget = QWidget()
        report_layout = QVBoxLayout(report_widget)
        report_layout.setSpacing(15)
        
        # عنوان التقرير
        report_title = QLabel("📋 التقرير التفصيلي الشامل")
        report_title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
        """)
        report_title.setAlignment(Qt.AlignCenter)
        report_layout.addWidget(report_title)
        
        # معلومات التقرير
        report_info = QFrame()
        report_info.setStyleSheet("""
            QFrame {
                background: #e8f4fd;
                border: 2px solid #3498db;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        info_layout = QVBoxLayout(report_info)
        
        info_text = QLabel(f"""
        📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        📊 فترة التقرير: {self.period_combo.currentText()}
        🎯 نطاق التصفية: {self.filter_combo.currentText()}
        👤 تم إنشاؤه بواسطة: مدير النظام
        """)
        info_text.setStyleSheet("""
            font-size: 14px;
            color: #2c3e50;
            line-height: 1.6;
        """)
        info_layout.addWidget(info_text)
        
        report_layout.addWidget(report_info)
        
        # محتوى التقرير
        report_content = self.create_report_content()
        report_layout.addWidget(report_content)
        
        self.tabs.addTab(report_widget, "📋 التقرير التفصيلي")
        
    def create_report_content(self):
        """إنشاء محتوى التقرير"""
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: white;
            }
        """)
        
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        
        # الملخص التنفيذي
        summary_label = QLabel("""
        <h3 style="color: #2c3e50;">🎯 الملخص التنفيذي:</h3>
        
        <p><strong>📈 الأداء العام:</strong></p>
        <ul>
        <li>معدل الموافقة على الطلبات: <span style="color: #27ae60; font-weight: bold;">87%</span></li>
        <li>متوسط وقت الاستجابة: <span style="color: #f39c12; font-weight: bold;">1.2 يوم</span></li>
        <li>كفاءة النظام: <span style="color: #3498db; font-weight: bold;">92%</span></li>
        </ul>
        
        <p><strong>📊 الإحصائيات الرئيسية:</strong></p>
        <ul>
        <li>إجمالي الطلبات: <strong>245 طلب</strong></li>
        <li>أكثر الأقسام نشاطاً: <strong>قسم التقنية</strong> (18 طلب)</li>
        <li>أكثر أنواع الإجازات: <strong>الإجازات اليومية</strong> (65%)</li>
        <li>متوسط أيام الإجازة للموظف: <strong>8.5 يوم</strong></li>
        </ul>
        
        <p><strong>⚠️ التحديات والفرص:</strong></p>
        <ul>
        <li>15 موظف لديهم رصيد منخفض (أقل من 5 أيام)</li>
        <li>فترة ذروة متوقعة في ديسمبر (إجازات نهاية العام)</li>
        <li>فرصة تحسين وقت الاستجابة في بعض الأقسام</li>
        <li>إمكانية تحسين توزيع أرصدة الإجازات</li>
        </ul>
        
        <p><strong>🎯 التوصيات:</strong></p>
        <ul>
        <li>مراجعة أرصدة الموظفين ذوي الرصيد المنخفض</li>
        <li>التخطيط المبكر لفترات الذروة القادمة</li>
        <li>تدريب مديري الأقسام على إدارة الطلبات</li>
        <li>تطوير سياسات واضحة لأنواع الإجازات المختلفة</li>
        <li>تحسين عملية الموافقة الإلكترونية</li>
        </ul>
        
        <p><strong>📈 النظرة المستقبلية:</strong></p>
        <ul>
        <li>توقع زيادة الطلبات بنسبة 15% في الربع القادم</li>
        <li>تطوير ميزات ذكية لتوقع فترات الذروة</li>
        <li>تحسين تجربة المستخدم في النظام</li>
        <li>إضافة تقارير تفاعلية أكثر تفصيلاً</li>
        </ul>
        """)
        
        summary_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                line-height: 1.8;
                color: #2c3e50;
                background: white;
                padding: 20px;
                border-radius: 8px;
            }
        """)
        summary_label.setWordWrap(True)
        content_layout.addWidget(summary_label)
        
        scroll_area.setWidget(content_widget)
        
        return scroll_area
        
    def refresh_data(self):
        """تحديث البيانات"""
        # إعادة تحميل البيانات
        self.load_departments_data()
        self.load_trends_data()
        
        QMessageBox.information(self, "تم التحديث", "تم تحديث جميع البيانات والإحصائيات بنجاح!")
        
    def export_report(self):
        """تصدير التقرير"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ التقرير", 
            f"analytics_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html",
            "HTML Files (*.html);;Text Files (*.txt)"
        )
        
        if file_path:
            # إنشاء تقرير HTML بسيط
            html_content = f"""
            <!DOCTYPE html>
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>تقرير الإحصائيات</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #3498db; }}
                    .stat {{ background: #f8f9fa; padding: 10px; margin: 5px; border-radius: 5px; }}
                </style>
            </head>
            <body>
                <h1>📊 تقرير الإحصائيات والتحليلات</h1>
                <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <h2>الإحصائيات الرئيسية:</h2>
                <div class="stat">إجمالي الموظفين: 156</div>
                <div class="stat">إجمالي الطلبات: 245</div>
                <div class="stat">معدل الموافقة: 87%</div>
                <div class="stat">متوسط وقت الرد: 1.2 يوم</div>
            </body>
            </html>
            """
            
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                QMessageBox.information(self, "تم التصدير", f"تم تصدير التقرير إلى:\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"لا يمكن حفظ التقرير:\n{str(e)}")
                
    def email_report(self):
        """إرسال التقرير بالبريد الإلكتروني"""
        email, ok = QInputDialog.getText(
            self, "إرسال التقرير", 
            "أدخل عنوان البريد الإلكتروني:"
        )
        
        if ok and email:
            # محاكاة إرسال البريد
            QMessageBox.information(
                self, "تم الإرسال", 
                f"تم إرسال التقرير بنجاح إلى:\n{email}\n\nملاحظة: هذا إرسال تجريبي"
            )

def test_simple_analytics():
    """اختبار لوحة الإحصائيات المبسطة"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # اختبار نافذة الإحصائيات المبسطة
    analytics_window = ModernSimpleAnalyticsWindow()
    analytics_window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    test_simple_analytics()
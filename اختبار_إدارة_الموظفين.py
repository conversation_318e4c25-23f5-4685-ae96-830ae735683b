#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام إدارة الموظفين والقوائم المنسدلة
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# استيراد الوحدات
from إدارة_الموظفين import EmployeeManagementWindow, get_employee_names
from نوافذ_محسّنة_شاملة import DailyVacationWindow, HourlyVacationWindow

class TestMainWindow(QMainWindow):
    """نافذة اختبار رئيسية"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("🧪 اختبار نظام إدارة الموظفين")
        self.setGeometry(100, 100, 600, 400)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - 600) // 2
        y = (screen.height() - 400) // 2
        self.move(x, y)
        
        # الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # العنوان
        title_label = QLabel("🧪 اختبار نظام إدارة الموظفين")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 22px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #e74c3c, stop: 1 #c0392b);
                border-radius: 12px;
                padding: 20px;
                margin: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات الموظفين الحاليين
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        info_layout = QVBoxLayout(info_frame)
        
        info_title = QLabel("📋 قائمة الموظفين الحالية:")
        info_title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        info_layout.addWidget(info_title)
        
        # عرض قائمة الموظفين
        self.employees_list = QListWidget()
        self.employees_list.setStyleSheet("""
            QListWidget {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 14px;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 5px;
                background: #f8f9fa;
            }
            QListWidget::item {
                padding: 5px;
                border-bottom: 1px solid #dee2e6;
            }
            QListWidget::item:selected {
                background: #007bff;
                color: white;
            }
        """)
        self.refresh_employees_list()
        info_layout.addWidget(self.employees_list)
        
        layout.addWidget(info_frame)
        
        # أزرار الاختبار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        buttons_layout = QGridLayout(buttons_frame)
        buttons_layout.setSpacing(15)
        
        # أزرار الوظائف
        buttons = [
            ("👥 إدارة الموظفين", "فتح نافذة إدارة قائمة الموظفين", "#2c3e50", self.open_employee_management),
            ("📝 طلب إجازة يومية", "اختبار القائمة المنسدلة في طلب الإجازة", "#27ae60", self.test_daily_vacation),
            ("⏱️ طلب إجازة ساعية", "اختبار القائمة المنسدلة في الإجازة الساعية", "#e67e22", self.test_hourly_vacation),
            ("🔄 تحديث القائمة", "تحديث قائمة الموظفين المعروضة", "#3498db", self.refresh_employees_list)
        ]
        
        for i, (title, description, color, callback) in enumerate(buttons):
            button = self.create_test_button(title, description, color, callback)
            row = i // 2
            col = i % 2
            buttons_layout.addWidget(button, row, col)
        
        layout.addWidget(buttons_frame)
        
        # تعليمات الاختبار
        instructions = QLabel("""
📝 تعليمات الاختبار:
1. ابدأ بفتح نافذة إدارة الموظفين لإضافة أو تعديل الموظفين
2. اختبر نوافذ طلبات الإجازات للتأكد من ظهور القوائم المنسدلة
3. تحقق من إمكانية إضافة موظفين جدد من نوافذ الطلبات
4. استخدم زر التحديث لتحديث القائمة المعروضة
        """)
        instructions.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 12px;
                color: #6c757d;
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px 0px;
            }
        """)
        layout.addWidget(instructions)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
        
    def create_test_button(self, title, description, color, callback):
        """إنشاء زر اختبار"""
        button = QPushButton()
        button.setFixedHeight(80)
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color}, stop: 1 rgba(0,0,0,0.1));
                border: none;
                border-radius: 10px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-weight: bold;
                text-align: left;
                padding: 15px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 rgba(255,255,255,0.2), stop: 1 {color});
            }}
            QPushButton:pressed {{
                background: {color};
            }}
        """)
        
        button.setText(f"{title}\n{description}")
        button.clicked.connect(callback)
        
        return button
        
    def refresh_employees_list(self):
        """تحديث قائمة الموظفين المعروضة"""
        self.employees_list.clear()
        try:
            employee_names = get_employee_names()
            for name in employee_names:
                self.employees_list.addItem(f"👤 {name}")
            
            # إضافة معلومة عن العدد
            count_item = QListWidgetItem(f"📊 إجمالي الموظفين: {len(employee_names)}")
            count_item.setBackground(QColor("#e9ecef"))
            self.employees_list.addItem(count_item)
            
        except Exception as e:
            self.employees_list.addItem(f"❌ خطأ في تحميل البيانات: {e}")
    
    def open_employee_management(self):
        """فتح نافذة إدارة الموظفين"""
        try:
            window = EmployeeManagementWindow()
            result = window.exec_()
            if result == QDialog.Accepted:
                self.refresh_employees_list()
                QMessageBox.information(self, "✅ تم", "تم تحديث قائمة الموظفين!")
        except Exception as e:
            QMessageBox.critical(self, "❌ خطأ", f"فشل في فتح نافذة إدارة الموظفين:\n{e}")
    
    def test_daily_vacation(self):
        """اختبار نافذة طلب الإجازة اليومية"""
        try:
            window = DailyVacationWindow()
            window.exec_()
            # تحديث القائمة في حالة إضافة موظفين جدد
            self.refresh_employees_list()
        except Exception as e:
            QMessageBox.critical(self, "❌ خطأ", f"فشل في فتح نافذة طلب الإجازة اليومية:\n{e}")
    
    def test_hourly_vacation(self):
        """اختبار نافذة طلب الإجازة الساعية"""
        try:
            window = HourlyVacationWindow()
            window.exec_()
            # تحديث القائمة في حالة إضافة موظفين جدد
            self.refresh_employees_list()
        except Exception as e:
            QMessageBox.critical(self, "❌ خطأ", f"فشل في فتح نافذة طلب الإجازة الساعية:\n{e}")

def main():
    """الدالة الرئيسية للاختبار"""
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    font = QFont("Sakkal Majalla", 10)
    app.setFont(font)
    
    print("🧪 بدء اختبار نظام إدارة الموظفين...")
    print("=" * 50)
    
    # معلومات الاختبار
    try:
        employee_names = get_employee_names()
        print(f"📋 عدد الموظفين المحملين: {len(employee_names)}")
        print("👥 الموظفين:")
        for i, name in enumerate(employee_names, 1):
            print(f"   {i}. {name}")
    except Exception as e:
        print(f"❌ خطأ في تحميل الموظفين: {e}")
    
    print("=" * 50)
    print("🚀 فتح نافذة الاختبار...")
    
    # فتح نافذة الاختبار
    window = TestMainWindow()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
لوحة التحليلات والرسوم البيانية
Analytics Dashboard with Charts
"""

import sys
import os
from datetime import datetime, timedelta
from database import VacationDatabase
from notification_system import NotificationSystem
import json

class AnalyticsDashboard:
    def __init__(self):
        self.db = VacationDatabase()
        self.notification_system = NotificationSystem()
    
    def display_main_dashboard(self):
        """عرض لوحة التحليلات الرئيسية"""
        print("\n" + "="*80)
        print("📊 لوحة التحليلات والإحصائيات المتقدمة")
        print("="*80)
        
        # إحصائيات عامة
        self._display_general_stats()
        
        print("\n" + "-"*80)
        print("📈 خيارات التحليل:")
        print("1. تحليل الإجازات الشهرية")
        print("2. تحليل أداء الموظفين")
        print("3. توزيع أنواع الإجازات")
        print("4. تحليل الاتجاهات الزمنية")
        print("5. تقرير الرصيد والاستهلاك")
        print("6. رسم بياني نصي للبيانات")
        print("7. مقارنة الأشهر")
        print("0. العودة للقائمة الرئيسية")
        print("="*80)
    
    def _display_general_stats(self):
        """عرض الإحصائيات العامة"""
        # إحصائيات أساسية
        employees = self.db.get_all_employees()
        daily_requests = self.db.get_requests_by_type('daily')
        hourly_requests = self.db.get_requests_by_type('hourly')
        added_vacations = self.db.get_requests_by_type('added')
        
        # إحصائيات الشهر الحالي
        current_date = datetime.now()
        monthly_data = self.db.get_monthly_report(current_date.year, current_date.month)
        
        print(f"📊 الإحصائيات العامة:")
        print(f"   👥 إجمالي الموظفين: {len(employees)}")
        print(f"   📅 إجمالي الطلبات اليومية: {len(daily_requests)}")
        print(f"   ⏰ إجمالي الطلبات الساعية: {len(hourly_requests)}")
        print(f"   ➕ إجمالي الإجازات المدرجة: {len(added_vacations)}")
        
        print(f"\n📈 إحصائيات الشهر الحالي ({current_date.strftime('%Y-%m')}):")
        print(f"   📅 طلبات يومية: {monthly_data['daily_stats'][0]}")
        print(f"   📅 أيام يومية: {monthly_data['daily_stats'][1]}")
        print(f"   ⏰ طلبات ساعية: {monthly_data['hourly_stats'][0]}")
        print(f"   ⏰ ساعات: {monthly_data['hourly_stats'][1]}")
    
    def analyze_monthly_vacations(self):
        """تحليل الإجازات الشهرية"""
        print("\n📊 تحليل الإجازات الشهرية")
        print("-" * 50)
        
        year = int(input("السنة (افتراضي: السنة الحالية): ") or datetime.now().year)
        
        monthly_stats = []
        for month in range(1, 13):
            data = self.db.get_monthly_report(year, month)
            monthly_stats.append({
                'month': month,
                'daily_requests': data['daily_stats'][0],
                'daily_days': data['daily_stats'][1],
                'hourly_requests': data['hourly_stats'][0],
                'hourly_hours': data['hourly_stats'][1]
            })
        
        # عرض البيانات في جدول
        print(f"\n📈 إحصائيات شهرية لسنة {year}:")
        print("الشهر".ljust(8) + "طلبات يومية".ljust(12) + "أيام يومية".ljust(12) + "طلبات ساعية".ljust(14) + "ساعات")
        print("-" * 60)
        
        months_ar = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
        
        for stat in monthly_stats:
            month_name = months_ar[stat['month'] - 1]
            print(f"{month_name:<8} {stat['daily_requests']:<12} {stat['daily_days']:<12} {stat['hourly_requests']:<14} {stat['hourly_hours']}")
        
        # رسم بياني نصي بسيط
        print(f"\n📊 رسم بياني للطلبات اليومية:")
        max_requests = max(stat['daily_requests'] for stat in monthly_stats)
        if max_requests > 0:
            for stat in monthly_stats:
                month_name = months_ar[stat['month'] - 1][:3]
                bar_length = int((stat['daily_requests'] / max_requests) * 30)
                bar = "█" * bar_length
                print(f"{month_name}: {bar} ({stat['daily_requests']})")
    
    def analyze_employee_performance(self):
        """تحليل أداء الموظفين"""
        print("\n👥 تحليل أداء الموظفين")
        print("-" * 50)
        
        employees = self.db.get_all_employees()
        employee_stats = []
        
        for employee in employees:
            balance = self.db.get_employee_balance(employee)
            
            # حساب معدل الاستخدام
            total_available = balance['initial_balance'] + balance['added_vacations']
            total_used = balance['daily_used'] + balance['hourly_used']
            usage_rate = (total_used / total_available * 100) if total_available > 0 else 0
            
            employee_stats.append({
                'name': employee,
                'initial_balance': balance['initial_balance'],
                'added_vacations': balance['added_vacations'],
                'daily_used': balance['daily_used'],
                'hourly_used': balance['hourly_used'],
                'net_balance': balance['net_balance'],
                'usage_rate': usage_rate
            })
        
        # ترتيب حسب معدل الاستخدام
        employee_stats.sort(key=lambda x: x['usage_rate'], reverse=True)
        
        print("📊 ترتيب الموظفين حسب معدل استخدام الإجازات:")
        print("الاسم".ljust(20) + "الرصيد الابتدائي".ljust(15) + "المستخدم".ljust(10) + "الباقي".ljust(10) + "معدل الاستخدام")
        print("-" * 70)
        
        for i, stat in enumerate(employee_stats[:10], 1):  # أول 10 موظفين
            name = stat['name'][:18] + ".." if len(stat['name']) > 20 else stat['name']
            total_used = stat['daily_used'] + stat['hourly_used']
            print(f"{name:<20} {stat['initial_balance']:<15} {total_used:<10.1f} {stat['net_balance']:<10.1f} {stat['usage_rate']:.1f}%")
        
        # إحصائيات إضافية
        avg_usage = sum(stat['usage_rate'] for stat in employee_stats) / len(employee_stats)
        high_usage = len([stat for stat in employee_stats if stat['usage_rate'] > 80])
        low_balance = len([stat for stat in employee_stats if stat['net_balance'] < 5])
        
        print(f"\n📈 ملخص التحليل:")
        print(f"   متوسط معدل الاستخدام: {avg_usage:.1f}%")
        print(f"   موظفين بمعدل استخدام عالي (>80%): {high_usage}")
        print(f"   موظفين برصيد منخفض (<5 أيام): {low_balance}")
    
    def analyze_vacation_types(self):
        """تحليل توزيع أنواع الإجازات"""
        print("\n📋 تحليل توزيع أنواع الإجازات")
        print("-" * 50)
        
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        # إحصائيات أنواع الإجازات
        cursor.execute('''
            SELECT vacation_type, COUNT(*) as count, SUM(days_count) as total_days
            FROM daily_requests 
            GROUP BY vacation_type 
            ORDER BY count DESC
        ''')
        
        vacation_types = cursor.fetchall()
        conn.close()
        
        if not vacation_types:
            print("⚠️ لا توجد بيانات إجازات يومية")
            return
        
        total_requests = sum(vt[1] for vt in vacation_types)
        total_days = sum(vt[2] for vt in vacation_types)
        
        print("📊 توزيع أنواع الإجازات:")
        print("نوع الإجازة".ljust(20) + "عدد الطلبات".ljust(12) + "إجمالي الأيام".ljust(15) + "النسبة")
        print("-" * 60)
        
        for vacation_type, count, days in vacation_types:
            percentage = (count / total_requests * 100) if total_requests > 0 else 0
            type_name = vacation_type[:18] + ".." if len(vacation_type) > 20 else vacation_type
            print(f"{type_name:<20} {count:<12} {days:<15} {percentage:.1f}%")
        
        print(f"\n📈 الإجمالي: {total_requests} طلب، {total_days} يوم")
        
        # رسم بياني نصي
        print(f"\n📊 رسم بياني للتوزيع:")
        max_count = max(vt[1] for vt in vacation_types)
        for vacation_type, count, days in vacation_types:
            bar_length = int((count / max_count) * 30) if max_count > 0 else 0
            bar = "█" * bar_length
            type_short = vacation_type[:10] + ".." if len(vacation_type) > 12 else vacation_type
            print(f"{type_short:<12}: {bar} ({count})")
    
    def analyze_time_trends(self):
        """تحليل الاتجاهات الزمنية"""
        print("\n📈 تحليل الاتجاهات الزمنية")
        print("-" * 50)
        
        # تحليل آخر 12 شهر
        current_date = datetime.now()
        monthly_trends = []
        
        for i in range(12):
            target_date = current_date - timedelta(days=30 * i)
            year = target_date.year
            month = target_date.month
            
            data = self.db.get_monthly_report(year, month)
            monthly_trends.append({
                'year_month': f"{year}-{month:02d}",
                'daily_requests': data['daily_stats'][0],
                'hourly_requests': data['hourly_stats'][0],
                'total_requests': data['daily_stats'][0] + data['hourly_stats'][0]
            })
        
        monthly_trends.reverse()  # ترتيب زمني
        
        print("📊 اتجاه الطلبات خلال آخر 12 شهر:")
        print("الشهر".ljust(10) + "طلبات يومية".ljust(12) + "طلبات ساعية".ljust(14) + "الإجمالي")
        print("-" * 50)
        
        for trend in monthly_trends:
            print(f"{trend['year_month']:<10} {trend['daily_requests']:<12} {trend['hourly_requests']:<14} {trend['total_requests']}")
        
        # حساب الاتجاه
        if len(monthly_trends) >= 6:
            first_half = sum(t['total_requests'] for t in monthly_trends[:6])
            second_half = sum(t['total_requests'] for t in monthly_trends[6:])
            
            if second_half > first_half:
                trend_direction = "📈 متزايد"
            elif second_half < first_half:
                trend_direction = "📉 متناقص"
            else:
                trend_direction = "➡️ مستقر"
            
            print(f"\n🔍 الاتجاه العام: {trend_direction}")
            print(f"   النصف الأول: {first_half} طلب")
            print(f"   النصف الثاني: {second_half} طلب")
    
    def balance_consumption_report(self):
        """تقرير الرصيد والاستهلاك"""
        print("\n💰 تقرير الرصيد والاستهلاك")
        print("-" * 50)
        
        employees = self.db.get_all_employees()
        
        total_initial = 0
        total_added = 0
        total_used_daily = 0
        total_used_hourly = 0
        total_remaining = 0
        
        balance_ranges = {'0-5': 0, '6-15': 0, '16-30': 0, '30+': 0}
        
        for employee in employees:
            balance = self.db.get_employee_balance(employee)
            
            total_initial += balance['initial_balance']
            total_added += balance['added_vacations']
            total_used_daily += balance['daily_used']
            total_used_hourly += balance['hourly_used']
            total_remaining += balance['net_balance']
            
            # تصنيف الرصيد
            net_balance = balance['net_balance']
            if net_balance <= 5:
                balance_ranges['0-5'] += 1
            elif net_balance <= 15:
                balance_ranges['6-15'] += 1
            elif net_balance <= 30:
                balance_ranges['16-30'] += 1
            else:
                balance_ranges['30+'] += 1
        
        print("📊 ملخص الرصيد والاستهلاك:")
        print(f"   إجمالي الرصيد الابتدائي: {total_initial:.1f} يوم")
        print(f"   إجمالي الإجازات المدرجة: {total_added:.1f} يوم")
        print(f"   إجمالي المستخدم (يومي): {total_used_daily:.1f} يوم")
        print(f"   إجمالي المستخدم (ساعي): {total_used_hourly:.1f} يوم")
        print(f"   إجمالي الرصيد المتبقي: {total_remaining:.1f} يوم")
        
        total_available = total_initial + total_added
        total_used = total_used_daily + total_used_hourly
        usage_percentage = (total_used / total_available * 100) if total_available > 0 else 0
        
        print(f"\n📈 معدل الاستهلاك العام: {usage_percentage:.1f}%")
        
        print(f"\n📊 توزيع الموظفين حسب الرصيد المتبقي:")
        for range_name, count in balance_ranges.items():
            percentage = (count / len(employees) * 100) if employees else 0
            print(f"   {range_name} أيام: {count} موظف ({percentage:.1f}%)")
    
    def create_text_chart(self):
        """إنشاء رسم بياني نصي"""
        print("\n📊 رسم بياني نصي للبيانات")
        print("-" * 50)
        
        print("اختر نوع البيانات:")
        print("1. الطلبات الشهرية")
        print("2. أرصدة الموظفين")
        print("3. أنواع الإجازات")
        
        choice = input("اختر نوع الرسم البياني: ").strip()
        
        if choice == '1':
            self._chart_monthly_requests()
        elif choice == '2':
            self._chart_employee_balances()
        elif choice == '3':
            self._chart_vacation_types()
        else:
            print("⚠️ اختيار غير صحيح")
    
    def _chart_monthly_requests(self):
        """رسم بياني للطلبات الشهرية"""
        year = int(input("السنة (افتراضي: السنة الحالية): ") or datetime.now().year)
        
        monthly_data = []
        for month in range(1, 13):
            data = self.db.get_monthly_report(year, month)
            monthly_data.append(data['daily_stats'][0] + data['hourly_stats'][0])
        
        max_requests = max(monthly_data) if monthly_data else 1
        
        print(f"\n📊 الطلبات الشهرية لسنة {year}:")
        months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
        
        for i, requests in enumerate(monthly_data):
            bar_length = int((requests / max_requests) * 40) if max_requests > 0 else 0
            bar = "█" * bar_length
            print(f"{months[i][:3]}: {bar} ({requests})")
    
    def _chart_employee_balances(self):
        """رسم بياني لأرصدة الموظفين"""
        employees = self.db.get_all_employees()[:15]  # أول 15 موظف
        
        balances = []
        names = []
        
        for employee in employees:
            balance = self.db.get_employee_balance(employee)
            balances.append(balance['net_balance'])
            names.append(employee[:10] + ".." if len(employee) > 12 else employee)
        
        max_balance = max(balances) if balances else 1
        
        print(f"\n📊 أرصدة الموظفين:")
        for i, balance in enumerate(balances):
            bar_length = int((balance / max_balance) * 30) if max_balance > 0 else 0
            bar = "█" * bar_length
            print(f"{names[i]:<12}: {bar} ({balance:.1f})")
    
    def _chart_vacation_types(self):
        """رسم بياني لأنواع الإجازات"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT vacation_type, COUNT(*) 
            FROM daily_requests 
            GROUP BY vacation_type 
            ORDER BY COUNT(*) DESC
        ''')
        
        vacation_data = cursor.fetchall()
        conn.close()
        
        if not vacation_data:
            print("⚠️ لا توجد بيانات")
            return
        
        max_count = max(vd[1] for vd in vacation_data)
        
        print(f"\n📊 أنواع الإجازات:")
        for vacation_type, count in vacation_data:
            bar_length = int((count / max_count) * 30) if max_count > 0 else 0
            bar = "█" * bar_length
            type_name = vacation_type[:12] + ".." if len(vacation_type) > 14 else vacation_type
            print(f"{type_name:<14}: {bar} ({count})")
    
    def compare_months(self):
        """مقارنة الأشهر"""
        print("\n📊 مقارنة الأشهر")
        print("-" * 50)
        
        year1 = int(input("السنة الأولى: "))
        month1 = int(input("الشهر الأول (1-12): "))
        year2 = int(input("السنة الثانية: "))
        month2 = int(input("الشهر الثاني (1-12): "))
        
        data1 = self.db.get_monthly_report(year1, month1)
        data2 = self.db.get_monthly_report(year2, month2)
        
        print(f"\n📊 مقارنة {year1}-{month1:02d} مع {year2}-{month2:02d}:")
        print("-" * 60)
        
        metrics = [
            ('الطلبات اليومية', data1['daily_stats'][0], data2['daily_stats'][0]),
            ('الأيام اليومية', data1['daily_stats'][1], data2['daily_stats'][1]),
            ('الطلبات الساعية', data1['hourly_stats'][0], data2['hourly_stats'][0]),
            ('الساعات', data1['hourly_stats'][1], data2['hourly_stats'][1])
        ]
        
        for metric_name, value1, value2 in metrics:
            diff = value2 - value1
            diff_percent = (diff / value1 * 100) if value1 > 0 else 0
            trend = "📈" if diff > 0 else "📉" if diff < 0 else "➡️"
            
            print(f"{metric_name:<15}: {value1:<8} → {value2:<8} {trend} ({diff:+.1f}, {diff_percent:+.1f}%)")
    
    def run(self):
        """تشغيل لوحة التحليلات"""
        while True:
            try:
                self.display_main_dashboard()
                choice = input("اختر نوع التحليل: ").strip()
                
                if choice == '0':
                    break
                elif choice == '1':
                    self.analyze_monthly_vacations()
                elif choice == '2':
                    self.analyze_employee_performance()
                elif choice == '3':
                    self.analyze_vacation_types()
                elif choice == '4':
                    self.analyze_time_trends()
                elif choice == '5':
                    self.balance_consumption_report()
                elif choice == '6':
                    self.create_text_chart()
                elif choice == '7':
                    self.compare_months()
                else:
                    print("⚠️ اختيار غير صحيح")
                
                input("\nاضغط Enter للمتابعة...")
                
            except KeyboardInterrupt:
                print("\n\n👋 تم إيقاف البرنامج")
                break
            except Exception as e:
                print(f"\n❌ خطأ غير متوقع: {e}")
                input("اضغط Enter للمتابعة...")

if __name__ == "__main__":
    dashboard = AnalyticsDashboard()
    dashboard.run()

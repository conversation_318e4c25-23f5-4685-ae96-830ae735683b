#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف إعداد نظام إدارة الإجازات
"""

import subprocess
import sys
import os

def install_package(package):
    """تثبيت مكتبة"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--user", package])
        return True
    except subprocess.CalledProcessError:
        return False

def setup_project():
    """إعداد المشروع"""
    print("🔧 بدء إعداد نظام إدارة الإجازات")
    print("=" * 50)
    
    # قائمة المكتبات المطلوبة
    packages = [
        "pandas",
        "openpyxl", 
        "PyQt5",
        "reportlab"
    ]
    
    print("📦 تثبيت المكتبات المطلوبة...")
    
    success_count = 0
    for package in packages:
        print(f"🔄 تثبيت {package}...")
        if install_package(package):
            print(f"✅ تم تثبيت {package} بنجاح")
            success_count += 1
        else:
            print(f"❌ فشل في تثبيت {package}")
    
    print(f"\n📊 النتائج: {success_count}/{len(packages)} مكتبة تم تثبيتها")
    
    if success_count == len(packages):
        print("🎉 تم تثبيت جميع المكتبات بنجاح!")
        
        # إنشاء ملف Excel النموذجي
        print("\n📋 إنشاء ملف Excel النموذجي...")
        try:
            exec(open('create_sample_excel.py').read())
            print("✅ تم إنشاء ملف Excel النموذجي")
        except Exception as e:
            print(f"❌ فشل في إنشاء ملف Excel: {e}")
        
        # اختبار البرنامج
        print("\n🧪 تشغيل اختبار البرنامج...")
        try:
            exec(open('test_app.py').read())
        except Exception as e:
            print(f"❌ فشل في اختبار البرنامج: {e}")
        
        print("\n🚀 البرنامج جاهز للتشغيل!")
        print("💡 لتشغيل البرنامج:")
        print("   python run_app.py")
        print("   أو انقر نقراً مزدوجاً على: تشغيل_البرنامج.bat")
        
        return True
    else:
        print("⚠️ فشل في تثبيت بعض المكتبات")
        print("🔧 جرب تشغيل الأمر التالي:")
        print("   pip install -r requirements.txt")
        return False

if __name__ == '__main__':
    setup_project()
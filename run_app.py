#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل نظام إدارة الإجازات
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """تشغيل البرنامج"""
    try:
        print("🚀 بدء تشغيل نظام إدارة الإجازات...")
        
        # استيراد المكتبات
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد الخط والاتجاه للنصوص العربية
        app.setLayoutDirection(Qt.RightToLeft)
        font = QFont("Arial", 10)
        app.setFont(font)
        
        # استيراد النافذة الرئيسية
        from main_window import MainWindow
        
        # إنشاء وعرض النافذة
        window = MainWindow()
        window.show()
        
        print("✅ تم تشغيل البرنامج بنجاح!")
        print("💡 معلومات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        print("🔧 تأكد من تثبيت جميع المكتبات المطلوبة")
        return False

if __name__ == '__main__':
    main()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف التشغيل النهائي - نظام إدارة الإجازات الشامل المحسّن
"""

import sys
import os

def main():
    """تشغيل النظام بأفضل إعدادات"""
    
    print("🚀 مرحباً بك في نظام إدارة الإجازات")
    print("=" * 50)
    print("📊 نظام شامل لإدارة إجازات الموظفين")
    print("✨ تصميم عصري ومقاسات محسّنة")
    print("🔧 جميع الميزات متاحة ومحسّنة")
    print("=" * 50)
    
    try:
        # استيراد النظام الشامل
        from النظام_الشامل_المحسّن import main as run_system
        
        print("🔄 جاري تحميل النظام...")
        
        # تشغيل النظام
        return run_system()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الملفات: {e}")
        print("💡 تأكد من وجود جميع ملفات النظام")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("💡 راجع ملفات النظام وأعد المحاولة")
    
    finally:
        print("\n📞 للدعم الفني أو الاستفسارات:")
        print("   📧 البريد الإلكتروني: <EMAIL>")
        print("   📱 الهاتف: +966-XX-XXX-XXXX")
        input("\n⏳ اضغط Enter للإغلاق...")

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n⚠️ تم إنهاء البرنامج بواسطة المستخدم")
        sys.exit(0)
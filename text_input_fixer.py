#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أداة إصلاح وتوحيد مربعات النص في جميع النوافذ
"""

import os
import re

def get_improved_input_style():
    """إرجاع أنماط محسّنة لمربعات النص"""
    return """
    QLineEdit {
        font-family: "Sakkal Majalla", "Arial", sans-serif;
        background: white;
        border: 2px solid #bdc3c7;
        border-radius: 12px;
        padding: 15px;
        font-size: 16px;
        font-weight: bold;
        color: #1a1a1a;
        min-height: 50px;
        max-height: 60px;
        selection-background-color: #3498db;
    }
    
    QLineEdit:focus {
        border: 2px solid #3498db;
        background: #ffffff;
        color: #000000;
    }
    
    QLineEdit:hover {
        border: 2px solid #5dade2;
        background: #ffffff;
    }
    
    QLineEdit::placeholder {
        color: #95a5a6;
        font-style: italic;
        font-size: 14px;
    }
    
    QTextEdit {
        font-family: "Sakka<PERSON> Majall<PERSON>", "Arial", sans-serif;
        background: white;
        border: 2px solid #bdc3c7;
        border-radius: 12px;
        padding: 15px;
        font-size: 16px;
        font-weight: bold;
        color: #1a1a1a;
        min-height: 80px;
        max-height: 120px;
        selection-background-color: #3498db;
    }
    
    QTextEdit:focus {
        border: 2px solid #3498db;
        background: #ffffff;
        color: #000000;
    }
    
    QComboBox {
        font-family: "Sakkal Majalla", "Arial", sans-serif;
        background: white;
        border: 2px solid #bdc3c7;
        border-radius: 12px;
        padding: 15px;
        font-size: 16px;
        font-weight: bold;
        color: #1a1a1a;
        min-height: 50px;
        max-height: 60px;
    }
    
    QComboBox:focus {
        border: 2px solid #3498db;
    }
    
    QSpinBox, QDoubleSpinBox {
        font-family: "Sakkal Majalla", "Arial", sans-serif;
        background: white;
        border: 2px solid #bdc3c7;
        border-radius: 12px;
        padding: 15px;
        font-size: 16px;
        font-weight: bold;
        color: #1a1a1a;
        min-height: 50px;
        max-height: 60px;
    }
    
    QDateEdit, QTimeEdit, QDateTimeEdit {
        font-family: "Sakkal Majalla", "Arial", sans-serif;
        background: white;
        border: 2px solid #bdc3c7;
        border-radius: 12px;
        padding: 15px;
        font-size: 16px;
        font-weight: bold;
        color: #1a1a1a;
        min-height: 50px;
        max-height: 60px;
    }
    """

def fix_file_input_sizes(file_path):
    """إصلاح أحجام مربعات النص في ملف محدد"""
    print(f"🔧 إصلاح الملف: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ الملف غير موجود: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # إصلاح ارتفاع مربعات النص الصغيرة
        content = re.sub(r'setMinimumHeight\((\d+)\)', lambda m: 
                        f'setMinimumHeight(55)' if int(m.group(1)) < 50 else m.group(0), content)
        content = re.sub(r'setMaximumHeight\((\d+)\)', lambda m: 
                        f'setMaximumHeight(65)' if int(m.group(1)) < 60 else m.group(0), content)
        
        # إصلاح الخط في المربعات
        content = re.sub(r'font-size:\s*(\d+)px', lambda m:
                        f'font-size: 16px' if int(m.group(1)) < 14 else m.group(0), content)
        
        # إضافة خط Sakkal Majalla إذا لم يكن موجود
        if 'font-family:' in content and 'Sakkal Majalla' not in content:
            content = re.sub(r'font-family:\s*[^;]+;', 
                           'font-family: "Sakkal Majalla", "Arial", sans-serif;', content)
        
        # حفظ الملف فقط إذا كان هناك تغيير
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ تم إصلاح الملف: {file_path}")
            return True
        else:
            print(f"ℹ️ لا يحتاج إصلاح: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح الملف {file_path}: {e}")
        return False

def fix_all_files():
    """إصلاح جميع الملفات في المجلد"""
    base_path = "c:/Users/<USER>/OneDrive/Documents/vacation"
    
    # قائمة الملفات المهمة
    files_to_fix = [
        "modern_request_windows.py",
        "modern_dialogs.py", 
        "modern_advanced_windows.py",
        "modern_edit_delete.py",
        "modern_analytics_dashboard.py",
        "modern_notifications_backup.py",
        "modern_search_settings.py"
    ]
    
    fixed_count = 0
    
    print("🚀 بدء عملية إصلاح مربعات النص...")
    print("=" * 50)
    
    for file_name in files_to_fix:
        file_path = os.path.join(base_path, file_name)
        if fix_file_input_sizes(file_path):
            fixed_count += 1
    
    print("=" * 50)
    print(f"✅ تم إصلاح {fixed_count} ملف من أصل {len(files_to_fix)} ملف")
    print("🎉 انتهت عملية الإصلاح!")

if __name__ == "__main__":
    fix_all_files()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشخيص مربعات النص في شاشة تسجيل الدخول
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# استيراد النافذة الأصلية
from modern_main_window import ModernLoginWindow

def diagnose_window():
    """تشخيص النافذة وفحص المكونات"""
    app = QApplication(sys.argv)
    
    # إنشاء النافذة
    window = ModernLoginWindow()
    
    print("🔍 تشخيص مكونات نافذة تسجيل الدخول:")
    print("=" * 50)
    
    # فحص وجود مربعات النص
    if hasattr(window, 'username_edit'):
        username_edit = window.username_edit
        print("✅ مربع اسم المستخدم موجود")
        print(f"   📏 الحجم: {username_edit.size()}")
        print(f"   👁️ مرئي: {username_edit.isVisible()}")
        print(f"   📝 النص: '{username_edit.text()}'")
        print(f"   🎨 الأنماط: {len(username_edit.styleSheet())} حرف")
    else:
        print("❌ مربع اسم المستخدم غير موجود!")
    
    if hasattr(window, 'password_edit'):
        password_edit = window.password_edit
        print("✅ مربع كلمة المرور موجود")
        print(f"   📏 الحجم: {password_edit.size()}")
        print(f"   👁️ مرئي: {password_edit.isVisible()}")
        print(f"   📝 النص: {'*' * len(password_edit.text())}")
        print(f"   🎨 الأنماط: {len(password_edit.styleSheet())} حرف")
    else:
        print("❌ مربع كلمة المرور غير موجود!")
    
    # فحص جميع مكونات QLineEdit في النافذة
    line_edits = window.findChildren(QLineEdit)
    print(f"\n📊 إجمالي مربعات النص الموجودة: {len(line_edits)}")
    
    for i, edit in enumerate(line_edits):
        print(f"   {i+1}. مربع نص - مرئي: {edit.isVisible()}, حجم: {edit.size()}")
    
    # عرض النافذة
    window.show()
    
    print("\n🚀 تم عرض النافذة - تحقق من ظهور مربعات النص!")
    print("👤 اسم المستخدم: admin")
    print("🔐 كلمة المرور: admin123")
    
    return app.exec_()

if __name__ == "__main__":
    diagnose_window()
// qmediaplayer.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGraphicsVideoItem /External/;
class QVideoWidget /External/;

class QMediaPlayer : public QMediaObject
{
%TypeHeaderCode
#include <qmediaplayer.h>
%End

public:
    enum State
    {
        StoppedState,
        PlayingState,
        PausedState,
    };

    enum MediaStatus
    {
        UnknownMediaStatus,
        NoMedia,
        LoadingMedia,
        LoadedMedia,
        StalledMedia,
        BufferingMedia,
        BufferedMedia,
        EndOfMedia,
        InvalidMedia,
    };

    enum Flag
    {
        LowLatency,
        StreamPlayback,
        VideoSurface,
    };

    typedef QFlags<QMediaPlayer::Flag> Flags;

    enum Error
    {
        NoError,
        ResourceError,
        FormatError,
        NetworkError,
        AccessDeniedError,
        ServiceMissingError,
    };

    QMediaPlayer(QObject *parent /TransferThis/ = 0, QMediaPlayer::Flags flags = QMediaPlayer::Flags());
    virtual ~QMediaPlayer();
    static QMultimedia::SupportEstimate hasSupport(const QString &mimeType, const QStringList &codecs = QStringList(), QMediaPlayer::Flags flags = QMediaPlayer::Flags());
    static QStringList supportedMimeTypes(QMediaPlayer::Flags flags = QMediaPlayer::Flags());
    void setVideoOutput(QVideoWidget *);
    void setVideoOutput(QGraphicsVideoItem *);
    void setVideoOutput(QAbstractVideoSurface *surface);
%If (Qt_5_15_0 -)
    void setVideoOutput(const QVector<QAbstractVideoSurface *> &surfaces);
%End
    QMediaContent media() const;
    const QIODevice *mediaStream() const;
    QMediaPlaylist *playlist() const;
    QMediaContent currentMedia() const;
    QMediaPlayer::State state() const;
    QMediaPlayer::MediaStatus mediaStatus() const;
    qint64 duration() const;
    qint64 position() const;
    int volume() const;
    bool isMuted() const;
    bool isAudioAvailable() const;
    bool isVideoAvailable() const;
    int bufferStatus() const;
    bool isSeekable() const;
    qreal playbackRate() const;
    QMediaPlayer::Error error() const;
    QString errorString() const;
    QNetworkConfiguration currentNetworkConfiguration() const;
    virtual QMultimedia::AvailabilityStatus availability() const;

public slots:
    void play();
    void pause();
    void stop();
    void setPosition(qint64 position);
    void setVolume(int volume);
    void setMuted(bool muted);
    void setPlaybackRate(qreal rate);
    void setMedia(const QMediaContent &media, QIODevice *stream = 0);
    void setPlaylist(QMediaPlaylist *playlist);
    void setNetworkConfigurations(const QList<QNetworkConfiguration> &configurations);

signals:
    void mediaChanged(const QMediaContent &media);
    void currentMediaChanged(const QMediaContent &media);
    void stateChanged(QMediaPlayer::State newState);
    void mediaStatusChanged(QMediaPlayer::MediaStatus status);
    void durationChanged(qint64 duration);
    void positionChanged(qint64 position);
    void volumeChanged(int volume);
    void mutedChanged(bool muted);
    void audioAvailableChanged(bool available);
    void videoAvailableChanged(bool videoAvailable);
    void bufferStatusChanged(int percentFilled);
    void seekableChanged(bool seekable);
    void playbackRateChanged(qreal rate);
    void error(QMediaPlayer::Error error);
    void networkConfigurationChanged(const QNetworkConfiguration &configuration);

public:
    virtual bool bind(QObject *);
    virtual void unbind(QObject *);
%If (Qt_5_6_0 -)
    QAudio::Role audioRole() const;
%End
%If (Qt_5_6_0 -)
    void setAudioRole(QAudio::Role audioRole);
%End
%If (Qt_5_6_0 -)
    QList<QAudio::Role> supportedAudioRoles() const;
%End

signals:
%If (Qt_5_6_0 -)
    void audioRoleChanged(QAudio::Role role);
%End

public:
%If (Qt_5_11_0 -)
    QString customAudioRole() const;
%End
%If (Qt_5_11_0 -)
    void setCustomAudioRole(const QString &audioRole);
%End
%If (Qt_5_11_0 -)
    QStringList supportedCustomAudioRoles() const;
%End

signals:
%If (Qt_5_11_0 -)
    void customAudioRoleChanged(const QString &role);
%End
};

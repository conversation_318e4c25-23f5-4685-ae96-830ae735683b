#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام التقارير المتقدم
Test Advanced Reports System
"""

import os
import sys
from datetime import datetime, timedelta
from advanced_reports import AdvancedReportsManager
from database import VacationDatabase

def setup_test_data():
    """إعداد بيانات تجريبية للاختبار"""
    print("🔧 إعداد بيانات تجريبية...")
    
    # إنشاء قاعدة بيانات تجريبية
    db = VacationDatabase('test_reports.db')
    
    # إضافة موظفين تجريبيين
    test_employees = [
        ('أحمد محمد علي', 'موظف', 30, '2024-01-01'),
        ('فاطمة عبد الله', 'مشرف', 35, '2024-01-01'),
        ('محمد عبد الرحمن', 'موظف', 30, '2024-01-01'),
        ('عائشة سعيد', 'مديرة', 40, '2024-01-01'),
        ('عبد الله أحمد', 'موظف', 30, '2024-01-01')
    ]
    
    for name, rank, days, date in test_employees:
        db.import_initial_balance_data(name, rank, days, date)
    
    # إضافة طلبات إجازة يومية تجريبية
    daily_requests = [
        ('أحمد محمد علي', 'EMP001', 'موظف', 'الإدارة', 'إجازة اعتيادية', '2024-07-01', 5),
        ('فاطمة عبد الله', 'EMP002', 'مشرف', 'الموارد البشرية', 'إجازة اعتيادية', '2024-07-10', 3),
        ('محمد عبد الرحمن', 'EMP003', 'موظف', 'المالية', 'إجازة مرضية', '2024-07-15', 2),
        ('أحمد محمد علي', 'EMP001', 'موظف', 'الإدارة', 'إجازة اعتيادية', '2024-08-01', 7),
        ('عائشة سعيد', 'EMP004', 'مديرة', 'الإدارة العليا', 'إجازة اعتيادية', '2024-08-05', 10)
    ]
    
    for request in daily_requests:
        db.add_daily_request(*request)
    
    # إضافة طلبات إجازة ساعية تجريبية
    hourly_requests = [
        ('أحمد محمد علي', '2024-07-20', 4),
        ('فاطمة عبد الله', '2024-07-25', 6),
        ('محمد عبد الرحمن', '2024-08-10', 3),
        ('أحمد محمد علي', '2024-08-15', 5),
        ('عبد الله أحمد', '2024-08-20', 2)
    ]
    
    for request in hourly_requests:
        db.add_hourly_request(*request)
    
    # إضافة إجازات مدرجة تجريبية
    added_vacations = [
        ('أحمد محمد علي', '2024-07-30', 2, 'إجازة إضافية للأداء المتميز'),
        ('فاطمة عبد الله', '2024-08-01', 3, 'إجازة تعويضية'),
        ('عائشة سعيد', '2024-08-10', 5, 'إجازة إدارية')
    ]
    
    for vacation in added_vacations:
        db.add_vacation(*vacation)
    
    print("✅ تم إعداد البيانات التجريبية بنجاح")
    return db

def test_monthly_reports():
    """اختبار التقارير الشهرية"""
    print("\n📊 اختبار التقارير الشهرية...")
    
    reports_manager = AdvancedReportsManager('test_reports.db')
    
    # تقرير شهر يوليو 2024
    success, message = reports_manager.generate_monthly_excel_report(2024, 7, 'تقرير_يوليو_2024_تجريبي.xlsx')
    print(f"تقرير يوليو 2024: {'✅' if success else '❌'} {message}")
    
    # تقرير شهر أغسطس 2024
    success, message = reports_manager.generate_monthly_excel_report(2024, 8, 'تقرير_أغسطس_2024_تجريبي.xlsx')
    print(f"تقرير أغسطس 2024: {'✅' if success else '❌'} {message}")

def test_yearly_reports():
    """اختبار التقارير السنوية"""
    print("\n📈 اختبار التقارير السنوية...")
    
    reports_manager = AdvancedReportsManager('test_reports.db')
    
    # تقرير سنة 2024
    success, message = reports_manager.generate_yearly_excel_report(2024, 'تقرير_2024_تجريبي.xlsx')
    print(f"تقرير 2024: {'✅' if success else '❌'} {message}")

def test_employee_reports():
    """اختبار تقارير الموظفين"""
    print("\n👤 اختبار تقارير الموظفين...")
    
    reports_manager = AdvancedReportsManager('test_reports.db')
    
    # تقرير أحمد محمد علي
    success, message = reports_manager.generate_employee_excel_report(
        'أحمد محمد علي', 
        '2024-07-01', 
        '2024-08-31',
        'تقرير_أحمد_محمد_علي_تجريبي.xlsx'
    )
    print(f"تقرير أحمد محمد علي: {'✅' if success else '❌'} {message}")
    
    # تقرير فاطمة عبد الله
    success, message = reports_manager.generate_employee_excel_report(
        'فاطمة عبد الله',
        output_path='تقرير_فاطمة_عبد_الله_تجريبي.xlsx'
    )
    print(f"تقرير فاطمة عبد الله: {'✅' if success else '❌'} {message}")

def test_custom_reports():
    """اختبار التقارير المخصصة"""
    print("\n🔧 اختبار التقارير المخصصة...")
    
    reports_manager = AdvancedReportsManager('test_reports.db')
    
    # تقرير مخصص للفترة من يوليو إلى أغسطس
    filters = {
        'start_date': '2024-07-01',
        'end_date': '2024-08-31',
        'include_daily': True,
        'include_hourly': True,
        'employee_names': ['أحمد محمد علي', 'فاطمة عبد الله']
    }
    
    success, message = reports_manager.generate_custom_report(
        filters, 
        'تقرير_مخصص_تجريبي.xlsx'
    )
    print(f"تقرير مخصص: {'✅' if success else '❌'} {message}")

def test_database_backup():
    """اختبار النسخة الاحتياطية"""
    print("\n💾 اختبار النسخة الاحتياطية...")
    
    reports_manager = AdvancedReportsManager('test_reports.db')
    
    success, message = reports_manager.export_database_backup('نسخة_احتياطية_تجريبية.xlsx')
    print(f"النسخة الاحتياطية: {'✅' if success else '❌'} {message}")

def test_database_functions():
    """اختبار وظائف قاعدة البيانات الجديدة"""
    print("\n🗄️ اختبار وظائف قاعدة البيانات الجديدة...")
    
    db = VacationDatabase('test_reports.db')
    
    # اختبار التقرير الشهري
    monthly_report = db.get_monthly_report(2024, 7)
    print(f"✅ تقرير شهري - طلبات يومية: {len(monthly_report['daily_requests'])}")
    print(f"✅ تقرير شهري - طلبات ساعية: {len(monthly_report['hourly_requests'])}")
    
    # اختبار التقرير السنوي
    yearly_report = db.get_yearly_report(2024)
    print(f"✅ تقرير سنوي - إحصائيات شهرية يومية: {len(yearly_report['monthly_daily_stats'])}")
    print(f"✅ تقرير سنوي - أكثر الموظفين استخداماً: {len(yearly_report['top_employees_daily'])}")
    
    # اختبار تقرير الموظف المفصل
    employee_report = db.get_employee_detailed_report('أحمد محمد علي')
    print(f"✅ تقرير موظف - طلبات يومية: {len(employee_report['daily_requests'])}")
    print(f"✅ تقرير موظف - طلبات ساعية: {len(employee_report['hourly_requests'])}")
    print(f"✅ تقرير موظف - رصيد صافي: {employee_report['balance']['net_balance']:.2f}")

def cleanup_test_files():
    """تنظيف ملفات الاختبار"""
    print("\n🧹 تنظيف ملفات الاختبار...")
    
    test_files = [
        'test_reports.db',
        'تقرير_يوليو_2024_تجريبي.xlsx',
        'تقرير_أغسطس_2024_تجريبي.xlsx',
        'تقرير_2024_تجريبي.xlsx',
        'تقرير_أحمد_محمد_علي_تجريبي.xlsx',
        'تقرير_فاطمة_عبد_الله_تجريبي.xlsx',
        'تقرير_مخصص_تجريبي.xlsx',
        'نسخة_احتياطية_تجريبية.xlsx'
    ]
    
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"✅ تم حذف {file}")
            except Exception as e:
                print(f"⚠️ لم يتم حذف {file}: {e}")

def main():
    """الدالة الرئيسية للاختبار"""
    print("🎯 اختبار نظام التقارير المتقدم")
    print("=" * 50)
    
    try:
        # إعداد البيانات التجريبية
        db = setup_test_data()
        
        # اختبار وظائف قاعدة البيانات
        test_database_functions()
        
        # اختبار التقارير المختلفة
        test_monthly_reports()
        test_yearly_reports()
        test_employee_reports()
        test_custom_reports()
        test_database_backup()
        
        print("\n🎉 تم الانتهاء من جميع الاختبارات بنجاح!")
        print("📁 تم إنشاء ملفات التقارير التجريبية")
        
        # سؤال المستخدم عن تنظيف الملفات
        cleanup = input("\nهل تريد حذف ملفات الاختبار؟ (y/n): ").lower()
        if cleanup in ['y', 'yes', 'نعم', 'ن']:
            cleanup_test_files()
        else:
            print("📁 تم الاحتفاظ بملفات الاختبار للمراجعة")
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✨ شكراً لاستخدام نظام التقارير المتقدم!")

if __name__ == "__main__":
    main()

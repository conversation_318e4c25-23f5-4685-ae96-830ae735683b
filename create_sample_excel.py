import pandas as pd
from datetime import datetime

# إنشاء بيانات نموذجية للرصيد الابتدائي
data = {
    'الاسم واللقب': [
        'أحمد محمد علي',
        'فاطمة عبد الله',
        'محمد عبد الرحمن',
        'عائشة سعيد',
        'عبد الله أحمد',
        'مريم محمد',
        'سعيد عبد الله',
        'خديجة أحمد',
        'يوسف محمد',
        'زينب عبد الله'
    ],
    'الرتبة': [
        'موظف',
        'مشرف',
        'موظف',
        'مديرة',
        'موظف',
        'مشرفة',
        'موظف',
        'موظفة',
        'مدير',
        'موظفة'
    ],
    'عدد الأيام': [
        30, 25, 30, 35, 30, 25, 30, 30, 40, 30
    ],
    'التاريخ': [
        '2024-01-01',
        '2024-01-01',
        '2024-01-01',
        '2024-01-01',
        '2024-01-01',
        '2024-01-01',
        '2024-01-01',
        '2024-01-01',
        '2024-01-01',
        '2024-01-01'
    ]
}

# إنشاء DataFrame
df = pd.DataFrame(data)

# حفظ البيانات في ملف Excel
df.to_excel('نموذج_الرصيد_الابتدائي.xlsx', index=False, engine='openpyxl')

print("تم إنشاء ملف Excel النموذجي بنجاح!")
print("اسم الملف: نموذج_الرصيد_الابتدائي.xlsx")
print("\nمحتوى الملف:")
print(df.to_string(index=False))
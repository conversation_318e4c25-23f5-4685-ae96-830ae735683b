#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام التقارير المتقدم
Run Advanced Reports System
"""

import sys
import os
import subprocess

def check_dependencies():
    """التحقق من المكتبات المطلوبة"""
    required_modules = ['pandas', 'openpyxl', 'reportlab']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} غير مثبت")
            missing_modules.append(module)
    
    return missing_modules

def install_missing_modules(missing_modules):
    """تثبيت المكتبات المفقودة"""
    if not missing_modules:
        return True
    
    print(f"\n🔧 تثبيت المكتبات المفقودة: {', '.join(missing_modules)}")
    
    try:
        for module in missing_modules:
            print(f"تثبيت {module}...")
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', module], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ تم تثبيت {module}")
            else:
                print(f"❌ فشل تثبيت {module}: {result.stderr}")
                return False
        return True
    except Exception as e:
        print(f"❌ خطأ في تثبيت المكتبات: {e}")
        return False

def display_main_menu():
    """عرض القائمة الرئيسية"""
    print("\n" + "="*60)
    print("🎯 نظام التقارير المتقدم - القائمة الرئيسية")
    print("="*60)
    print("1. تشغيل واجهة التقارير التفاعلية")
    print("2. تشغيل اختبار النظام")
    print("3. إنشاء تقرير سريع")
    print("4. عرض معلومات النظام")
    print("5. تحديث المكتبات")
    print("0. خروج")
    print("="*60)

def quick_report_menu():
    """قائمة التقارير السريعة"""
    print("\n📊 التقارير السريعة")
    print("-" * 30)
    print("1. تقرير الشهر الحالي")
    print("2. تقرير السنة الحالية")
    print("3. نسخة احتياطية سريعة")
    print("0. العودة للقائمة الرئيسية")

def create_quick_reports():
    """إنشاء تقارير سريعة"""
    try:
        from advanced_reports import AdvancedReportsManager
        from datetime import datetime
        
        reports_manager = AdvancedReportsManager()
        current_date = datetime.now()
        
        while True:
            quick_report_menu()
            choice = input("اختر نوع التقرير: ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                # تقرير الشهر الحالي
                print("⏳ إنشاء تقرير الشهر الحالي...")
                success, message = reports_manager.generate_monthly_excel_report(
                    current_date.year, current_date.month)
                print(f"{'✅' if success else '❌'} {message}")
                
            elif choice == '2':
                # تقرير السنة الحالية
                print("⏳ إنشاء تقرير السنة الحالية...")
                success, message = reports_manager.generate_yearly_excel_report(current_date.year)
                print(f"{'✅' if success else '❌'} {message}")
                
            elif choice == '3':
                # نسخة احتياطية سريعة
                print("⏳ إنشاء نسخة احتياطية...")
                success, message = reports_manager.export_database_backup()
                print(f"{'✅' if success else '❌'} {message}")
                
            else:
                print("⚠️ اختيار غير صحيح")
            
            input("\nاضغط Enter للمتابعة...")
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("💡 تأكد من تثبيت جميع المكتبات المطلوبة")
    except Exception as e:
        print(f"❌ خطأ في إنشاء التقارير: {e}")

def show_system_info():
    """عرض معلومات النظام"""
    print("\n📋 معلومات النظام")
    print("-" * 30)
    
    # معلومات Python
    print(f"🐍 Python: {sys.version}")
    
    # معلومات المكتبات
    print("\n📦 المكتبات المثبتة:")
    modules_to_check = ['pandas', 'openpyxl', 'reportlab', 'sqlite3']
    
    for module in modules_to_check:
        try:
            mod = __import__(module)
            version = getattr(mod, '__version__', 'غير محدد')
            print(f"  ✅ {module}: {version}")
        except ImportError:
            print(f"  ❌ {module}: غير مثبت")
    
    # معلومات الملفات
    print("\n📁 ملفات النظام:")
    system_files = [
        'database.py',
        'advanced_reports.py', 
        'reports_interface.py',
        'vacation_system.db'
    ]
    
    for file in system_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"  ✅ {file}: {size:,} بايت")
        else:
            print(f"  ❌ {file}: غير موجود")

def update_libraries():
    """تحديث المكتبات"""
    print("\n🔄 تحديث المكتبات")
    print("-" * 30)
    
    libraries = ['pandas', 'openpyxl', 'reportlab']
    
    for lib in libraries:
        print(f"تحديث {lib}...")
        try:
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', lib],
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ تم تحديث {lib}")
            else:
                print(f"⚠️ لم يتم تحديث {lib}")
        except Exception as e:
            print(f"❌ خطأ في تحديث {lib}: {e}")

def main():
    """الدالة الرئيسية"""
    print("🎯 نظام التقارير المتقدم لإدارة الإجازات")
    print("=" * 50)
    
    # التحقق من المكتبات
    print("🔍 التحقق من المكتبات المطلوبة:")
    missing_modules = check_dependencies()
    
    if missing_modules:
        print(f"\n⚠️ المكتبات المفقودة: {', '.join(missing_modules)}")
        install_choice = input("هل تريد تثبيت المكتبات المفقودة؟ (y/n): ").lower()
        
        if install_choice in ['y', 'yes', 'نعم', 'ن']:
            if not install_missing_modules(missing_modules):
                print("❌ فشل في تثبيت المكتبات. لا يمكن المتابعة.")
                return
        else:
            print("⚠️ لا يمكن تشغيل النظام بدون المكتبات المطلوبة")
            return
    
    # القائمة الرئيسية
    while True:
        try:
            display_main_menu()
            choice = input("اختر العملية المطلوبة: ").strip()
            
            if choice == '0':
                print("👋 شكراً لاستخدام نظام التقارير المتقدم!")
                break
                
            elif choice == '1':
                # تشغيل واجهة التقارير التفاعلية
                print("🚀 تشغيل واجهة التقارير التفاعلية...")
                try:
                    from reports_interface import ReportsInterface
                    interface = ReportsInterface()
                    interface.run()
                except ImportError as e:
                    print(f"❌ خطأ في تشغيل الواجهة: {e}")
                except Exception as e:
                    print(f"❌ خطأ غير متوقع: {e}")
                    
            elif choice == '2':
                # تشغيل اختبار النظام
                print("🧪 تشغيل اختبار النظام...")
                try:
                    subprocess.run([sys.executable, 'test_advanced_reports.py'])
                except Exception as e:
                    print(f"❌ خطأ في تشغيل الاختبار: {e}")
                    
            elif choice == '3':
                # إنشاء تقرير سريع
                create_quick_reports()
                
            elif choice == '4':
                # عرض معلومات النظام
                show_system_info()
                
            elif choice == '5':
                # تحديث المكتبات
                update_libraries()
                
            else:
                print("⚠️ اختيار غير صحيح")
            
            if choice != '1':  # لا نحتاج Enter إضافي بعد الواجهة التفاعلية
                input("\nاضغط Enter للمتابعة...")
                
        except KeyboardInterrupt:
            print("\n\n👋 تم إيقاف البرنامج")
            break
        except Exception as e:
            print(f"\n❌ خطأ غير متوقع: {e}")
            input("اضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()

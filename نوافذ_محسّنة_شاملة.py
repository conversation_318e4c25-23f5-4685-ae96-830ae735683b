#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نوافذ محسّنة شاملة لجميع وظائف النظام
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime, timedelta

# استيراد مديري الموظفين وأنواع الإجازات
try:
    from إدارة_الموظفين import get_employee_names, get_employee_manager
    from إدارة_أنواع_الإجازات import get_vacation_type_names, get_vacation_types_manager
except ImportError:
    def get_employee_names():
        return ["أحمد محمد علي", "فاطمة أحمد خالد", "محمد خالد عبدالله", "نور سالم محمد"]
    def get_employee_manager():
        return None
    def get_vacation_type_names():
        return ["إجازة اعتيادية", "إجازة مرضية", "إجازة طارئة", "إجازة بدون راتب"]
    def get_vacation_types_manager():
        return None

class OptimizedDialog(QDialog):
    """نافذة حوار محسّنة أساسية"""
    
    def __init__(self, title, width=600, height=450):
        super().__init__()
        self.setWindowTitle(title)
        self.setFixedSize(width, height)
        
        # تطبيق التوجيه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        self.move(x, y)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة النافذة"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # العنوان
        self.title_label = QLabel(self.windowTitle())
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #3498db, stop: 1 #2980b9);
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        main_layout.addWidget(self.title_label)
        
        # المحتوى الرئيسي
        self.content_frame = QFrame()
        self.content_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        self.content_layout = QVBoxLayout(self.content_frame)
        self.content_layout.setSpacing(10)
        main_layout.addWidget(self.content_frame)
        
        # أزرار التحكم
        self.buttons_layout = QHBoxLayout()
        self.buttons_layout.setSpacing(10)
        main_layout.addLayout(self.buttons_layout)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
    
    def add_button(self, text, style="primary", callback=None):
        """إضافة زر للنافذة"""
        button = QPushButton(text)
        
        if style == "primary":
            button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #28a745, stop: 1 #20c997);
                    border: none;
                    border-radius: 8px;
                    color: white;
                    font-weight: bold;
                    font-size: 14px;
                    padding: 10px 20px;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #34ce57, stop: 1 #28a745);
                }
            """)
        elif style == "secondary":
            button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #6c757d, stop: 1 #5a6268);
                    border: none;
                    border-radius: 8px;
                    color: white;
                    font-weight: bold;
                    font-size: 14px;
                    padding: 10px 20px;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #868e96, stop: 1 #6c757d);
                }
            """)
        elif style == "danger":
            button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #dc3545, stop: 1 #c82333);
                    border: none;
                    border-radius: 8px;
                    color: white;
                    font-weight: bold;
                    font-size: 14px;
                    padding: 10px 20px;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #e0506e, stop: 1 #dc3545);
                }
            """)
        
        if callback:
            button.clicked.connect(callback)
        
        self.buttons_layout.addWidget(button)
        return button
    
    def create_form_row(self, label_text, widget_or_layout):
        """إنشاء صف في النموذج"""
        row_layout = QHBoxLayout()
        
        label = QLabel(label_text)
        label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                min-width: 120px;
                max-width: 150px;
            }
        """)
        
        row_layout.addWidget(label)
        
        # التحقق من نوع المدخل
        if isinstance(widget_or_layout, QLayout):
            # إذا كان layout، أضفه مباشرة
            row_layout.addLayout(widget_or_layout)
        else:
            # إذا كان widget، طبق الأنماط وأضفه
            widget = widget_or_layout
            if isinstance(widget, (QLineEdit, QComboBox, QDateEdit, QTimeEdit)):
                # تطبيق التوجيه العربي على العناصر
                widget.setLayoutDirection(Qt.RightToLeft)
                widget.setStyleSheet("""
                    QLineEdit, QComboBox, QDateEdit, QTimeEdit {
                        font-family: "Sakkal Majalla", "Arial", sans-serif;
                        background: #f8f9fa;
                        border: 2px solid #dee2e6;
                        border-radius: 8px;
                        padding: 10px;
                        font-size: 14px;
                        font-weight: bold;
                        color: #000000;
                        min-height: 15px;
                    }
                    QLineEdit:focus, QComboBox:focus, QDateEdit:focus, QTimeEdit:focus {
                        border: 2px solid #007bff;
                        background: white;
                    }
                """)
            row_layout.addWidget(widget)
        
        self.content_layout.addLayout(row_layout)
        return row_layout

class DailyVacationWindow(OptimizedDialog):
    """نافذة طلب الإجازة اليومية المحسّنة"""
    
    def __init__(self):
        super().__init__("📝 طلب إجازة يومية", 550, 400)
        self.setup_content()
        
    def setup_content(self):
        """إعداد محتوى نافذة الإجازة اليومية"""
        
        # اسم الموظف (قائمة منسدلة)
        employee_layout = QHBoxLayout()
        
        self.employee_name = QComboBox()
        self.employee_name.setEditable(True)
        employee_names = get_employee_names()
        self.employee_name.addItems(employee_names)
        self.employee_name.setCurrentText("")
        self.employee_name.lineEdit().setPlaceholderText("اختر أو أدخل اسم الموظف...")
        
        # زر إدارة الموظفين
        manage_button = QPushButton("👥")
        manage_button.setToolTip("إدارة قائمة الموظفين")
        manage_button.setFixedSize(35, 35)
        manage_button.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        manage_button.clicked.connect(self.manage_employees)
        
        employee_layout.addWidget(self.employee_name)
        employee_layout.addWidget(manage_button)
        
        self.create_form_row("👤 اسم الموظف:", employee_layout)
        
        # تاريخ البداية
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate())
        self.start_date.setCalendarPopup(True)
        self.create_form_row("📅 تاريخ البداية:", self.start_date)
        
        # تاريخ النهاية
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate().addDays(1))
        self.end_date.setCalendarPopup(True)
        self.create_form_row("📅 تاريخ النهاية:", self.end_date)
        
        # نوع الإجازة (قائمة منسدلة قابلة للتعديل)
        vacation_type_layout = QHBoxLayout()
        
        self.vacation_type = QComboBox()
        self.vacation_type.setEditable(True)
        vacation_types = get_vacation_type_names()
        self.vacation_type.addItems(vacation_types)
        self.vacation_type.setCurrentText("")
        self.vacation_type.lineEdit().setPlaceholderText("اختر أو أدخل نوع الإجازة...")
        self.vacation_type.setLayoutDirection(Qt.RightToLeft)
        
        # زر إدارة أنواع الإجازات
        manage_types_button = QPushButton("📋")
        manage_types_button.setToolTip("إدارة أنواع الإجازات")
        manage_types_button.setFixedSize(35, 35)
        manage_types_button.setStyleSheet("""
            QPushButton {
                background: #8e44ad;
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background: #9b59b6;
            }
        """)
        manage_types_button.clicked.connect(self.manage_vacation_types)
        
        vacation_type_layout.addWidget(self.vacation_type)
        vacation_type_layout.addWidget(manage_types_button)
        
        self.create_form_row("📋 نوع الإجازة:", vacation_type_layout)
        
        # السبب
        self.reason = QLineEdit()
        self.reason.setPlaceholderText("اختياري - اذكر السبب...")
        self.create_form_row("📝 السبب:", self.reason)
        
        # الأزرار
        self.add_button("💾 حفظ الطلب", "primary", self.save_request)
        self.add_button("❌ إلغاء", "secondary", self.reject)
        
    def save_request(self):
        """حفظ طلب الإجازة"""
        employee_name = self.employee_name.currentText().strip()
        if not employee_name:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار أو إدخال اسم الموظف!")
            return
            
        QMessageBox.information(self, "✅ تم الحفظ", 
                              f"تم حفظ طلب الإجازة اليومية للموظف:\n{employee_name}")
        self.accept()
    
    def manage_employees(self):
        """فتح نافذة إدارة الموظفين"""
        try:
            from إدارة_الموظفين import EmployeeManagementWindow
            window = EmployeeManagementWindow()
            if window.exec_() == QDialog.Accepted:
                # تحديث قائمة الموظفين
                self.refresh_employee_list()
        except ImportError:
            QMessageBox.warning(self, "تحذير", "نافذة إدارة الموظفين غير متاحة!")
    
    def refresh_employee_list(self):
        """تحديث قائمة الموظفين"""
        current_text = self.employee_name.currentText()
        self.employee_name.clear()
        employee_names = get_employee_names()
        self.employee_name.addItems(employee_names)
        self.employee_name.setCurrentText(current_text)
    
    def manage_vacation_types(self):
        """فتح نافذة إدارة أنواع الإجازات"""
        try:
            from إدارة_أنواع_الإجازات import VacationTypesManagementWindow
            window = VacationTypesManagementWindow()
            if window.exec_() == QDialog.Accepted:
                # تحديث قائمة أنواع الإجازات
                self.refresh_vacation_types_list()
        except ImportError:
            QMessageBox.warning(self, "تحذير", "نافذة إدارة أنواع الإجازات غير متاحة!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إدارة أنواع الإجازات:\n{e}")
    
    def refresh_vacation_types_list(self):
        """تحديث قائمة أنواع الإجازات"""
        try:
            current_text = self.vacation_type.currentText()
            self.vacation_type.clear()
            vacation_types = get_vacation_type_names()
            self.vacation_type.addItems(vacation_types)
            self.vacation_type.setCurrentText(current_text)
        except Exception as e:
            print(f"خطأ في تحديث قائمة أنواع الإجازات: {e}")

class HourlyVacationWindow(OptimizedDialog):
    """نافذة طلب الإجازة الساعية المحسّنة"""
    
    def __init__(self):
        super().__init__("⏱️ طلب إجازة ساعية", 550, 400)
        self.setup_content()
        
    def setup_content(self):
        """إعداد محتوى نافذة الإجازة الساعية"""
        
        # اسم الموظف (قائمة منسدلة)
        employee_layout = QHBoxLayout()
        
        self.employee_name = QComboBox()
        self.employee_name.setEditable(True)
        employee_names = get_employee_names()
        self.employee_name.addItems(employee_names)
        self.employee_name.setCurrentText("")
        self.employee_name.lineEdit().setPlaceholderText("اختر أو أدخل اسم الموظف...")
        
        # زر إدارة الموظفين
        manage_button = QPushButton("👥")
        manage_button.setToolTip("إدارة قائمة الموظفين")
        manage_button.setFixedSize(35, 35)
        manage_button.setStyleSheet("""
            QPushButton {
                background: #17a2b8;
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background: #138496;
            }
        """)
        manage_button.clicked.connect(self.manage_employees)
        
        employee_layout.addWidget(self.employee_name)
        employee_layout.addWidget(manage_button)
        
        self.create_form_row("👤 اسم الموظف:", employee_layout)
        
        # التاريخ
        self.date = QDateEdit()
        self.date.setDate(QDate.currentDate())
        self.date.setCalendarPopup(True)
        self.create_form_row("📅 التاريخ:", self.date)
        
        # وقت البداية
        self.start_time = QTimeEdit()
        self.start_time.setTime(QTime(9, 0))
        self.create_form_row("🕘 وقت البداية:", self.start_time)
        
        # وقت النهاية
        self.end_time = QTimeEdit()
        self.end_time.setTime(QTime(11, 0))
        self.create_form_row("🕐 وقت النهاية:", self.end_time)
        
        # نوع الإجازة (قائمة منسدلة قابلة للتعديل)
        vacation_type_layout = QHBoxLayout()
        
        self.vacation_type = QComboBox()
        self.vacation_type.setEditable(True)
        vacation_types = get_vacation_type_names()
        self.vacation_type.addItems(vacation_types)
        # إضافة أنواع خاصة بالإجازات الساعية
        hourly_types = ["إجازة ساعية اعتيادية", "موعد طبي", "ظرف طارئ", "أمور شخصية"]
        for h_type in hourly_types:
            if h_type not in vacation_types:
                self.vacation_type.addItem(h_type)
        self.vacation_type.setCurrentText("")
        self.vacation_type.lineEdit().setPlaceholderText("اختر أو أدخل نوع الإجازة...")
        self.vacation_type.setLayoutDirection(Qt.RightToLeft)
        
        # زر إدارة أنواع الإجازات
        manage_types_button = QPushButton("📋")
        manage_types_button.setToolTip("إدارة أنواع الإجازات")
        manage_types_button.setFixedSize(35, 35)
        manage_types_button.setStyleSheet("""
            QPushButton {
                background: #8e44ad;
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background: #9b59b6;
            }
        """)
        manage_types_button.clicked.connect(self.manage_vacation_types)
        
        vacation_type_layout.addWidget(self.vacation_type)
        vacation_type_layout.addWidget(manage_types_button)
        
        self.create_form_row("📋 نوع الإجازة:", vacation_type_layout)
        
        # السبب
        self.reason = QLineEdit()
        self.reason.setPlaceholderText("اختياري - اذكر السبب...")
        self.create_form_row("📝 السبب:", self.reason)
        
        # الأزرار
        self.add_button("💾 حفظ الطلب", "primary", self.save_request)
        self.add_button("❌ إلغاء", "secondary", self.reject)
        
    def save_request(self):
        """حفظ طلب الإجازة"""
        employee_name = self.employee_name.currentText().strip()
        if not employee_name:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار أو إدخال اسم الموظف!")
            return
            
        start_time = self.start_time.time()
        end_time = self.end_time.time()
        
        if start_time >= end_time:
            QMessageBox.warning(self, "تحذير", "وقت البداية يجب أن يكون قبل وقت النهاية!")
            return
            
        QMessageBox.information(self, "✅ تم الحفظ", 
                              f"تم حفظ طلب الإجازة الساعية للموظف:\n{employee_name}")
        self.accept()
    
    def manage_employees(self):
        """فتح نافذة إدارة الموظفين"""
        try:
            from إدارة_الموظفين import EmployeeManagementWindow
            window = EmployeeManagementWindow()
            if window.exec_() == QDialog.Accepted:
                # تحديث قائمة الموظفين
                self.refresh_employee_list()
        except ImportError:
            QMessageBox.warning(self, "تحذير", "نافذة إدارة الموظفين غير متاحة!")
    
    def refresh_employee_list(self):
        """تحديث قائمة الموظفين"""
        current_text = self.employee_name.currentText()
        self.employee_name.clear()
        employee_names = get_employee_names()
        self.employee_name.addItems(employee_names)
        self.employee_name.setCurrentText(current_text)
    
    def manage_vacation_types(self):
        """فتح نافذة إدارة أنواع الإجازات"""
        try:
            from إدارة_أنواع_الإجازات import VacationTypesManagementWindow
            window = VacationTypesManagementWindow()
            if window.exec_() == QDialog.Accepted:
                # تحديث قائمة أنواع الإجازات
                self.refresh_vacation_types_list()
        except ImportError:
            QMessageBox.warning(self, "تحذير", "نافذة إدارة أنواع الإجازات غير متاحة!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إدارة أنواع الإجازات:\n{e}")
    
    def refresh_vacation_types_list(self):
        """تحديث قائمة أنواع الإجازات"""
        try:
            current_text = self.vacation_type.currentText()
            self.vacation_type.clear()
            vacation_types = get_vacation_type_names()
            self.vacation_type.addItems(vacation_types)
            # إضافة أنواع خاصة بالإجازات الساعية
            hourly_types = ["إجازة ساعية اعتيادية", "موعد طبي", "ظرف طارئ", "أمور شخصية"]
            for h_type in hourly_types:
                if h_type not in vacation_types:
                    self.vacation_type.addItem(h_type)
            self.vacation_type.setCurrentText(current_text)
        except Exception as e:
            print(f"خطأ في تحديث قائمة أنواع الإجازات: {e}")

class SearchEditWindow(OptimizedDialog):
    """نافذة البحث والتعديل المحسّنة"""
    
    def __init__(self):
        super().__init__("🔍 البحث والتعديل", 650, 500)
        self.setup_content()
        
    def setup_content(self):
        """إعداد محتوى نافذة البحث"""
        
        # البحث
        search_layout = QHBoxLayout()
        
        self.search_field = QLineEdit()
        self.search_field.setPlaceholderText("ابحث عن موظف، تاريخ، أو رقم الطلب...")
        self.search_field.setStyleSheet("""
            QLineEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                background: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
                color: #000000;
            }
            QLineEdit:focus {
                border: 2px solid #007bff;
                background: white;
            }
        """)
        
        search_button = QPushButton("🔍 بحث")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #007bff, stop: 1 #0056b3);
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #0069d9, stop: 1 #007bff);
            }
        """)
        search_button.clicked.connect(self.perform_search)
        
        search_layout.addWidget(QLabel("🔍 البحث:"))
        search_layout.addWidget(self.search_field)
        search_layout.addWidget(search_button)
        
        self.content_layout.addLayout(search_layout)
        
        # جدول النتائج
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(5)
        self.results_table.setHorizontalHeaderLabels([
            "اسم الموظف", "نوع الإجازة", "التاريخ", "المدة", "الحالة"
        ])
        self.results_table.horizontalHeader().setStretchLastSection(True)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                gridline-color: #dee2e6;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 13px;
            }
            QHeaderView::section {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 8px;
                font-weight: bold;
                color: #495057;
            }
        """)
        
        # إضافة بيانات تجريبية
        self.populate_sample_data()
        
        self.content_layout.addWidget(self.results_table)
        
        # الأزرار
        self.add_button("✏️ تعديل", "primary", self.edit_selected)
        self.add_button("🗑️ حذف", "danger", self.delete_selected)
        self.add_button("❌ إغلاق", "secondary", self.reject)
        
    def populate_sample_data(self):
        """إضافة بيانات تجريبية"""
        sample_data = [
            ["أحمد محمد", "إجازة اعتيادية", "2024-01-15", "3 أيام", "مقبولة"],
            ["فاطمة علي", "إجازة مرضية", "2024-01-10", "2 أيام", "مقبولة"],
            ["محمد خالد", "إجازة ساعية", "2024-01-12", "2 ساعة", "معلقة"],
            ["نور أحمد", "إجازة طارئة", "2024-01-08", "1 يوم", "مرفوضة"]
        ]
        
        self.results_table.setRowCount(len(sample_data))
        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                self.results_table.setItem(row, col, QTableWidgetItem(value))
    
    def perform_search(self):
        """تنفيذ البحث"""
        search_text = self.search_field.text().strip()
        if search_text:
            QMessageBox.information(self, "نتائج البحث", f"البحث عن: {search_text}\nتم العثور على 4 نتائج")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال نص للبحث!")
    
    def edit_selected(self):
        """تعديل العنصر المحدد"""
        current_row = self.results_table.currentRow()
        if current_row >= 0:
            employee_name = self.results_table.item(current_row, 0).text()
            QMessageBox.information(self, "تعديل", f"فتح نافذة تعديل للموظف: {employee_name}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد صف للتعديل!")
    
    def delete_selected(self):
        """حذف العنصر المحدد"""
        current_row = self.results_table.currentRow()
        if current_row >= 0:
            employee_name = self.results_table.item(current_row, 0).text()
            reply = QMessageBox.question(self, "تأكيد الحذف", 
                                       f"هل تريد حذف طلب الإجازة للموظف: {employee_name}؟")
            if reply == QMessageBox.Yes:
                self.results_table.removeRow(current_row)
                QMessageBox.information(self, "تم الحذف", "تم حذف الطلب بنجاح!")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد صف للحذف!")

class ReportsWindow(OptimizedDialog):
    """نافذة التقارير المحسّنة"""
    
    def __init__(self):
        super().__init__("📊 التقارير والإحصائيات", 600, 450)
        self.setup_content()
        
    def setup_content(self):
        """إعداد محتوى نافذة التقارير"""
        
        # نوع التقرير
        self.report_type = QComboBox()
        self.report_type.addItems([
            "تقرير الإجازات الشهري",
            "تقرير الإجازات السنوي",
            "تقرير الموظفين",
            "تقرير الإجازات المعلقة",
            "تقرير الإجازات المرفوضة"
        ])
        self.create_form_row("📋 نوع التقرير:", self.report_type)
        
        # الفترة من
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setCalendarPopup(True)
        self.create_form_row("📅 من تاريخ:", self.date_from)
        
        # الفترة إلى
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        self.create_form_row("📅 إلى تاريخ:", self.date_to)
        
        # الموظف
        self.employee_filter = QComboBox()
        self.employee_filter.addItems([
            "جميع الموظفين",
            "أحمد محمد",
            "فاطمة علي", 
            "محمد خالد",
            "نور أحمد"
        ])
        self.create_form_row("👤 الموظف:", self.employee_filter)
        
        # ملخص الإحصائيات
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #e3f2fd, stop: 1 #bbdefb);
                border: 2px solid #2196f3;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0px;
            }
        """)
        
        stats_layout = QGridLayout(stats_frame)
        
        stats_data = [
            ("📊 إجمالي الطلبات", "127", 0, 0),
            ("✅ المقبولة", "98", 0, 1),
            ("❌ المرفوضة", "15", 1, 0),
            ("⏳ المعلقة", "14", 1, 1)
        ]
        
        for text, value, row, col in stats_data:
            stat_widget = QLabel(f"{text}\n{value}")
            stat_widget.setAlignment(Qt.AlignCenter)
            stat_widget.setStyleSheet("""
                QLabel {
                    font-family: "Sakkal Majalla", "Arial", sans-serif;
                    font-size: 14px;
                    font-weight: bold;
                    color: #1976d2;
                    background: white;
                    border-radius: 8px;
                    padding: 10px;
                    margin: 2px;
                }
            """)
            stats_layout.addWidget(stat_widget, row, col)
        
        self.content_layout.addWidget(stats_frame)
        
        # الأزرار
        self.add_button("📄 إنشاء التقرير", "primary", self.generate_report)
        self.add_button("🖨️ طباعة", "secondary", self.print_report)
        self.add_button("❌ إغلاق", "secondary", self.reject)
        
    def generate_report(self):
        """إنشاء التقرير"""
        report_type = self.report_type.currentText()
        date_from = self.date_from.date().toString("dd/MM/yyyy")
        date_to = self.date_to.date().toString("dd/MM/yyyy")
        
        QMessageBox.information(self, "✅ تم إنشاء التقرير", 
                              f"تم إنشاء {report_type}\nللفترة من {date_from} إلى {date_to}")
    
    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "🖨️ طباعة", "سيتم فتح معاينة الطباعة...")

# الوظائف المساعدة
def show_daily_vacation_window():
    """عرض نافذة طلب الإجازة اليومية"""
    window = DailyVacationWindow()
    return window.exec_()

def show_hourly_vacation_window():
    """عرض نافذة طلب الإجازة الساعية"""
    window = HourlyVacationWindow()
    return window.exec_()

def show_search_edit_window():
    """عرض نافذة البحث والتعديل"""
    window = SearchEditWindow()
    return window.exec_()

def show_reports_window():
    """عرض نافذة التقارير"""
    window = ReportsWindow()
    return window.exec_()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    font = QFont("Sakkal Majalla", 10)
    app.setFont(font)
    
    # اختبار النوافذ
    print("🧪 اختبار النوافذ المحسّنة:")
    print("1. نافذة طلب الإجازة اليومية")
    
    show_daily_vacation_window()
    
    sys.exit()
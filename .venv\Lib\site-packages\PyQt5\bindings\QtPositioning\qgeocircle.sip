// qgeocircle.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QGeoCircle : public QGeoShape
{
%TypeHeaderCode
#include <qgeocircle.h>
%End

public:
    QGeoCircle();
    QGeoCircle(const QGeoCoordinate &center, qreal radius = -1.);
    QGeoCircle(const QGeoCircle &other);
    QGeoCircle(const QGeoShape &other);
    ~QGeoCircle();
    bool operator==(const QGeoCircle &other) const;
    bool operator!=(const QGeoCircle &other) const;
    void setCenter(const QGeoCoordinate &center);
    QGeoCoordinate center() const;
    void setRadius(qreal radius);
    qreal radius() const;
    void translate(double degreesLatitude, double degreesLongitude);
    QGeoCircle translated(double degreesLatitude, double degreesLongitude) const;
%If (Qt_5_5_0 -)
    QString toString() const;
%End
%If (Qt_5_9_0 -)
    void extendCircle(const QGeoCoordinate &coordinate);
%End
};

%End

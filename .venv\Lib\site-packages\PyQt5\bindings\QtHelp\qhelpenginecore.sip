// qhelpenginecore.sip generated by MetaSIP
//
// This file is part of the QtHelp Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QHelpEngineCore : public QObject
{
%TypeHeaderCode
#include <qhelpenginecore.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QHelpContentModel, &sipType_QHelpContentModel, -1, 1},
        {sipName_QHelpContentWidget, &sipType_QHelpContentWidget, -1, 2},
        {sipName_QHelpEngineCore, &sipType_QHelpEngineCore, 10, 3},
    #if QT_VERSION >= 0x050d00
        {sipName_QHelpFilterEngine, &sipType_QHelpFilterEngine, -1, 4},
    #else
        {0, 0, -1, 4},
    #endif
    #if QT_VERSION >= 0x050f00
        {sipName_QHelpFilterSettingsWidget, &sipType_QHelpFilterSettingsWidget, -1, 5},
    #else
        {0, 0, -1, 5},
    #endif
        {sipName_QHelpIndexModel, &sipType_QHelpIndexModel, -1, 6},
        {sipName_QHelpIndexWidget, &sipType_QHelpIndexWidget, -1, 7},
        {sipName_QHelpSearchEngine, &sipType_QHelpSearchEngine, -1, 8},
        {sipName_QHelpSearchQueryWidget, &sipType_QHelpSearchQueryWidget, -1, 9},
        {sipName_QHelpSearchResultWidget, &sipType_QHelpSearchResultWidget, -1, -1},
        {sipName_QHelpEngine, &sipType_QHelpEngine, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    QHelpEngineCore(const QString &collectionFile, QObject *parent /TransferThis/ = 0);
    virtual ~QHelpEngineCore();
    bool setupData();
    QString collectionFile() const;
    void setCollectionFile(const QString &fileName);
    bool copyCollectionFile(const QString &fileName);
    static QString namespaceName(const QString &documentationFileName);
    bool registerDocumentation(const QString &documentationFileName);
    bool unregisterDocumentation(const QString &namespaceName);
    QString documentationFileName(const QString &namespaceName);
    QStringList customFilters() const;
    bool removeCustomFilter(const QString &filterName);
    bool addCustomFilter(const QString &filterName, const QStringList &attributes);
    QStringList filterAttributes() const;
    QStringList filterAttributes(const QString &filterName) const;
    QString currentFilter() const;
    void setCurrentFilter(const QString &filterName);
    QStringList registeredDocumentations() const;
    QList<QStringList> filterAttributeSets(const QString &namespaceName) const;
    QList<QUrl> files(const QString namespaceName, const QStringList &filterAttributes, const QString &extensionFilter = QString());
    QUrl findFile(const QUrl &url) const;
    QByteArray fileData(const QUrl &url) const;
    QMap<QString, QUrl> linksForIdentifier(const QString &id) const;
%If (Qt_5_9_0 -)
    QMap<QString, QUrl> linksForKeyword(const QString &keyword) const;
%End
    bool removeCustomValue(const QString &key);
    QVariant customValue(const QString &key, const QVariant &defaultValue = QVariant()) const;
    bool setCustomValue(const QString &key, const QVariant &value);
    static QVariant metaData(const QString &documentationFileName, const QString &name);
    QString error() const;
    bool autoSaveFilter() const;
    void setAutoSaveFilter(bool save);

signals:
    void setupStarted();
    void setupFinished();
    void currentFilterChanged(const QString &newFilter);
    void warning(const QString &msg);
%If (Qt_5_4_0 -)
    void readersAboutToBeInvalidated();
%End

public:
%If (Qt_5_13_0 -)
    QHelpFilterEngine *filterEngine() const;
%End
%If (Qt_5_13_0 -)
    QList<QUrl> files(const QString namespaceName, const QString &filterName, const QString &extensionFilter = QString());
%End
%If (Qt_5_13_0 -)
    void setUsesFilterEngine(bool uses);
%End
%If (Qt_5_13_0 -)
    bool usesFilterEngine() const;
%End
%If (Qt_5_15_0 -)
    QList<QHelpLink> documentsForIdentifier(const QString &id) const;
%End
%If (Qt_5_15_0 -)
    QList<QHelpLink> documentsForIdentifier(const QString &id, const QString &filterName) const;
%End
%If (Qt_5_15_0 -)
    QList<QHelpLink> documentsForKeyword(const QString &keyword) const;
%End
%If (Qt_5_15_0 -)
    QList<QHelpLink> documentsForKeyword(const QString &keyword, const QString &filterName) const;
%End
};

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة الإجازات
===================

نظام شامل لإدارة إجازات الموظفين يتضمن:
- تسجيل الدخول الآمن
- استيراد الرصيد الابتدائي من Excel
- إدارة طلبات الإجازات اليومية والساعية
- إدراج الإجازات الإضافية
- توليد التقارير
- البحث والاستعلام
- تعديل وحذف الطلبات
- أرشفة البيانات

المتطلبات:
- Python 3.7+
- PyQt5
- pandas
- openpyxl
- sqlite3

الاستخدام:
python main.py

المطور: نظام إدارة الإجازات
التاريخ: 2024
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إضافة مسار المشروع لـ Python Path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from main_window import MainWindow
    from database import VacationDatabase
except ImportError as e:
    print(f"خطأ في استيراد المكتبات: {e}")
    sys.exit(1)

def check_dependencies():
    """التحقق من وجود المكتبات المطلوبة"""
    required_modules = ['PyQt5', 'pandas', 'openpyxl', 'sqlite3']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("المكتبات المطلوبة غير موجودة:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\nلتثبيت المكتبات المطلوبة، قم بتشغيل:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    # التحقق من المكتبات
    if not check_dependencies():
        return
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # إعداد الخط والاتجاه للنصوص العربية
    app.setLayoutDirection(Qt.RightToLeft)
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إعداد أيقونة التطبيق (إن وجدت)
    # app.setWindowIcon(QIcon('icon.png'))
    
    try:
        # إنشاء النافذة الرئيسية
        window = MainWindow()
        window.show()
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        # عرض رسالة خطأ في حالة حدوث مشكلة
        error_msg = f"حدث خطأ أثناء تشغيل البرنامج:\n{str(e)}"
        
        if 'app' in locals():
            QMessageBox.critical(None, "خطأ", error_msg)
        else:
            print(error_msg)
        
        sys.exit(1)

if __name__ == '__main__':
    main()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل للنظام المحدث مع إدارة الموظفين وأنواع الإجازات والتوجيه العربي
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class ComprehensiveTestWindow(QMainWindow):
    """نافذة الاختبار الشامل المحدث"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("🧪 اختبار شامل - النظام المحدث")
        self.setGeometry(50, 50, 800, 600)
        
        # تطبيق التوجيه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - 800) // 2
        y = (screen.height() - 600) // 2
        self.move(x, y)
        
        # الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(20)
        
        # العنوان
        title_label = QLabel("🧪 اختبار شامل للنظام المحدث")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 24px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #e67e22, stop: 1 #d35400);
                border-radius: 15px;
                padding: 25px;
                margin: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات النظام
        self.create_system_info(layout)
        
        # اختبارات المكونات
        self.create_component_tests(layout)
        
        # سجل الاختبارات
        self.create_test_log(layout)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
        
        # بدء الاختبارات التلقائية
        self.run_automatic_tests()
    
    def create_system_info(self, layout):
        """إنشاء معلومات النظام"""
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 12px;
                padding: 20px;
            }
        """)
        info_layout = QGridLayout(info_frame)
        info_layout.setSpacing(10)
        
        # معلومات النظام
        system_data = [
            ("📊 حالة النظام", self.check_system_status(), "#27ae60"),
            ("👥 عدد الموظفين", self.get_employees_count(), "#3498db"),
            ("📋 أنواع الإجازات", self.get_vacation_types_count(), "#8e44ad"),
            ("🔄 التوجيه العربي", "مفعل ✅", "#2c3e50"),
            ("💾 ملفات البيانات", self.check_data_files(), "#e67e22"),
            ("🎨 خط Sakkal Majalla", "مطبق ✅", "#27ae60")
        ]
        
        for i, (title, value, color) in enumerate(system_data):
            card = self.create_info_card(title, value, color)
            row = i // 3
            col = i % 3
            info_layout.addWidget(card, row, col)
        
        layout.addWidget(info_frame)
    
    def create_info_card(self, title, value, color):
        """إنشاء بطاقة معلومات"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 {color}, stop: 1 rgba(0,0,0,0.1));
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }}
        """)
        
        card_layout = QVBoxLayout(card)
        card_layout.setAlignment(Qt.AlignCenter)
        
        value_label = QLabel(str(value))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: white;
            }
        """)
        
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: white;
            }
        """)
        
        card_layout.addWidget(value_label)
        card_layout.addWidget(title_label)
        
        return card
    
    def create_component_tests(self, layout):
        """إنشاء اختبارات المكونات"""
        tests_frame = QFrame()
        tests_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 12px;
                padding: 20px;
            }
        """)
        tests_layout = QGridLayout(tests_frame)
        tests_layout.setSpacing(15)
        
        # اختبارات المكونات
        tests = [
            ("👥 اختبار إدارة الموظفين", "فتح نافذة إدارة الموظفين", "#3498db", self.test_employee_management),
            ("📋 اختبار إدارة أنواع الإجازات", "فتح نافذة إدارة أنواع الإجازات", "#8e44ad", self.test_vacation_types_management),
            ("📝 اختبار طلب إجازة يومية", "فتح نافذة طلب الإجازة اليومية", "#27ae60", self.test_daily_vacation),
            ("⏱️ اختبار طلب إجازة ساعية", "فتح نافذة طلب الإجازة الساعية", "#e67e22", self.test_hourly_vacation),
            ("🔍 اختبار البحث والتعديل", "فتح نافذة البحث والتعديل", "#9b59b6", self.test_search_edit),
            ("📊 اختبار التقارير", "فتح نافذة التقارير", "#34495e", self.test_reports)
        ]
        
        for i, (title, description, color, callback) in enumerate(tests):
            button = self.create_test_button(title, description, color, callback)
            row = i // 2
            col = i % 2
            tests_layout.addWidget(button, row, col)
        
        layout.addWidget(tests_frame)
    
    def create_test_button(self, title, description, color, callback):
        """إنشاء زر اختبار"""
        button = QPushButton()
        button.setFixedHeight(80)
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color}, stop: 1 rgba(0,0,0,0.1));
                border: none;
                border-radius: 10px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-weight: bold;
                text-align: center;
                padding: 15px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 rgba(255,255,255,0.2), stop: 1 {color});
            }}
            QPushButton:pressed {{
                background: {color};
            }}
        """)
        
        button.setText(f"{title}\n{description}")
        button.clicked.connect(callback)
        
        return button
    
    def create_test_log(self, layout):
        """إنشاء سجل الاختبارات"""
        log_frame = QFrame()
        log_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 12px;
                padding: 15px;
            }
        """)
        log_layout = QVBoxLayout(log_frame)
        
        log_title = QLabel("📝 سجل الاختبارات:")
        log_title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        log_layout.addWidget(log_title)
        
        self.test_log = QTextEdit()
        self.test_log.setMaximumHeight(150)
        self.test_log.setLayoutDirection(Qt.RightToLeft)
        self.test_log.setStyleSheet("""
            QTextEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 12px;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 5px;
                background: #f8f9fa;
            }
        """)
        log_layout.addWidget(self.test_log)
        
        layout.addWidget(log_frame)
    
    def log_test_result(self, test_name, result, details=""):
        """تسجيل نتيجة اختبار"""
        status = "✅ نجح" if result else "❌ فشل"
        timestamp = QDateTime.currentDateTime().toString("hh:mm:ss")
        log_entry = f"[{timestamp}] {test_name}: {status}"
        if details:
            log_entry += f" - {details}"
        
        self.test_log.append(log_entry)
        self.test_log.ensureCursorVisible()
    
    def run_automatic_tests(self):
        """تشغيل الاختبارات التلقائية"""
        self.log_test_result("بدء الاختبارات التلقائية", True)
        
        # اختبار تحميل الوحدات
        try:
            from إدارة_الموظفين import get_employee_names
            employee_count = len(get_employee_names())
            self.log_test_result("تحميل وحدة إدارة الموظفين", True, f"تم تحميل {employee_count} موظف")
        except Exception as e:
            self.log_test_result("تحميل وحدة إدارة الموظفين", False, str(e))
        
        try:
            from إدارة_أنواع_الإجازات import get_vacation_type_names
            types_count = len(get_vacation_type_names())
            self.log_test_result("تحميل وحدة أنواع الإجازات", True, f"تم تحميل {types_count} نوع")
        except Exception as e:
            self.log_test_result("تحميل وحدة أنواع الإجازات", False, str(e))
        
        # اختبار ملفات البيانات
        import os
        employees_file = os.path.exists("employees_data.json")
        vacation_types_file = os.path.exists("vacation_types_data.json")
        
        self.log_test_result("ملف بيانات الموظفين", employees_file)
        self.log_test_result("ملف بيانات أنواع الإجازات", vacation_types_file)
        
        # اختبار التوجيه العربي
        direction_test = self.layoutDirection() == Qt.RightToLeft
        self.log_test_result("التوجيه العربي", direction_test)
        
        self.log_test_result("انتهاء الاختبارات التلقائية", True, "جميع الاختبارات مكتملة")
    
    # الوظائف المساعدة
    def check_system_status(self):
        """فحص حالة النظام"""
        try:
            from إدارة_الموظفين import get_employee_names
            from إدارة_أنواع_الإجازات import get_vacation_type_names
            return "متصل ✅"
        except:
            return "غير متصل ❌"
    
    def get_employees_count(self):
        """الحصول على عدد الموظفين"""
        try:
            from إدارة_الموظفين import get_employee_names
            return str(len(get_employee_names()))
        except:
            return "غير متاح"
    
    def get_vacation_types_count(self):
        """الحصول على عدد أنواع الإجازات"""
        try:
            from إدارة_أنواع_الإجازات import get_vacation_type_names
            return str(len(get_vacation_type_names()))
        except:
            return "غير متاح"
    
    def check_data_files(self):
        """فحص ملفات البيانات"""
        import os
        employees_exists = os.path.exists("employees_data.json")
        types_exists = os.path.exists("vacation_types_data.json")
        
        if employees_exists and types_exists:
            return "موجودة ✅"
        elif employees_exists or types_exists:
            return "جزئية ⚠️"
        else:
            return "مفقودة ❌"
    
    # وظائف الاختبار
    def test_employee_management(self):
        """اختبار إدارة الموظفين"""
        try:
            from إدارة_الموظفين import EmployeeManagementWindow
            window = EmployeeManagementWindow()
            window.show()
            self.log_test_result("فتح نافذة إدارة الموظفين", True)
        except Exception as e:
            self.log_test_result("فتح نافذة إدارة الموظفين", False, str(e))
    
    def test_vacation_types_management(self):
        """اختبار إدارة أنواع الإجازات"""
        try:
            from إدارة_أنواع_الإجازات import VacationTypesManagementWindow
            window = VacationTypesManagementWindow()
            window.show()
            self.log_test_result("فتح نافذة إدارة أنواع الإجازات", True)
        except Exception as e:
            self.log_test_result("فتح نافذة إدارة أنواع الإجازات", False, str(e))
    
    def test_daily_vacation(self):
        """اختبار طلب الإجازة اليومية"""
        try:
            from نوافذ_محسّنة_شاملة import DailyVacationWindow
            window = DailyVacationWindow()
            window.show()
            self.log_test_result("فتح نافذة طلب الإجازة اليومية", True)
        except Exception as e:
            self.log_test_result("فتح نافذة طلب الإجازة اليومية", False, str(e))
    
    def test_hourly_vacation(self):
        """اختبار طلب الإجازة الساعية"""
        try:
            from نوافذ_محسّنة_شاملة import HourlyVacationWindow
            window = HourlyVacationWindow()
            window.show()
            self.log_test_result("فتح نافذة طلب الإجازة الساعية", True)
        except Exception as e:
            self.log_test_result("فتح نافذة طلب الإجازة الساعية", False, str(e))
    
    def test_search_edit(self):
        """اختبار البحث والتعديل"""
        try:
            from نوافذ_محسّنة_شاملة import SearchEditWindow
            window = SearchEditWindow()
            window.show()
            self.log_test_result("فتح نافذة البحث والتعديل", True)
        except Exception as e:
            self.log_test_result("فتح نافذة البحث والتعديل", False, str(e))
    
    def test_reports(self):
        """اختبار التقارير"""
        try:
            from نوافذ_محسّنة_شاملة import ReportsWindow
            window = ReportsWindow()
            window.show()
            self.log_test_result("فتح نافذة التقارير", True)
        except Exception as e:
            self.log_test_result("فتح نافذة التقارير", False, str(e))

def main():
    """الدالة الرئيسية للاختبار الشامل"""
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي والتوجيه
    font = QFont("Sakkal Majalla", 10)
    app.setFont(font)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🧪 بدء الاختبار الشامل للنظام المحدث...")
    print("=" * 60)
    
    window = ComprehensiveTestWindow()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
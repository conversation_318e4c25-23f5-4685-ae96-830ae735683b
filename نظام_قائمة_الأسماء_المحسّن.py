#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام محسّن مع قائمة منسدلة للأسماء وإدراج يدوي للأيام والساعات
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime
import json
import os

class EmployeeManager:
    """مدير الموظفين للقائمة المنسدلة"""
    
    def __init__(self):
        self.filename = "employees_list.json"
        self.employees = self.load_employees()
    
    def load_employees(self):
        """تحميل قائمة الموظفين"""
        try:
            if os.path.exists(self.filename):
                with open(self.filename, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    return data.get("employees", [])
            else:
                # قائمة افتراضية
                default_employees = [
                    "أحمد محمد علي",
                    "فاطمة أحمد خالد", 
                    "محمد خالد عبدالله",
                    "نور سالم محمد",
                    "علي حسن أحمد",
                    "مريم عبدالله سعد"
                ]
                self.save_employees(default_employees)
                return default_employees
        except:
            return ["موظف افتراضي"]
    
    def save_employees(self, employees_list):
        """حفظ قائمة الموظفين"""
        try:
            data = {"employees": employees_list}
            with open(self.filename, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الموظفين: {e}")
    
    def add_employee(self, name):
        """إضافة موظف جديد"""
        if name.strip() and name.strip() not in self.employees:
            self.employees.append(name.strip())
            self.save_employees(self.employees)
            return True
        return False
    
    def get_employees(self):
        """الحصول على قائمة الموظفين"""
        return self.employees

class OptimizedDialog(QDialog):
    """نافذة حوار محسّنة مع مقاسات مناسبة"""
    
    def __init__(self, title, width=380, height=300):
        super().__init__()
        self.setWindowTitle(title)
        self.setFixedSize(width, height)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        self.move(x, y)
        
        # مدير الموظفين
        self.employee_manager = EmployeeManager()
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد الواجهة الأساسية"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(10, 10, 10, 10)
        self.main_layout.setSpacing(6)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
    
    def create_form_row(self, label_text, widget):
        """إنشاء صف في النموذج"""
        row_widget = QWidget()
        row_layout = QHBoxLayout(row_widget)
        row_layout.setContentsMargins(2, 2, 2, 2)
        row_layout.setSpacing(6)
        
        # التسمية
        label = QLabel(label_text)
        label.setFixedWidth(80)
        label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 11px;
                font-weight: bold;
                color: #2c3e50;
                padding: 2px;
            }
        """)
        
        # تطبيق الأنماط على العناصر
        if isinstance(widget, (QLineEdit, QComboBox, QSpinBox)):
            widget.setLayoutDirection(Qt.RightToLeft)
            widget.setFixedHeight(24)
            
            if isinstance(widget, QComboBox):
                widget.setStyleSheet("""
                    QComboBox {
                        font-family: "Sakkal Majalla", "Arial", sans-serif;
                        background: white;
                        border: 1px solid #bdc3c7;
                        border-radius: 4px;
                        padding: 4px;
                        font-size: 10px;
                        color: #2c3e50;
                    }
                    QComboBox:focus {
                        border: 2px solid #3498db;
                        background: #f8f9fa;
                    }
                    QComboBox::drop-down {
                        border: none;
                        width: 20px;
                    }
                    QComboBox::down-arrow {
                        image: none;
                        border-left: 4px solid transparent;
                        border-right: 4px solid transparent;
                        border-top: 6px solid #7f8c8d;
                        margin-right: 4px;
                    }
                """)
            else:
                widget.setStyleSheet("""
                    QLineEdit, QSpinBox {
                        font-family: "Sakkal Majalla", "Arial", sans-serif;
                        background: white;
                        border: 1px solid #bdc3c7;
                        border-radius: 4px;
                        padding: 4px;
                        font-size: 10px;
                        color: #2c3e50;
                    }
                    QLineEdit:focus, QSpinBox:focus {
                        border: 2px solid #3498db;
                        background: #f8f9fa;
                    }
                """)
        
        row_layout.addWidget(label)
        row_layout.addWidget(widget)
        
        self.main_layout.addWidget(row_widget)
    
    def create_employee_row(self, label_text):
        """إنشاء صف خاص بالموظفين مع زر إضافة"""
        row_widget = QWidget()
        row_layout = QHBoxLayout(row_widget)
        row_layout.setContentsMargins(2, 2, 2, 2)
        row_layout.setSpacing(4)
        
        # التسمية
        label = QLabel(label_text)
        label.setFixedWidth(80)
        label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 11px;
                font-weight: bold;
                color: #2c3e50;
                padding: 2px;
            }
        """)
        
        # القائمة المنسدلة للموظفين
        self.employee_combo = QComboBox()
        self.employee_combo.setEditable(True)
        self.employee_combo.setLayoutDirection(Qt.RightToLeft)
        self.employee_combo.setFixedHeight(24)
        
        # إضافة الموظفين للقائمة
        employees = self.employee_manager.get_employees()
        self.employee_combo.addItems(employees)
        
        self.employee_combo.setStyleSheet("""
            QComboBox {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                background: white;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 4px;
                font-size: 10px;
                color: #2c3e50;
            }
            QComboBox:focus {
                border: 2px solid #3498db;
                background: #f8f9fa;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #7f8c8d;
                margin-right: 4px;
            }
        """)
        
        # زر إضافة موظف جديد
        add_btn = QPushButton("➕")
        add_btn.setFixedSize(24, 24)
        add_btn.setStyleSheet("""
            QPushButton {
                background: #27ae60;
                border: none;
                border-radius: 4px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2ecc71;
            }
        """)
        add_btn.clicked.connect(self.add_new_employee)
        
        row_layout.addWidget(label)
        row_layout.addWidget(self.employee_combo)
        row_layout.addWidget(add_btn)
        
        self.main_layout.addWidget(row_widget)
    
    def add_new_employee(self):
        """إضافة موظف جديد"""
        name, ok = QInputDialog.getText(self, "إضافة موظف", "اسم الموظف الجديد:")
        if ok and name.strip():
            if self.employee_manager.add_employee(name.strip()):
                self.employee_combo.addItem(name.strip())
                self.employee_combo.setCurrentText(name.strip())
                QMessageBox.information(self, "نجح", f"تم إضافة الموظف: {name}")
            else:
                QMessageBox.warning(self, "تحذير", "الموظف موجود مسبقاً أو الاسم فارغ!")
    
    def add_button(self, text, style_type, callback):
        """إضافة زر"""
        button = QPushButton(text)
        button.setFixedHeight(26)
        button.setFixedWidth(80)
        
        colors = {
            "primary": ("#27ae60", "#2ecc71"),
            "secondary": ("#95a5a6", "#bdc3c7"),
            "danger": ("#e74c3c", "#c0392b")
        }
        
        color, hover_color = colors.get(style_type, colors["secondary"])
        
        button.setStyleSheet(f"""
            QPushButton {{
                background: {color};
                border: none;
                border-radius: 4px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 10px;
                font-weight: bold;
                padding: 4px;
            }}
            QPushButton:hover {{
                background: {hover_color};
            }}
        """)
        
        button.clicked.connect(callback)
        
        # إضافة الزر في layout أفقي
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(button)
        button_layout.addStretch()
        
        self.main_layout.addLayout(button_layout)

class ImprovedDailyVacationWindow(OptimizedDialog):
    """نافذة طلب الإجازة اليومية مع قائمة منسدلة للأسماء"""
    
    def __init__(self):
        super().__init__("📅 إجازة يومية", 400, 320)
        self.setup_content()
        
    def setup_content(self):
        """إعداد محتوى النافذة"""
        
        # العنوان
        title_label = QLabel("📅 طلب إجازة يومية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 13px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #e74c3c, stop: 1 #c0392b);
                border-radius: 5px;
                padding: 8px;
                margin: 2px;
            }
        """)
        self.main_layout.addWidget(title_label)
        
        # اسم الموظف (قائمة منسدلة)
        self.create_employee_row("👤 الموظف:")
        
        # تاريخ البداية (إدراج يدوي)
        self.start_date = QLineEdit()
        self.start_date.setPlaceholderText("2024-12-01")
        self.create_form_row("📅 من:", self.start_date)
        
        # تاريخ النهاية (إدراج يدوي)
        self.end_date = QLineEdit()
        self.end_date.setPlaceholderText("2024-12-03")
        self.create_form_row("📅 إلى:", self.end_date)
        
        # عدد الأيام (إدراج يدوي)
        self.days_count = QSpinBox()
        self.days_count.setMinimum(1)
        self.days_count.setMaximum(365)
        self.days_count.setValue(1)
        self.days_count.setSuffix(" يوم")
        self.create_form_row("📊 الأيام:", self.days_count)
        
        # نوع الإجازة (إدراج يدوي)
        self.vacation_type = QLineEdit()
        self.vacation_type.setPlaceholderText("إجازة اعتيادية...")
        self.create_form_row("📋 النوع:", self.vacation_type)
        
        # السبب
        self.reason = QLineEdit()
        self.reason.setPlaceholderText("السبب...")
        self.create_form_row("📝 السبب:", self.reason)
        
        # ملاحظات
        self.notes = QLineEdit()
        self.notes.setPlaceholderText("ملاحظات...")
        self.create_form_row("📄 ملاحظة:", self.notes)
        
        # مساحة فارغة
        self.main_layout.addStretch()
        
        # معلومات إرشادية
        info_label = QLabel("💡 قائمة منسدلة للأسماء + إدراج يدوي للأيام")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 8px;
                color: #7f8c8d;
                background: #ecf0f1;
                border-radius: 3px;
                padding: 4px;
            }
        """)
        self.main_layout.addWidget(info_label)
        
        # الأزرار
        self.add_button("💾 حفظ", "primary", self.save_request)
        self.add_button("❌ إلغاء", "secondary", self.reject)
        
    def save_request(self):
        """حفظ طلب الإجازة"""
        # التحقق من البيانات
        employee_name = self.employee_combo.currentText().strip()
        if not employee_name:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار اسم الموظف!")
            return
            
        if not self.vacation_type.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال نوع الإجازة!")
            return
        
        # إعداد البيانات
        vacation_data = {
            "type": "daily",
            "employee_name": employee_name,
            "start_date": self.start_date.text().strip(),
            "end_date": self.end_date.text().strip(),
            "days_count_manual": self.days_count.value(),
            "vacation_type": self.vacation_type.text().strip(),
            "reason": self.reason.text().strip(),
            "notes": self.notes.text().strip(),
            "request_datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # حفظ البيانات
        self.save_data(vacation_data)
        
        # رسالة النجاح
        QMessageBox.information(self, "✅ تم الحفظ", 
            f"تم حفظ طلب الإجازة!\n\n"
            f"الموظف: {employee_name}\n"
            f"الأيام: {self.days_count.value()} يوم")
        
        self.accept()
    
    def save_data(self, data):
        """حفظ البيانات في ملف JSON"""
        try:
            filename = "vacation_with_dropdown.json"
            
            if os.path.exists(filename):
                with open(filename, "r", encoding="utf-8") as f:
                    requests = json.load(f)
            else:
                requests = []
            
            requests.append(data)
            
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(requests, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الحفظ:\n{e}")

class ImprovedHourlyVacationWindow(OptimizedDialog):
    """نافذة طلب الإجازة الساعية مع قائمة منسدلة للأسماء"""
    
    def __init__(self):
        super().__init__("⏱️ إجازة ساعية", 400, 300)
        self.setup_content()
        
    def setup_content(self):
        """إعداد محتوى النافذة"""
        
        # العنوان
        title_label = QLabel("⏱️ طلب إجازة ساعية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 13px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #f39c12, stop: 1 #e67e22);
                border-radius: 5px;
                padding: 8px;
                margin: 2px;
            }
        """)
        self.main_layout.addWidget(title_label)
        
        # اسم الموظف (قائمة منسدلة)
        self.create_employee_row("👤 الموظف:")
        
        # التاريخ (إدراج يدوي)
        self.date = QLineEdit()
        self.date.setPlaceholderText("2024-12-01")
        self.create_form_row("📅 التاريخ:", self.date)
        
        # وقت البداية (إدراج يدوي)
        self.start_time = QLineEdit()
        self.start_time.setPlaceholderText("09:00")
        self.create_form_row("🕘 من:", self.start_time)
        
        # وقت النهاية (إدراج يدوي)
        self.end_time = QLineEdit()
        self.end_time.setPlaceholderText("11:00")
        self.create_form_row("🕐 إلى:", self.end_time)
        
        # عدد الساعات (إدراج يدوي)
        self.hours_count = QSpinBox()
        self.hours_count.setMinimum(1)
        self.hours_count.setMaximum(24)
        self.hours_count.setValue(2)
        self.hours_count.setSuffix(" ساعة")
        self.create_form_row("⏰ الساعات:", self.hours_count)
        
        # نوع الإجازة (إدراج يدوي)
        self.vacation_type = QLineEdit()
        self.vacation_type.setPlaceholderText("موعد طبي...")
        self.create_form_row("📋 النوع:", self.vacation_type)
        
        # السبب
        self.reason = QLineEdit()
        self.reason.setPlaceholderText("السبب...")
        self.create_form_row("📝 السبب:", self.reason)
        
        # مساحة فارغة
        self.main_layout.addStretch()
        
        # معلومات إرشادية
        info_label = QLabel("💡 قائمة منسدلة للأسماء + إدراج يدوي للساعات")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 8px;
                color: #7f8c8d;
                background: #ecf0f1;
                border-radius: 3px;
                padding: 4px;
            }
        """)
        self.main_layout.addWidget(info_label)
        
        # الأزرار
        self.add_button("💾 حفظ", "primary", self.save_request)
        self.add_button("❌ إلغاء", "secondary", self.reject)
        
    def save_request(self):
        """حفظ طلب الإجازة الساعية"""
        # التحقق من البيانات
        employee_name = self.employee_combo.currentText().strip()
        if not employee_name:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار اسم الموظف!")
            return
            
        if not self.vacation_type.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال نوع الإجازة!")
            return
        
        # إعداد البيانات
        vacation_data = {
            "type": "hourly",
            "employee_name": employee_name,
            "date": self.date.text().strip(),
            "start_time": self.start_time.text().strip(),
            "end_time": self.end_time.text().strip(),
            "hours_count_manual": self.hours_count.value(),
            "vacation_type": self.vacation_type.text().strip(),
            "reason": self.reason.text().strip(),
            "request_datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # حفظ البيانات
        self.save_data(vacation_data)
        
        # رسالة النجاح
        QMessageBox.information(self, "✅ تم الحفظ", 
            f"تم حفظ طلب الإجازة!\n\n"
            f"الموظف: {employee_name}\n"
            f"الساعات: {self.hours_count.value()} ساعة")
        
        self.accept()
    
    def save_data(self, data):
        """حفظ البيانات في ملف JSON"""
        try:
            filename = "vacation_with_dropdown.json"
            
            if os.path.exists(filename):
                with open(filename, "r", encoding="utf-8") as f:
                    requests = json.load(f)
            else:
                requests = []
            
            requests.append(data)
            
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(requests, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الحفظ:\n{e}")

class SmallLoginWindow(QDialog):
    """نافذة تسجيل دخول صغيرة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔐 دخول")
        self.setFixedSize(220, 110)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - 220) // 2
        y = (screen.height() - 110) // 2
        self.move(x, y)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة تسجيل الدخول"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 6, 8, 6)
        layout.setSpacing(4)
        
        # العنوان
        title = QLabel("🔐 تسجيل الدخول")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 11px;
                font-weight: bold;
                color: white;
                background: #3498db;
                border-radius: 4px;
                padding: 5px;
            }
        """)
        layout.addWidget(title)
        
        # اسم المستخدم
        self.username = QLineEdit()
        self.username.setPlaceholderText("المستخدم")
        self.username.setText("admin")
        self.username.setFixedHeight(20)
        self.username.setLayoutDirection(Qt.RightToLeft)
        self.username.setStyleSheet("""
            QLineEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                padding: 3px;
                font-size: 9px;
                background: white;
            }
        """)
        layout.addWidget(self.username)
        
        # كلمة المرور
        self.password = QLineEdit()
        self.password.setPlaceholderText("كلمة المرور")
        self.password.setText("admin123")
        self.password.setEchoMode(QLineEdit.Password)
        self.password.setFixedHeight(20)
        self.password.setLayoutDirection(Qt.RightToLeft)
        self.password.setStyleSheet(self.username.styleSheet())
        layout.addWidget(self.password)
        
        # زر الدخول
        login_btn = QPushButton("✅ دخول")
        login_btn.setFixedHeight(22)
        login_btn.setStyleSheet("""
            QPushButton {
                background: #27ae60;
                border: none;
                border-radius: 3px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 9px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2ecc71;
            }
        """)
        login_btn.clicked.connect(self.login)
        layout.addWidget(login_btn)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }
        """)
    
    def login(self):
        """عملية تسجيل الدخول"""
        if self.username.text() == "admin" and self.password.text() == "admin123":
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ", "بيانات دخول غير صحيحة!")

class MainSystemWindow(QMainWindow):
    """النافذة الرئيسية للنظام"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🏢 نظام الإجازات - قائمة منسدلة للأسماء")
        self.setGeometry(100, 100, 350, 240)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - 350) // 2
        y = (screen.height() - 240) // 2
        self.move(x, y)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد الواجهة الرئيسية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)
        
        # العنوان
        title = QLabel("🏢 نظام إدارة الإجازات")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 15px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2c3e50, stop: 1 #34495e);
                border-radius: 6px;
                padding: 10px;
            }
        """)
        layout.addWidget(title)
        
        # وصف النظام
        desc = QLabel("👥 قائمة منسدلة للأسماء + إدراج يدوي للأيام/الساعات")
        desc.setAlignment(Qt.AlignCenter)
        desc.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 10px;
                color: #7f8c8d;
                padding: 4px;
            }
        """)
        layout.addWidget(desc)
        
        # الأزرار الرئيسية
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(6)
        
        # زر الإجازة اليومية
        daily_btn = self.create_main_button(
            "📅 إجازة يومية", 
            "#e74c3c",
            self.open_daily_vacation
        )
        buttons_layout.addWidget(daily_btn)
        
        # زر الإجازة الساعية
        hourly_btn = self.create_main_button(
            "⏱️ إجازة ساعية", 
            "#f39c12",
            self.open_hourly_vacation
        )
        buttons_layout.addWidget(hourly_btn)
        
        # زر عرض الطلبات
        view_btn = self.create_main_button(
            "📋 عرض الطلبات",
            "#27ae60", 
            self.view_requests
        )
        buttons_layout.addWidget(view_btn)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        # معلومات النظام
        info = QLabel("💡 مقاسات مناسبة + قائمة أسماء قابلة للتوسيع")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 8px;
                color: #95a5a6;
                background: #ecf0f1;
                border-radius: 3px;
                padding: 4px;
            }
        """)
        layout.addWidget(info)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }
        """)
    
    def create_main_button(self, title, color, callback):
        """إنشاء زر رئيسي"""
        button = QPushButton(title)
        button.setFixedHeight(35)
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color}, stop: 1 rgba(0,0,0,0.1));
                border: none;
                border-radius: 6px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 11px;
                font-weight: bold;
                text-align: center;
                padding: 6px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 rgba(255,255,255,0.2), stop: 1 {color});
            }}
        """)
        
        button.clicked.connect(callback)
        return button
    
    def open_daily_vacation(self):
        """فتح نافذة الإجازة اليومية"""
        window = ImprovedDailyVacationWindow()
        window.exec_()
    
    def open_hourly_vacation(self):
        """فتح نافذة الإجازة الساعية"""
        window = ImprovedHourlyVacationWindow()
        window.exec_()
    
    def view_requests(self):
        """عرض الطلبات المحفوظة"""
        try:
            filename = "vacation_with_dropdown.json"
            
            if not os.path.exists(filename):
                QMessageBox.information(self, "معلومات", "لا توجد طلبات محفوظة!")
                return
            
            with open(filename, "r", encoding="utf-8") as f:
                requests = json.load(f)
            
            if not requests:
                QMessageBox.information(self, "معلومات", "لا توجد طلبات محفوظة!")
                return
            
            # عرض الطلبات
            self.show_requests_window(requests)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في قراءة الطلبات:\n{e}")
    
    def show_requests_window(self, requests):
        """عرض نافذة الطلبات"""
        window = QDialog(self)
        window.setWindowTitle("📋 الطلبات المحفوظة")
        window.setFixedSize(420, 280)
        window.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout(window)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # العنوان
        title = QLabel(f"📋 إجمالي الطلبات: {len(requests)}")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 11px;
                font-weight: bold;
                color: white;
                background: #3498db;
                border-radius: 4px;
                padding: 6px;
            }
        """)
        layout.addWidget(title)
        
        # جدول الطلبات
        table = QTextEdit()
        table.setReadOnly(True)
        table.setLayoutDirection(Qt.RightToLeft)
        table.setStyleSheet("""
            QTextEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 9px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                padding: 6px;
                background: white;
            }
        """)
        
        # تنسيق النصوص
        content = ""
        for i, request in enumerate(requests, 1):
            content += f"الطلب رقم {i}:\n"
            content += f"👤 الموظف: {request.get('employee_name', 'غير محدد')}\n"
            
            if request.get('type') == 'hourly':
                content += f"⏱️ إجازة ساعية\n"
                content += f"📅 التاريخ: {request.get('date', 'غير محدد')}\n"
                content += f"🕘 من: {request.get('start_time', 'غير محدد')} إلى: {request.get('end_time', 'غير محدد')}\n"
                content += f"⏰ عدد الساعات: {request.get('hours_count_manual', 0)} ساعة (يدوي)\n"
            else:
                content += f"📅 إجازة يومية\n"
                content += f"📅 من: {request.get('start_date', 'غير محدد')} إلى: {request.get('end_date', 'غير محدد')}\n"
                content += f"📊 عدد الأيام: {request.get('days_count_manual', 0)} يوم (يدوي)\n"
            
            content += f"📋 نوع الإجازة: {request.get('vacation_type', 'غير محدد')}\n"
            content += f"📝 السبب: {request.get('reason', 'غير محدد')}\n"
            content += f"🕒 وقت الطلب: {request.get('request_datetime', 'غير محدد')}\n"
            content += "-" * 30 + "\n\n"
        
        table.setPlainText(content)
        layout.addWidget(table)
        
        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setFixedHeight(24)
        close_btn.setStyleSheet("""
            QPushButton {
                background: #95a5a6;
                border: none;
                border-radius: 3px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 9px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #7f8c8d;
            }
        """)
        close_btn.clicked.connect(window.close)
        layout.addWidget(close_btn)
        
        window.exec_()

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    font = QFont("Sakkal Majalla", 8)
    app.setFont(font)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🚀 نظام محسّن مع قائمة منسدلة للأسماء")
    print("=" * 40)
    print("✅ مقاسات مناسبة ومريحة")
    print("✅ قائمة منسدلة للموظفين مع:")
    print("   👥 أسماء افتراضية محملة")
    print("   ➕ إمكانية إضافة أسماء جديدة")
    print("   💾 حفظ الأسماء تلقائياً")
    print("✅ إدراج يدوي لعدد الأيام والساعات")
    print("✅ توجيه عربي كامل")
    print("=" * 40)
    
    # تسجيل الدخول
    login = SmallLoginWindow()
    if login.exec_() == QDialog.Accepted:
        print("✅ تم تسجيل الدخول بنجاح!")
        
        # النظام الرئيسي
        main_window = MainSystemWindow()
        main_window.show()
        
        return app.exec_()
    else:
        print("❌ تم إلغاء تسجيل الدخول")
        return 0

if __name__ == "__main__":
    sys.exit(main())#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام محسّن مع قائمة منسدلة للأسماء وإدراج يدوي للأيام والساعات
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime
import json
import os

class EmployeeManager:
    """مدير الموظفين للقائمة المنسدلة"""
    
    def __init__(self):
        self.filename = "employees_list.json"
        self.employees = self.load_employees()
    
    def load_employees(self):
        """تحميل قائمة الموظفين"""
        try:
            if os.path.exists(self.filename):
                with open(self.filename, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    return data.get("employees", [])
            else:
                # قائمة افتراضية
                default_employees = [
                    "أحمد محمد علي",
                    "فاطمة أحمد خالد", 
                    "محمد خالد عبدالله",
                    "نور سالم محمد",
                    "علي حسن أحمد",
                    "مريم عبدالله سعد"
                ]
                self.save_employees(default_employees)
                return default_employees
        except:
            return ["موظف افتراضي"]
    
    def save_employees(self, employees_list):
        """حفظ قائمة الموظفين"""
        try:
            data = {"employees": employees_list}
            with open(self.filename, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الموظفين: {e}")
    
    def add_employee(self, name):
        """إضافة موظف جديد"""
        if name.strip() and name.strip() not in self.employees:
            self.employees.append(name.strip())
            self.save_employees(self.employees)
            return True
        return False
    
    def get_employees(self):
        """الحصول على قائمة الموظفين"""
        return self.employees

class OptimizedDialog(QDialog):
    """نافذة حوار محسّنة مع مقاسات مناسبة"""
    
    def __init__(self, title, width=380, height=300):
        super().__init__()
        self.setWindowTitle(title)
        self.setFixedSize(width, height)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        self.move(x, y)
        
        # مدير الموظفين
        self.employee_manager = EmployeeManager()
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد الواجهة الأساسية"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(10, 10, 10, 10)
        self.main_layout.setSpacing(6)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
    
    def create_form_row(self, label_text, widget):
        """إنشاء صف في النموذج"""
        row_widget = QWidget()
        row_layout = QHBoxLayout(row_widget)
        row_layout.setContentsMargins(2, 2, 2, 2)
        row_layout.setSpacing(6)
        
        # التسمية
        label = QLabel(label_text)
        label.setFixedWidth(80)
        label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 11px;
                font-weight: bold;
                color: #2c3e50;
                padding: 2px;
            }
        """)
        
        # تطبيق الأنماط على العناصر
        if isinstance(widget, (QLineEdit, QComboBox, QSpinBox)):
            widget.setLayoutDirection(Qt.RightToLeft)
            widget.setFixedHeight(24)
            
            if isinstance(widget, QComboBox):
                widget.setStyleSheet("""
                    QComboBox {
                        font-family: "Sakkal Majalla", "Arial", sans-serif;
                        background: white;
                        border: 1px solid #bdc3c7;
                        border-radius: 4px;
                        padding: 4px;
                        font-size: 10px;
                        color: #2c3e50;
                    }
                    QComboBox:focus {
                        border: 2px solid #3498db;
                        background: #f8f9fa;
                    }
                    QComboBox::drop-down {
                        border: none;
                        width: 20px;
                    }
                    QComboBox::down-arrow {
                        image: none;
                        border-left: 4px solid transparent;
                        border-right: 4px solid transparent;
                        border-top: 6px solid #7f8c8d;
                        margin-right: 4px;
                    }
                """)
            else:
                widget.setStyleSheet("""
                    QLineEdit, QSpinBox {
                        font-family: "Sakkal Majalla", "Arial", sans-serif;
                        background: white;
                        border: 1px solid #bdc3c7;
                        border-radius: 4px;
                        padding: 4px;
                        font-size: 10px;
                        color: #2c3e50;
                    }
                    QLineEdit:focus, QSpinBox:focus {
                        border: 2px solid #3498db;
                        background: #f8f9fa;
                    }
                """)
        
        row_layout.addWidget(label)
        row_layout.addWidget(widget)
        
        self.main_layout.addWidget(row_widget)
    
    def create_employee_row(self, label_text):
        """إنشاء صف خاص بالموظفين مع زر إضافة"""
        row_widget = QWidget()
        row_layout = QHBoxLayout(row_widget)
        row_layout.setContentsMargins(2, 2, 2, 2)
        row_layout.setSpacing(4)
        
        # التسمية
        label = QLabel(label_text)
        label.setFixedWidth(80)
        label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 11px;
                font-weight: bold;
                color: #2c3e50;
                padding: 2px;
            }
        """)
        
        # القائمة المنسدلة للموظفين
        self.employee_combo = QComboBox()
        self.employee_combo.setEditable(True)
        self.employee_combo.setLayoutDirection(Qt.RightToLeft)
        self.employee_combo.setFixedHeight(24)
        
        # إضافة الموظفين للقائمة
        employees = self.employee_manager.get_employees()
        self.employee_combo.addItems(employees)
        
        self.employee_combo.setStyleSheet("""
            QComboBox {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                background: white;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 4px;
                font-size: 10px;
                color: #2c3e50;
            }
            QComboBox:focus {
                border: 2px solid #3498db;
                background: #f8f9fa;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #7f8c8d;
                margin-right: 4px;
            }
        """)
        
        # زر إضافة موظف جديد
        add_btn = QPushButton("➕")
        add_btn.setFixedSize(24, 24)
        add_btn.setStyleSheet("""
            QPushButton {
                background: #27ae60;
                border: none;
                border-radius: 4px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2ecc71;
            }
        """)
        add_btn.clicked.connect(self.add_new_employee)
        
        row_layout.addWidget(label)
        row_layout.addWidget(self.employee_combo)
        row_layout.addWidget(add_btn)
        
        self.main_layout.addWidget(row_widget)
    
    def add_new_employee(self):
        """إضافة موظف جديد"""
        name, ok = QInputDialog.getText(self, "إضافة موظف", "اسم الموظف الجديد:")
        if ok and name.strip():
            if self.employee_manager.add_employee(name.strip()):
                self.employee_combo.addItem(name.strip())
                self.employee_combo.setCurrentText(name.strip())
                QMessageBox.information(self, "نجح", f"تم إضافة الموظف: {name}")
            else:
                QMessageBox.warning(self, "تحذير", "الموظف موجود مسبقاً أو الاسم فارغ!")
    
    def add_button(self, text, style_type, callback):
        """إضافة زر"""
        button = QPushButton(text)
        button.setFixedHeight(26)
        button.setFixedWidth(80)
        
        colors = {
            "primary": ("#27ae60", "#2ecc71"),
            "secondary": ("#95a5a6", "#bdc3c7"),
            "danger": ("#e74c3c", "#c0392b")
        }
        
        color, hover_color = colors.get(style_type, colors["secondary"])
        
        button.setStyleSheet(f"""
            QPushButton {{
                background: {color};
                border: none;
                border-radius: 4px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 10px;
                font-weight: bold;
                padding: 4px;
            }}
            QPushButton:hover {{
                background: {hover_color};
            }}
        """)
        
        button.clicked.connect(callback)
        
        # إضافة الزر في layout أفقي
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(button)
        button_layout.addStretch()
        
        self.main_layout.addLayout(button_layout)

class ImprovedDailyVacationWindow(OptimizedDialog):
    """نافذة طلب الإجازة اليومية مع قائمة منسدلة للأسماء"""
    
    def __init__(self):
        super().__init__("📅 إجازة يومية", 400, 320)
        self.setup_content()
        
    def setup_content(self):
        """إعداد محتوى النافذة"""
        
        # العنوان
        title_label = QLabel("📅 طلب إجازة يومية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 13px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #e74c3c, stop: 1 #c0392b);
                border-radius: 5px;
                padding: 8px;
                margin: 2px;
            }
        """)
        self.main_layout.addWidget(title_label)
        
        # اسم الموظف (قائمة منسدلة)
        self.create_employee_row("👤 الموظف:")
        
        # تاريخ البداية (إدراج يدوي)
        self.start_date = QLineEdit()
        self.start_date.setPlaceholderText("2024-12-01")
        self.create_form_row("📅 من:", self.start_date)
        
        # تاريخ النهاية (إدراج يدوي)
        self.end_date = QLineEdit()
        self.end_date.setPlaceholderText("2024-12-03")
        self.create_form_row("📅 إلى:", self.end_date)
        
        # عدد الأيام (إدراج يدوي)
        self.days_count = QSpinBox()
        self.days_count.setMinimum(1)
        self.days_count.setMaximum(365)
        self.days_count.setValue(1)
        self.days_count.setSuffix(" يوم")
        self.create_form_row("📊 الأيام:", self.days_count)
        
        # نوع الإجازة (إدراج يدوي)
        self.vacation_type = QLineEdit()
        self.vacation_type.setPlaceholderText("إجازة اعتيادية...")
        self.create_form_row("📋 النوع:", self.vacation_type)
        
        # السبب
        self.reason = QLineEdit()
        self.reason.setPlaceholderText("السبب...")
        self.create_form_row("📝 السبب:", self.reason)
        
        # ملاحظات
        self.notes = QLineEdit()
        self.notes.setPlaceholderText("ملاحظات...")
        self.create_form_row("📄 ملاحظة:", self.notes)
        
        # مساحة فارغة
        self.main_layout.addStretch()
        
        # معلومات إرشادية
        info_label = QLabel("💡 قائمة منسدلة للأسماء + إدراج يدوي للأيام")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 8px;
                color: #7f8c8d;
                background: #ecf0f1;
                border-radius: 3px;
                padding: 4px;
            }
        """)
        self.main_layout.addWidget(info_label)
        
        # الأزرار
        self.add_button("💾 حفظ", "primary", self.save_request)
        self.add_button("❌ إلغاء", "secondary", self.reject)
        
    def save_request(self):
        """حفظ طلب الإجازة"""
        # التحقق من البيانات
        employee_name = self.employee_combo.currentText().strip()
        if not employee_name:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار اسم الموظف!")
            return
            
        if not self.vacation_type.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال نوع الإجازة!")
            return
        
        # إعداد البيانات
        vacation_data = {
            "type": "daily",
            "employee_name": employee_name,
            "start_date": self.start_date.text().strip(),
            "end_date": self.end_date.text().strip(),
            "days_count_manual": self.days_count.value(),
            "vacation_type": self.vacation_type.text().strip(),
            "reason": self.reason.text().strip(),
            "notes": self.notes.text().strip(),
            "request_datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # حفظ البيانات
        self.save_data(vacation_data)
        
        # رسالة النجاح
        QMessageBox.information(self, "✅ تم الحفظ", 
            f"تم حفظ طلب الإجازة!\n\n"
            f"الموظف: {employee_name}\n"
            f"الأيام: {self.days_count.value()} يوم")
        
        self.accept()
    
    def save_data(self, data):
        """حفظ البيانات في ملف JSON"""
        try:
            filename = "vacation_with_dropdown.json"
            
            if os.path.exists(filename):
                with open(filename, "r", encoding="utf-8") as f:
                    requests = json.load(f)
            else:
                requests = []
            
            requests.append(data)
            
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(requests, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الحفظ:\n{e}")

class ImprovedHourlyVacationWindow(OptimizedDialog):
    """نافذة طلب الإجازة الساعية مع قائمة منسدلة للأسماء"""
    
    def __init__(self):
        super().__init__("⏱️ إجازة ساعية", 400, 300)
        self.setup_content()
        
    def setup_content(self):
        """إعداد محتوى النافذة"""
        
        # العنوان
        title_label = QLabel("⏱️ طلب إجازة ساعية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 13px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #f39c12, stop: 1 #e67e22);
                border-radius: 5px;
                padding: 8px;
                margin: 2px;
            }
        """)
        self.main_layout.addWidget(title_label)
        
        # اسم الموظف (قائمة منسدلة)
        self.create_employee_row("👤 الموظف:")
        
        # التاريخ (إدراج يدوي)
        self.date = QLineEdit()
        self.date.setPlaceholderText("2024-12-01")
        self.create_form_row("📅 التاريخ:", self.date)
        
        # وقت البداية (إدراج يدوي)
        self.start_time = QLineEdit()
        self.start_time.setPlaceholderText("09:00")
        self.create_form_row("🕘 من:", self.start_time)
        
        # وقت النهاية (إدراج يدوي)
        self.end_time = QLineEdit()
        self.end_time.setPlaceholderText("11:00")
        self.create_form_row("🕐 إلى:", self.end_time)
        
        # عدد الساعات (إدراج يدوي)
        self.hours_count = QSpinBox()
        self.hours_count.setMinimum(1)
        self.hours_count.setMaximum(24)
        self.hours_count.setValue(2)
        self.hours_count.setSuffix(" ساعة")
        self.create_form_row("⏰ الساعات:", self.hours_count)
        
        # نوع الإجازة (إدراج يدوي)
        self.vacation_type = QLineEdit()
        self.vacation_type.setPlaceholderText("موعد طبي...")
        self.create_form_row("📋 النوع:", self.vacation_type)
        
        # السبب
        self.reason = QLineEdit()
        self.reason.setPlaceholderText("السبب...")
        self.create_form_row("📝 السبب:", self.reason)
        
        # مساحة فارغة
        self.main_layout.addStretch()
        
        # معلومات إرشادية
        info_label = QLabel("💡 قائمة منسدلة للأسماء + إدراج يدوي للساعات")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 8px;
                color: #7f8c8d;
                background: #ecf0f1;
                border-radius: 3px;
                padding: 4px;
            }
        """)
        self.main_layout.addWidget(info_label)
        
        # الأزرار
        self.add_button("💾 حفظ", "primary", self.save_request)
        self.add_button("❌ إلغاء", "secondary", self.reject)
        
    def save_request(self):
        """حفظ طلب الإجازة الساعية"""
        # التحقق من البيانات
        employee_name = self.employee_combo.currentText().strip()
        if not employee_name:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار اسم الموظف!")
            return
            
        if not self.vacation_type.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال نوع الإجازة!")
            return
        
        # إعداد البيانات
        vacation_data = {
            "type": "hourly",
            "employee_name": employee_name,
            "date": self.date.text().strip(),
            "start_time": self.start_time.text().strip(),
            "end_time": self.end_time.text().strip(),
            "hours_count_manual": self.hours_count.value(),
            "vacation_type": self.vacation_type.text().strip(),
            "reason": self.reason.text().strip(),
            "request_datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # حفظ البيانات
        self.save_data(vacation_data)
        
        # رسالة النجاح
        QMessageBox.information(self, "✅ تم الحفظ", 
            f"تم حفظ طلب الإجازة!\n\n"
            f"الموظف: {employee_name}\n"
            f"الساعات: {self.hours_count.value()} ساعة")
        
        self.accept()
    
    def save_data(self, data):
        """حفظ البيانات في ملف JSON"""
        try:
            filename = "vacation_with_dropdown.json"
            
            if os.path.exists(filename):
                with open(filename, "r", encoding="utf-8") as f:
                    requests = json.load(f)
            else:
                requests = []
            
            requests.append(data)
            
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(requests, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الحفظ:\n{e}")

class SmallLoginWindow(QDialog):
    """نافذة تسجيل دخول صغيرة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔐 دخول")
        self.setFixedSize(220, 110)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - 220) // 2
        y = (screen.height() - 110) // 2
        self.move(x, y)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة تسجيل الدخول"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 6, 8, 6)
        layout.setSpacing(4)
        
        # العنوان
        title = QLabel("🔐 تسجيل الدخول")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 11px;
                font-weight: bold;
                color: white;
                background: #3498db;
                border-radius: 4px;
                padding: 5px;
            }
        """)
        layout.addWidget(title)
        
        # اسم المستخدم
        self.username = QLineEdit()
        self.username.setPlaceholderText("المستخدم")
        self.username.setText("admin")
        self.username.setFixedHeight(20)
        self.username.setLayoutDirection(Qt.RightToLeft)
        self.username.setStyleSheet("""
            QLineEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                padding: 3px;
                font-size: 9px;
                background: white;
            }
        """)
        layout.addWidget(self.username)
        
        # كلمة المرور
        self.password = QLineEdit()
        self.password.setPlaceholderText("كلمة المرور")
        self.password.setText("admin123")
        self.password.setEchoMode(QLineEdit.Password)
        self.password.setFixedHeight(20)
        self.password.setLayoutDirection(Qt.RightToLeft)
        self.password.setStyleSheet(self.username.styleSheet())
        layout.addWidget(self.password)
        
        # زر الدخول
        login_btn = QPushButton("✅ دخول")
        login_btn.setFixedHeight(22)
        login_btn.setStyleSheet("""
            QPushButton {
                background: #27ae60;
                border: none;
                border-radius: 3px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 9px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2ecc71;
            }
        """)
        login_btn.clicked.connect(self.login)
        layout.addWidget(login_btn)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }
        """)
    
    def login(self):
        """عملية تسجيل الدخول"""
        if self.username.text() == "admin" and self.password.text() == "admin123":
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ", "بيانات دخول غير صحيحة!")

class MainSystemWindow(QMainWindow):
    """النافذة الرئيسية للنظام"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🏢 نظام الإجازات - قائمة منسدلة للأسماء")
        self.setGeometry(100, 100, 350, 240)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - 350) // 2
        y = (screen.height() - 240) // 2
        self.move(x, y)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد الواجهة الرئيسية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)
        
        # العنوان
        title = QLabel("🏢 نظام إدارة الإجازات")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 15px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2c3e50, stop: 1 #34495e);
                border-radius: 6px;
                padding: 10px;
            }
        """)
        layout.addWidget(title)
        
        # وصف النظام
        desc = QLabel("👥 قائمة منسدلة للأسماء + إدراج يدوي للأيام/الساعات")
        desc.setAlignment(Qt.AlignCenter)
        desc.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 10px;
                color: #7f8c8d;
                padding: 4px;
            }
        """)
        layout.addWidget(desc)
        
        # الأزرار الرئيسية
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(6)
        
        # زر الإجازة اليومية
        daily_btn = self.create_main_button(
            "📅 إجازة يومية", 
            "#e74c3c",
            self.open_daily_vacation
        )
        buttons_layout.addWidget(daily_btn)
        
        # زر الإجازة الساعية
        hourly_btn = self.create_main_button(
            "⏱️ إجازة ساعية", 
            "#f39c12",
            self.open_hourly_vacation
        )
        buttons_layout.addWidget(hourly_btn)
        
        # زر عرض الطلبات
        view_btn = self.create_main_button(
            "📋 عرض الطلبات",
            "#27ae60", 
            self.view_requests
        )
        buttons_layout.addWidget(view_btn)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        # معلومات النظام
        info = QLabel("💡 مقاسات مناسبة + قائمة أسماء قابلة للتوسيع")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 8px;
                color: #95a5a6;
                background: #ecf0f1;
                border-radius: 3px;
                padding: 4px;
            }
        """)
        layout.addWidget(info)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }
        """)
    
    def create_main_button(self, title, color, callback):
        """إنشاء زر رئيسي"""
        button = QPushButton(title)
        button.setFixedHeight(35)
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color}, stop: 1 rgba(0,0,0,0.1));
                border: none;
                border-radius: 6px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 11px;
                font-weight: bold;
                text-align: center;
                padding: 6px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 rgba(255,255,255,0.2), stop: 1 {color});
            }}
        """)
        
        button.clicked.connect(callback)
        return button
    
    def open_daily_vacation(self):
        """فتح نافذة الإجازة اليومية"""
        window = ImprovedDailyVacationWindow()
        window.exec_()
    
    def open_hourly_vacation(self):
        """فتح نافذة الإجازة الساعية"""
        window = ImprovedHourlyVacationWindow()
        window.exec_()
    
    def view_requests(self):
        """عرض الطلبات المحفوظة"""
        try:
            filename = "vacation_with_dropdown.json"
            
            if not os.path.exists(filename):
                QMessageBox.information(self, "معلومات", "لا توجد طلبات محفوظة!")
                return
            
            with open(filename, "r", encoding="utf-8") as f:
                requests = json.load(f)
            
            if not requests:
                QMessageBox.information(self, "معلومات", "لا توجد طلبات محفوظة!")
                return
            
            # عرض الطلبات
            self.show_requests_window(requests)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في قراءة الطلبات:\n{e}")
    
    def show_requests_window(self, requests):
        """عرض نافذة الطلبات"""
        window = QDialog(self)
        window.setWindowTitle("📋 الطلبات المحفوظة")
        window.setFixedSize(420, 280)
        window.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout(window)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # العنوان
        title = QLabel(f"📋 إجمالي الطلبات: {len(requests)}")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 11px;
                font-weight: bold;
                color: white;
                background: #3498db;
                border-radius: 4px;
                padding: 6px;
            }
        """)
        layout.addWidget(title)
        
        # جدول الطلبات
        table = QTextEdit()
        table.setReadOnly(True)
        table.setLayoutDirection(Qt.RightToLeft)
        table.setStyleSheet("""
            QTextEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 9px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                padding: 6px;
                background: white;
            }
        """)
        
        # تنسيق النصوص
        content = ""
        for i, request in enumerate(requests, 1):
            content += f"الطلب رقم {i}:\n"
            content += f"👤 الموظف: {request.get('employee_name', 'غير محدد')}\n"
            
            if request.get('type') == 'hourly':
                content += f"⏱️ إجازة ساعية\n"
                content += f"📅 التاريخ: {request.get('date', 'غير محدد')}\n"
                content += f"🕘 من: {request.get('start_time', 'غير محدد')} إلى: {request.get('end_time', 'غير محدد')}\n"
                content += f"⏰ عدد الساعات: {request.get('hours_count_manual', 0)} ساعة (يدوي)\n"
            else:
                content += f"📅 إجازة يومية\n"
                content += f"📅 من: {request.get('start_date', 'غير محدد')} إلى: {request.get('end_date', 'غير محدد')}\n"
                content += f"📊 عدد الأيام: {request.get('days_count_manual', 0)} يوم (يدوي)\n"
            
            content += f"📋 نوع الإجازة: {request.get('vacation_type', 'غير محدد')}\n"
            content += f"📝 السبب: {request.get('reason', 'غير محدد')}\n"
            content += f"🕒 وقت الطلب: {request.get('request_datetime', 'غير محدد')}\n"
            content += "-" * 30 + "\n\n"
        
        table.setPlainText(content)
        layout.addWidget(table)
        
        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setFixedHeight(24)
        close_btn.setStyleSheet("""
            QPushButton {
                background: #95a5a6;
                border: none;
                border-radius: 3px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 9px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #7f8c8d;
            }
        """)
        close_btn.clicked.connect(window.close)
        layout.addWidget(close_btn)
        
        window.exec_()

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    font = QFont("Sakkal Majalla", 8)
    app.setFont(font)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🚀 نظام محسّن مع قائمة منسدلة للأسماء")
    print("=" * 40)
    print("✅ مقاسات مناسبة ومريحة")
    print("✅ قائمة منسدلة للموظفين مع:")
    print("   👥 أسماء افتراضية محملة")
    print("   ➕ إمكانية إضافة أسماء جديدة")
    print("   💾 حفظ الأسماء تلقائياً")
    print("✅ إدراج يدوي لعدد الأيام والساعات")
    print("✅ توجيه عربي كامل")
    print("=" * 40)
    
    # تسجيل الدخول
    login = SmallLoginWindow()
    if login.exec_() == QDialog.Accepted:
        print("✅ تم تسجيل الدخول بنجاح!")
        
        # النظام الرئيسي
        main_window = MainSystemWindow()
        main_window.show()
        
        return app.exec_()
    else:
        print("❌ تم إلغاء تسجيل الدخول")
        return 0

if __name__ == "__main__":
    sys.exit(main())
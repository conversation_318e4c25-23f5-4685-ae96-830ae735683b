// qsettings.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSettings : public QObject
{
%TypeHeaderCode
#include <qsettings.h>
%End

public:
    enum Status
    {
        NoError,
        AccessError,
        FormatError,
    };

    enum Format
    {
        NativeFormat,
        IniFormat,
        InvalidFormat,
    };

    enum Scope
    {
        UserScope,
        SystemScope,
    };

    QSettings(const QString &organization, const QString &application = QString(), QObject *parent /TransferThis/ = 0) /ReleaseGIL/;
    QSettings(QSettings::Scope scope, const QString &organization, const QString &application = QString(), QObject *parent /TransferThis/ = 0) /ReleaseGIL/;
    QSettings(QSettings::Format format, QSettings::Scope scope, const QString &organization, const QString &application = QString(), QObject *parent /TransferThis/ = 0) /ReleaseGIL/;
    QSettings(const QString &fileName, QSettings::Format format, QObject *parent /TransferThis/ = 0) /ReleaseGIL/;
%If (Qt_5_13_0 -)
    QSettings(QSettings::Scope scope, QObject *parent /TransferThis/ = 0) /ReleaseGIL/;
%End
    explicit QSettings(QObject *parent /TransferThis/ = 0) /ReleaseGIL/;
    virtual ~QSettings() /ReleaseGIL/;
    void clear() /ReleaseGIL/;
    void sync() /ReleaseGIL/;
    QSettings::Status status() const;
    void beginGroup(const QString &prefix);
    void endGroup();
    QString group() const;
    int beginReadArray(const QString &prefix);
    void beginWriteArray(const QString &prefix, int size = -1);
    void endArray();
    void setArrayIndex(int i);
    QStringList allKeys() const /ReleaseGIL/;
    QStringList childKeys() const /ReleaseGIL/;
    QStringList childGroups() const /ReleaseGIL/;
    bool isWritable() const;
    void setValue(const QString &key, const QVariant &value) /ReleaseGIL/;
    SIP_PYOBJECT value(const QString &key, const QVariant &defaultValue = QVariant(), SIP_PYOBJECT type /TypeHint="type", TypeHintValue="None"/ = 0) const /ReleaseGIL/;
%MethodCode
        QVariant value;
        
        // QSettings has an internal mutex so release the GIL to avoid the possibility
        // of deadlocks.
        Py_BEGIN_ALLOW_THREADS
        value = sipCpp->value(*a0, *a1);
        Py_END_ALLOW_THREADS
        
        sipRes = pyqt5_from_qvariant_by_type(value, a2);
        
        sipIsErr = !sipRes;
%End

    void remove(const QString &key) /ReleaseGIL/;
    bool contains(const QString &key) const /ReleaseGIL/;
    void setFallbacksEnabled(bool b);
    bool fallbacksEnabled() const;
    QString fileName() const;
    static void setPath(QSettings::Format format, QSettings::Scope scope, const QString &path) /ReleaseGIL/;
    QSettings::Format format() const;
    QSettings::Scope scope() const;
    QString organizationName() const;
    QString applicationName() const;
    static void setDefaultFormat(QSettings::Format format);
    static QSettings::Format defaultFormat();
    void setIniCodec(QTextCodec *codec /KeepReference/);
    void setIniCodec(const char *codecName);
    QTextCodec *iniCodec() const;
%If (Qt_5_10_0 -)
    bool isAtomicSyncRequired() const;
%End
%If (Qt_5_10_0 -)
    void setAtomicSyncRequired(bool enable);
%End

protected:
    virtual bool event(QEvent *event);
};

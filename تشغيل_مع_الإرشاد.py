#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل نظام إدارة الإجازات مع الإرشاد
"""

import sys
import os
import time

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "="*70)
    print(f"🎯 {title}")
    print("="*70)

def print_instructions():
    """طباعة تعليمات الاستخدام"""
    print_header("مرحباً بك في نظام إدارة الإجازات")
    
    print("""
🎉 تم تشغيل النظام بنجاح!

📋 بيانات تسجيل الدخول:
   👤 اسم المستخدم: admin
   🔐 كلمة المرور: admin123

🚀 خطوات البدء السريع:

   1️⃣ سجل الدخول بالبيانات أعلاه
   
   2️⃣ انقر "📥 استيراد رصيد" لاستيراد البيانات النموذجية
      (الملف متوفر: نموذج_الرصيد_الابتدائي.xlsx)
   
   3️⃣ جرب إضافة طلب إجازة يومية:
      - انقر "📝 طلب إجازة يومية"
      - املأ البيانات (اختر اسم من الملف النموذجي)
   
   4️⃣ جرب إضافة طلب إجازة ساعية:
      - انقر "⏱️ طلب إجازة ساعية"
      - لاحظ الحساب التلقائي للمعادل
   
   5️⃣ شاهد التقرير:
      - انقر "📊 التقارير"
      - اختر موظف وشاهد تفاصيل رصيده

💡 نصائح مهمة:
   ✅ استخدم أسماء الموظفين من الملف النموذجي
   ✅ جرب جميع الأزرار لاستكشاف الوظائف
   ✅ راجع التقارير للتأكد من صحة الحسابات

📖 للمساعدة:
   📄 راجع: دليل_المستخدم_السريع.md
   🧪 راجع: دليل_اختبار_الواجهة.md

🔥 استمتع بالاستخدام!
""")

def main():
    """تشغيل البرنامج مع الإرشاد"""
    
    # عرض التعليمات
    print_instructions()
    
    # انتظار المستخدم
    input("\n🎮 اضغط Enter للمتابعة إلى البرنامج...")
    
    try:
        print("\n🚀 جاري تشغيل نظام إدارة الإجازات...")
        print("⏳ انتظار تحميل الواجهة...")
        
        # استيراد المكتبات
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد الخط والاتجاه للنصوص العربية
        app.setLayoutDirection(Qt.RightToLeft)
        font = QFont("Arial", 10)
        app.setFont(font)
        
        # استيراد النافذة الرئيسية
        from main_window import MainWindow
        
        # إنشاء وعرض النافذة
        window = MainWindow()
        window.show()
        
        print("✅ تم تشغيل البرنامج بنجاح!")
        print("🖥️ النافذة الرئيسية مفتوحة الآن")
        
        # عرض رسالة ترحيب في النافذة
        QMessageBox.information(
            window, 
            "مرحباً!", 
            "🎉 مرحباً بك في نظام إدارة الإجازات!\n\n"
            "👤 اسم المستخدم: admin\n"
            "🔐 كلمة المرور: admin123\n\n"
            "💡 ابدأ بتسجيل الدخول ثم استيراد الرصيد النموذجي"
        )
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البرنامج: {e}")
        print("\n🔧 حلول مقترحة:")
        print("   1. تأكد من تثبيت المكتبات: python setup.py")
        print("   2. جرب الاختبار: python اختبار_سريع.py")
        print("   3. راجع: README.md")
        
        input("\n⏳ اضغط Enter للخروج...")
        return False

if __name__ == '__main__':
    main()
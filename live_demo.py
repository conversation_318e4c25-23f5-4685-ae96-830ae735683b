#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض مباشر لنظام إدارة الإجازات المتقدم
Live Demo for Advanced Vacation Management System
"""

import sqlite3
import os
from datetime import datetime

def create_live_demo():
    """إنشاء عرض مباشر للنظام"""
    
    print("🎯 نظام إدارة الإجازات المتقدم - عرض مباشر")
    print("="*60)
    
    # إنشاء قاعدة بيانات
    conn = sqlite3.connect('vacation_live_demo.db')
    cursor = conn.cursor()
    
    # إنشاء جدول الموظفين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            department TEXT,
            initial_balance INTEGER DEFAULT 30,
            used_days INTEGER DEFAULT 0,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إضافة بيانات تجريبية
    employees_data = [
        ('أحمد محمد علي', 'الإدارة', 30, 5),
        ('فاطمة عبد الله', 'الموارد البشرية', 35, 8),
        ('محمد عبد الرحمن', 'المالية', 30, 12),
        ('عائشة سعيد', 'التسويق', 40, 3),
        ('عبد الله أحمد', 'تقنية المعلومات', 30, 15),
        ('سارة أحمد', 'المبيعات', 32, 7),
        ('يوسف محمد', 'الإنتاج', 28, 10),
        ('نور الدين', 'الجودة', 35, 4)
    ]
    
    cursor.execute('DELETE FROM employees')
    for name, dept, initial, used in employees_data:
        cursor.execute('''
            INSERT INTO employees (name, department, initial_balance, used_days)
            VALUES (?, ?, ?, ?)
        ''', (name, dept, initial, used))
    
    conn.commit()
    
    print("✅ تم إنشاء قاعدة البيانات بنجاح")
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # عرض قائمة الموظفين
    print("👥 قائمة الموظفين:")
    print("-" * 85)
    print("الرقم".ljust(5) + "الاسم".ljust(20) + "القسم".ljust(18) + "الرصيد".ljust(8) + "المستخدم".ljust(10) + "المتبقي".ljust(8) + "الحالة")
    print("-" * 85)
    
    cursor.execute('''
        SELECT id, name, department, initial_balance, used_days 
        FROM employees 
        ORDER BY name
    ''')
    
    total_initial = 0
    total_used = 0
    low_balance_count = 0
    
    for row in cursor.fetchall():
        emp_id, name, dept, initial, used = row
        remaining = initial - used
        total_initial += initial
        total_used += used
        
        # تحديد حالة الرصيد
        if remaining < 5:
            status = "🔴 منخفض"
            low_balance_count += 1
        elif remaining < 10:
            status = "🟡 متوسط"
        else:
            status = "🟢 جيد"
        
        print(f"{emp_id:<5} {name[:18]:<20} {dept[:16]:<18} {initial:<8} {used:<10} {remaining:<8} {status}")
    
    print("-" * 85)
    print(f"الإجمالي:".ljust(43) + f"{total_initial:<8} {total_used:<10} {total_initial - total_used}")
    
    print()
    print("📊 إحصائيات شاملة:")
    print(f"  👥 إجمالي الموظفين: {len(employees_data)}")
    print(f"  📈 إجمالي الرصيد الابتدائي: {total_initial} يوم")
    print(f"  📉 إجمالي الأيام المستخدمة: {total_used} يوم")
    print(f"  💰 إجمالي الرصيد المتبقي: {total_initial - total_used} يوم")
    print(f"  📊 معدل الاستخدام: {(total_used/total_initial*100):.1f}%")
    print(f"  ⚠️ موظفين برصيد منخفض: {low_balance_count}")
    
    # تحليل حسب الأقسام
    print()
    print("🏢 تحليل حسب الأقسام:")
    print("-" * 50)
    
    cursor.execute('''
        SELECT department, COUNT(*) as emp_count, 
               SUM(initial_balance) as total_initial,
               SUM(used_days) as total_used
        FROM employees 
        GROUP BY department 
        ORDER BY total_used DESC
    ''')
    
    for dept, count, dept_initial, dept_used in cursor.fetchall():
        dept_remaining = dept_initial - dept_used
        usage_rate = (dept_used / dept_initial * 100) if dept_initial > 0 else 0
        print(f"  📋 {dept[:15]:<15}: {count} موظف | {dept_used}/{dept_initial} أيام ({usage_rate:.1f}%)")
    
    # عرض التنبيهات
    print()
    print("🔔 التنبيهات والإشعارات:")
    print("-" * 40)
    
    # موظفين برصيد منخفض
    cursor.execute('''
        SELECT name, initial_balance - used_days as remaining 
        FROM employees 
        WHERE remaining < 10 
        ORDER BY remaining ASC
    ''')
    
    low_balance_employees = cursor.fetchall()
    
    if low_balance_employees:
        print("⚠️ موظفين برصيد منخفض:")
        for name, remaining in low_balance_employees:
            if remaining < 5:
                icon = "🔴"
                level = "حرج"
            else:
                icon = "🟡"
                level = "تحذير"
            print(f"  {icon} {name}: {remaining} يوم ({level})")
    else:
        print("✅ جميع الموظفين لديهم رصيد كافي")
    
    # أكثر الموظفين استخداماً
    print()
    print("📈 أكثر الموظفين استخداماً للإجازات:")
    cursor.execute('''
        SELECT name, used_days, initial_balance,
               ROUND((used_days * 100.0 / initial_balance), 1) as usage_percent
        FROM employees 
        ORDER BY usage_percent DESC 
        LIMIT 5
    ''')
    
    for i, (name, used, initial, usage_percent) in enumerate(cursor.fetchall(), 1):
        print(f"  {i}. {name}: {used}/{initial} أيام ({usage_percent}%)")
    
    # رسم بياني للأرصدة
    print()
    print("📊 رسم بياني للأرصدة المتبقية:")
    print("-" * 50)
    
    cursor.execute('''
        SELECT name, initial_balance - used_days as remaining 
        FROM employees 
        ORDER BY remaining DESC
    ''')
    
    remaining_data = cursor.fetchall()
    max_remaining = max(row[1] for row in remaining_data) if remaining_data else 1
    
    for name, remaining in remaining_data:
        bar_length = int((remaining / max_remaining) * 30) if max_remaining > 0 else 0
        bar = "█" * bar_length
        short_name = name[:12] + ".." if len(name) > 14 else name
        
        # تلوين الشريط حسب الحالة
        if remaining < 5:
            color_bar = f"🔴{bar}"
        elif remaining < 10:
            color_bar = f"🟡{bar}"
        else:
            color_bar = f"🟢{bar}"
        
        print(f"{short_name:<14}: {color_bar} ({remaining} يوم)")
    
    conn.close()
    
    print()
    print("🎉 النظام يعمل بشكل مثالي!")
    print()
    print("💡 الملفات المتقدمة المتاحة:")
    
    advanced_files = [
        ('master_control_panel.py', 'لوحة التحكم الرئيسية'),
        ('advanced_reports.py', 'نظام التقارير المتقدم'),
        ('analytics_dashboard.py', 'لوحة التحليلات'),
        ('backup_system.py', 'نظام النسخ الاحتياطي'),
        ('notification_system.py', 'نظام الإشعارات'),
        ('user_management_interface.py', 'إدارة المستخدمين')
    ]
    
    for filename, description in advanced_files:
        status = "✅" if os.path.exists(filename) else "❌"
        print(f"  {status} {filename} - {description}")
    
    print()
    print("🚀 طرق التشغيل المتاحة:")
    print("  1. python master_control_panel.py - النظام الكامل")
    print("  2. python simple_demo.py - العرض التوضيحي")
    print("  3. python analytics_dashboard.py - لوحة التحليلات")
    print("  4. python run_reports.py - نظام التقارير")
    print("  5. تشغيل_النظام_المتقدم.bat - تشغيل سريع")
    
    print()
    print("🔧 ميزات النظام المتقدم:")
    features = [
        "📊 تقارير شاملة مع تصدير Excel",
        "🔔 إشعارات ذكية وتنبيهات تلقائية",
        "📈 رسوم بيانية وتحليلات متقدمة",
        "💾 نسخ احتياطي تلقائي وآمن",
        "👥 إدارة مستخدمين مع صلاحيات متقدمة",
        "🎯 لوحة تحكم موحدة وسهلة الاستخدام"
    ]
    
    for feature in features:
        print(f"  ✨ {feature}")
    
    print()
    print("="*60)
    print("✅ العرض المباشر اكتمل بنجاح!")
    print("💡 النظام جاهز للاستخدام الفوري على جهازك")
    print("🚀 جرب أي من طرق التشغيل أعلاه")
    print("="*60)

if __name__ == "__main__":
    try:
        create_live_demo()
    except Exception as e:
        print(f"❌ خطأ في العرض: {e}")
        print("💡 تأكد من وجود Python وجميع الملفات المطلوبة")

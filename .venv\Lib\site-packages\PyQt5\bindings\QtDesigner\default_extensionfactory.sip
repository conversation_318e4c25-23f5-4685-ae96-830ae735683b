// default_extensionfactory.sip generated by MetaSIP
//
// This file is part of the QtDesigner Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QExtensionFactory : public QObject, public QAbstractExtensionFactory
{
%TypeHeaderCode
#include <default_extensionfactory.h>
%End

public:
%If (Qt_5_11_0 -)
    explicit QExtensionFactory(QExtensionManager *parent /TransferThis/ = 0);
%End
%If (- Qt_5_11_0)
    QExtensionFactory(QExtensionManager *parent /TransferThis/ = 0);
%End
    virtual QObject *extension(QObject *object, const QString &iid) const;
    QExtensionManager *extensionManager() const;

protected:
    virtual QObject *createExtension(QObject *object, const QString &iid, QObject *parent /TransferThis/) const /Factory/;
};

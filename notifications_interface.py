#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة نظام الإشعارات والتنبيهات
Notifications and Alerts Interface
"""

import sys
import os
from datetime import datetime
from notification_system import NotificationSystem
import json

class NotificationsInterface:
    def __init__(self):
        self.notification_system = NotificationSystem()
    
    def display_main_menu(self):
        """عرض القائمة الرئيسية"""
        stats = self.notification_system.get_notification_stats()
        
        print("\n" + "="*60)
        print("🔔 نظام الإشعارات والتنبيهات")
        print("="*60)
        print(f"📊 الإحصائيات: {stats['total']} إجمالي | {stats['unread']} غير مقروء")
        print("-"*60)
        print("1. عرض الإشعارات غير المقروءة")
        print("2. عرض جميع الإشعارات")
        print("3. تشغيل فحص التنبيهات")
        print("4. إعدادات التنبيهات")
        print("5. إحصائيات مفصلة")
        print("6. تنظيف الإشعارات القديمة")
        print("7. إنشاء إشعار يدوي")
        print("0. خروج")
        print("="*60)
    
    def display_notifications(self, status='unread'):
        """عرض الإشعارات"""
        notifications = self.notification_system.get_notifications(status)
        
        if not notifications:
            print(f"\n📭 لا توجد إشعارات {self._get_status_text(status)}")
            return
        
        print(f"\n🔔 الإشعارات {self._get_status_text(status)} ({len(notifications)})")
        print("-" * 80)
        
        for i, notification in enumerate(notifications, 1):
            self._display_notification_item(i, notification)
        
        # خيارات التفاعل
        if status == 'unread':
            print("\nخيارات:")
            print("r [رقم] - تحديد كمقروء")
            print("ra - تحديد الكل كمقروء")
            print("d [رقم] - عرض التفاصيل")
            
            choice = input("اختر عملية (أو Enter للعودة): ").strip().lower()
            
            if choice.startswith('r'):
                if choice == 'ra':
                    self._mark_all_read(notifications)
                elif choice.startswith('r '):
                    try:
                        index = int(choice.split()[1]) - 1
                        if 0 <= index < len(notifications):
                            self.notification_system.mark_notification_read(notifications[index][0])
                            print("✅ تم تحديد الإشعار كمقروء")
                        else:
                            print("⚠️ رقم غير صحيح")
                    except (ValueError, IndexError):
                        print("⚠️ تنسيق غير صحيح")
            
            elif choice.startswith('d '):
                try:
                    index = int(choice.split()[1]) - 1
                    if 0 <= index < len(notifications):
                        self._display_notification_details(notifications[index])
                    else:
                        print("⚠️ رقم غير صحيح")
                except (ValueError, IndexError):
                    print("⚠️ تنسيق غير صحيح")
    
    def _display_notification_item(self, index, notification):
        """عرض عنصر إشعار واحد"""
        id, type, title, message, employee_name, priority, status, created_at, scheduled_for, data = notification
        
        # رموز الأولوية
        priority_icons = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}
        priority_icon = priority_icons.get(priority, '⚪')
        
        # رموز النوع
        type_icons = {
            'low_balance': '⚠️',
            'vacation_expiry': '📅',
            'pending_requests': '📋',
            'daily_summary': '📊',
            'system': '⚙️'
        }
        type_icon = type_icons.get(type, '📢')
        
        # تنسيق التاريخ
        created_date = datetime.fromisoformat(created_at.replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M')
        
        print(f"{index:2d}. {priority_icon} {type_icon} {title}")
        print(f"    📝 {message[:60]}{'...' if len(message) > 60 else ''}")
        if employee_name:
            print(f"    👤 {employee_name}")
        print(f"    🕐 {created_date} | 📊 {status}")
        print()
    
    def _display_notification_details(self, notification):
        """عرض تفاصيل الإشعار"""
        id, type, title, message, employee_name, priority, status, created_at, scheduled_for, data = notification
        
        print("\n" + "="*60)
        print("📋 تفاصيل الإشعار")
        print("="*60)
        print(f"🆔 المعرف: {id}")
        print(f"📝 العنوان: {title}")
        print(f"📄 الرسالة: {message}")
        print(f"🏷️ النوع: {type}")
        print(f"⚡ الأولوية: {priority}")
        print(f"📊 الحالة: {status}")
        
        if employee_name:
            print(f"👤 الموظف: {employee_name}")
        
        created_date = datetime.fromisoformat(created_at.replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
        print(f"🕐 تاريخ الإنشاء: {created_date}")
        
        if scheduled_for:
            scheduled_date = datetime.fromisoformat(scheduled_for.replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')
            print(f"⏰ مجدول لـ: {scheduled_date}")
        
        if data:
            try:
                data_obj = json.loads(data)
                print("📊 البيانات الإضافية:")
                for key, value in data_obj.items():
                    print(f"   {key}: {value}")
            except json.JSONDecodeError:
                print(f"📊 البيانات الإضافية: {data}")
        
        print("="*60)
        input("اضغط Enter للعودة...")
    
    def _mark_all_read(self, notifications):
        """تحديد جميع الإشعارات كمقروءة"""
        for notification in notifications:
            self.notification_system.mark_notification_read(notification[0])
        print(f"✅ تم تحديد {len(notifications)} إشعار كمقروء")
    
    def _get_status_text(self, status):
        """الحصول على نص الحالة بالعربية"""
        status_map = {
            'unread': 'غير المقروءة',
            'read': 'المقروءة',
            None: 'جميع'
        }
        return status_map.get(status, status)
    
    def run_alerts_check(self):
        """تشغيل فحص التنبيهات"""
        print("\n🔍 تشغيل فحص التنبيهات...")
        print("-" * 40)
        
        results = self.notification_system.run_all_checks()
        
        print("📊 نتائج الفحص:")
        print(f"⚠️ تنبيهات الرصيد المنخفض: {results['low_balance']}")
        print(f"📅 تنبيهات انتهاء الإجازات: {results['vacation_expiry']}")
        print(f"📋 تنبيهات الطلبات المعلقة: {results['pending_requests']}")
        print(f"📊 الملخص اليومي: {results['daily_summary']}")
        
        total_new = sum(results.values())
        print(f"\n✅ تم إنشاء {total_new} إشعار جديد")
    
    def manage_settings(self):
        """إدارة إعدادات التنبيهات"""
        while True:
            print("\n⚙️ إعدادات التنبيهات")
            print("-" * 30)
            
            settings = [
                ('low_balance_threshold', 'حد التنبيه عند انخفاض الرصيد'),
                ('vacation_expiry_days', 'عدد الأيام للتنبيه قبل انتهاء الإجازة'),
                ('enable_email_notifications', 'تفعيل الإشعارات عبر البريد الإلكتروني'),
                ('enable_desktop_notifications', 'تفعيل الإشعارات على سطح المكتب'),
                ('daily_report_time', 'وقت إرسال التقرير اليومي')
            ]
            
            for i, (setting_name, description) in enumerate(settings, 1):
                current_value = self.notification_system.get_setting(setting_name)
                print(f"{i}. {description}: {current_value}")
            
            print("0. العودة للقائمة الرئيسية")
            
            choice = input("\nاختر إعداد للتعديل: ").strip()
            
            if choice == '0':
                break
            
            try:
                index = int(choice) - 1
                if 0 <= index < len(settings):
                    setting_name, description = settings[index]
                    current_value = self.notification_system.get_setting(setting_name)
                    
                    print(f"\n📝 تعديل: {description}")
                    print(f"القيمة الحالية: {current_value}")
                    new_value = input("القيمة الجديدة (أو Enter للإلغاء): ").strip()
                    
                    if new_value:
                        self.notification_system.update_setting(setting_name, new_value)
                        print("✅ تم تحديث الإعداد")
                else:
                    print("⚠️ اختيار غير صحيح")
            except ValueError:
                print("⚠️ يرجى إدخال رقم صحيح")
    
    def show_detailed_stats(self):
        """عرض إحصائيات مفصلة"""
        stats = self.notification_system.get_notification_stats()
        
        print("\n📊 إحصائيات مفصلة للإشعارات")
        print("=" * 50)
        
        print(f"📈 الإحصائيات العامة:")
        print(f"   إجمالي الإشعارات: {stats['total']}")
        print(f"   غير مقروءة: {stats['unread']}")
        print(f"   مقروءة: {stats['total'] - stats['unread']}")
        
        if stats['by_type']:
            print(f"\n📋 حسب النوع:")
            for type_name, count in stats['by_type']:
                print(f"   {type_name}: {count}")
        
        if stats['by_priority']:
            print(f"\n⚡ حسب الأولوية (غير مقروءة):")
            for priority, count in stats['by_priority']:
                print(f"   {priority}: {count}")
    
    def cleanup_notifications(self):
        """تنظيف الإشعارات القديمة"""
        print("\n🧹 تنظيف الإشعارات القديمة")
        print("-" * 30)
        
        days = input("عدد الأيام للاحتفاظ بالإشعارات المقروءة (افتراضي: 30): ").strip()
        
        try:
            days = int(days) if days else 30
            deleted_count = self.notification_system.cleanup_old_notifications(days)
            print(f"✅ تم حذف {deleted_count} إشعار قديم")
        except ValueError:
            print("⚠️ يرجى إدخال رقم صحيح")
    
    def create_manual_notification(self):
        """إنشاء إشعار يدوي"""
        print("\n📝 إنشاء إشعار يدوي")
        print("-" * 30)
        
        title = input("العنوان: ").strip()
        if not title:
            print("⚠️ العنوان مطلوب")
            return
        
        message = input("الرسالة: ").strip()
        if not message:
            print("⚠️ الرسالة مطلوبة")
            return
        
        employee_name = input("اسم الموظف (اختياري): ").strip() or None
        
        print("الأولوية:")
        print("1. عالية (high)")
        print("2. متوسطة (medium)")
        print("3. منخفضة (low)")
        
        priority_choice = input("اختر الأولوية (افتراضي: متوسطة): ").strip()
        priority_map = {'1': 'high', '2': 'medium', '3': 'low'}
        priority = priority_map.get(priority_choice, 'medium')
        
        notification_id = self.notification_system.create_notification(
            'manual', title, message, employee_name, priority
        )
        
        print(f"✅ تم إنشاء الإشعار برقم: {notification_id}")
    
    def run(self):
        """تشغيل الواجهة"""
        print("🔔 مرحباً بك في نظام الإشعارات والتنبيهات!")
        
        while True:
            try:
                self.display_main_menu()
                choice = input("اختر العملية المطلوبة: ").strip()
                
                if choice == '0':
                    print("👋 شكراً لاستخدام نظام الإشعارات!")
                    break
                elif choice == '1':
                    self.display_notifications('unread')
                elif choice == '2':
                    self.display_notifications(None)
                elif choice == '3':
                    self.run_alerts_check()
                elif choice == '4':
                    self.manage_settings()
                elif choice == '5':
                    self.show_detailed_stats()
                elif choice == '6':
                    self.cleanup_notifications()
                elif choice == '7':
                    self.create_manual_notification()
                else:
                    print("⚠️ اختيار غير صحيح")
                
                if choice != '0':
                    input("\nاضغط Enter للمتابعة...")
                
            except KeyboardInterrupt:
                print("\n\n👋 تم إيقاف البرنامج")
                break
            except Exception as e:
                print(f"\n❌ خطأ غير متوقع: {e}")
                input("اضغط Enter للمتابعة...")

if __name__ == "__main__":
    interface = NotificationsInterface()
    interface.run()

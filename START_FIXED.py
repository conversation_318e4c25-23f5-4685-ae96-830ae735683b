#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 ملف التشغيل السريع للنظام
START.py - أسرع طريقة لتشغيل النظام
"""

import sys
import os
import subprocess
from PyQt5.QtWidgets import QApplication, QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QFrame
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

def quick_start():
    """تشغيل سريع للنظام"""
    
    print("🚀 تشغيل سريع لنظام إدارة الإجازات...")
    print("=" * 50)
    
    # محاولة تشغيل النظام المتكامل
    try:
        print("🎯 محاولة تشغيل المشغل الموحد...")
        subprocess.run([sys.executable, "تشغيل_النظام_المتكامل.py"], 
                      timeout=3)
        print("✅ تم تشغيل المشغل الموحد بنجاح!")
        return True
        
    except subprocess.TimeoutExpired:
        print("✅ المشغل الموحد يعمل...")
        return True
        
    except Exception as e:
        print(f"⚠️ خطأ في المشغل الموحد: {e}")
        
        # محاولة تشغيل الواجهة الحديثة
        try:
            print("🎯 محاولة تشغيل الواجهة الحديثة...")
            subprocess.run([sys.executable, "تشغيل_الواجهة_الحديثة.py"], 
                          timeout=3)
            print("✅ تم تشغيل الواجهة الحديثة بنجاح!")
            return True
            
        except Exception as e2:
            print(f"⚠️ خطأ في الواجهة الحديثة: {e2}")
            
            # محاولة تشغيل الواجهة التقليدية
            try:
                print("🎯 محاولة تشغيل الواجهة التقليدية...")
                subprocess.run([sys.executable, "run_app.py"], 
                              timeout=3)
                print("✅ تم تشغيل الواجهة التقليدية بنجاح!")
                return True
                
            except Exception as e3:
                print(f"❌ خطأ في جميع الواجهات: {e3}")
                return False

def show_quick_menu():
    """عرض قائمة سريعة"""
    
    print("\n🎯 قائمة التشغيل السريع:")
    print("=" * 30)
    print("1. 🚀 المشغل الموحد الذكي")
    print("2. ✨ الواجهة الحديثة")
    print("3. 🔹 الواجهة التقليدية")
    print("4. 🎛️ نظام الاختيار الذكي")
    print("5. 📊 نظام الإحصائيات")
    print("6. 👥 إدارة المستخدمين")
    print("7. 🗄️ مدير قاعدة البيانات")
    print("8. 🧪 اختبار النظام")
    print("9. 📋 عرض جميع الأنظمة")
    print("0. ❌ خروج")
    print("=" * 30)

def run_selected_system(choice):
    """تشغيل النظام المختار"""
    
    systems = {
        "1": "تشغيل_النظام_المتكامل.py",
        "2": "تشغيل_الواجهة_الحديثة.py", 
        "3": "run_app.py",
        "4": "اختيار_الواجهة.py",
        "5": "modern_simple_analytics.py",
        "6": "modern_user_management.py",
        "7": "modern_database_manager.py",
        "8": "اختبار_سريع.py",
        "9": "عرض_الأنظمة_المتاحة.py"
    }
    
    if choice in systems:
        try:
            print(f"🚀 تشغيل {systems[choice]}...")
            subprocess.run([sys.executable, systems[choice]])
            return True
        except Exception as e:
            print(f"❌ خطأ في تشغيل {systems[choice]}: {e}")
            return False
    else:
        print("❌ خيار غير صحيح!")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🏢 نظام إدارة الإجازات المتكامل")
    print("🚀 ملف التشغيل السريع")
    print("=" * 50)
    
    while True:
        show_quick_menu()
        
        try:
            choice = input("\n🎯 اختر رقم الخيار (0-9): ").strip()
            
            if choice == "0":
                print("👋 شكراً لاستخدام النظام!")
                break
            elif choice == "":
                # تشغيل سريع تلقائي
                print("🚀 تشغيل تلقائي للنظام...")
                if quick_start():
                    break
            else:
                if run_selected_system(choice):
                    break
                    
        except KeyboardInterrupt:
            print("\n\n⏹️ تم إيقاف النظام بواسطة المستخدم")
            break
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")
            
    print("\n🎉 انتهى تشغيل النظام!")

if __name__ == "__main__":
    main()
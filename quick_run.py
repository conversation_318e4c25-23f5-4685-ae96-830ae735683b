#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لنظام إدارة الإجازات
Quick Run for Vacation Management System
"""

import sqlite3
import os
from datetime import datetime

def main():
    print("🎯 نظام إدارة الإجازات المتقدم - تشغيل سريع")
    print("=" * 60)
    
    # إنشاء قاعدة بيانات
    conn = sqlite3.connect('vacation_quick.db')
    cursor = conn.cursor()
    
    # إنشاء جدول الموظفين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            department TEXT,
            initial_balance INTEGER DEFAULT 30,
            used_days INTEGER DEFAULT 0
        )
    ''')
    
    # إضافة بيانات تجريبية
    employees_data = [
        ('أحمد محمد علي', 'الإدارة', 30, 5),
        ('فاطمة عبد الله', 'الموارد البشرية', 35, 8),
        ('محمد عبد الرحمن', 'المالية', 30, 12),
        ('عائشة سعيد', 'التسويق', 40, 3),
        ('عبد الله أحمد', 'تقنية المعلومات', 30, 15),
        ('سارة أحمد', 'المبيعات', 32, 7),
        ('يوسف محمد', 'الإنتاج', 28, 10),
        ('نور الدين', 'الجودة', 35, 4)
    ]
    
    cursor.execute('DELETE FROM employees')
    for name, dept, initial, used in employees_data:
        cursor.execute('''
            INSERT INTO employees (name, department, initial_balance, used_days)
            VALUES (?, ?, ?, ?)
        ''', (name, dept, initial, used))
    
    conn.commit()
    
    print("✅ تم إنشاء قاعدة البيانات بنجاح")
    print(f"📅 وقت التشغيل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # عرض البيانات
    print("👥 قائمة الموظفين النشطة:")
    print("-" * 80)
    print("الرقم".ljust(5) + "الاسم".ljust(20) + "القسم".ljust(18) + "الرصيد".ljust(8) + "المستخدم".ljust(10) + "المتبقي".ljust(8) + "الحالة")
    print("-" * 80)
    
    cursor.execute('SELECT id, name, department, initial_balance, used_days FROM employees ORDER BY name')
    total_initial = 0
    total_used = 0
    
    for row in cursor.fetchall():
        emp_id, name, dept, initial, used = row
        remaining = initial - used
        total_initial += initial
        total_used += used
        
        if remaining < 5:
            status = "🔴 منخفض"
        elif remaining < 10:
            status = "🟡 متوسط"
        else:
            status = "🟢 جيد"
        
        print(f"{emp_id:<5} {name[:18]:<20} {dept[:16]:<18} {initial:<8} {used:<10} {remaining:<8} {status}")
    
    print("-" * 80)
    print(f"الإجمالي:".ljust(43) + f"{total_initial:<8} {total_used:<10} {total_initial - total_used}")
    
    print()
    print("📊 إحصائيات التشغيل:")
    print(f"  👥 إجمالي الموظفين: {len(employees_data)}")
    print(f"  📈 إجمالي الرصيد: {total_initial} يوم")
    print(f"  📉 إجمالي المستخدم: {total_used} يوم")
    print(f"  💰 إجمالي المتبقي: {total_initial - total_used} يوم")
    print(f"  📊 معدل الاستخدام: {(total_used/total_initial*100):.1f}%")
    
    # عرض التنبيهات
    print()
    print("🔔 التنبيهات والإشعارات:")
    print("-" * 40)
    
    cursor.execute('SELECT name, initial_balance - used_days as remaining FROM employees WHERE remaining < 10')
    low_balance = cursor.fetchall()
    
    if low_balance:
        print("⚠️ موظفين برصيد منخفض:")
        for name, remaining in low_balance:
            icon = "🔴" if remaining < 5 else "🟡"
            level = "حرج" if remaining < 5 else "تحذير"
            print(f"  {icon} {name}: {remaining} يوم ({level})")
    else:
        print("✅ جميع الموظفين لديهم رصيد كافي")
    
    # رسم بياني بسيط
    print()
    print("📈 رسم بياني للأرصدة المتبقية:")
    print("-" * 50)
    
    cursor.execute('SELECT name, initial_balance - used_days as remaining FROM employees ORDER BY remaining DESC')
    employees_remaining = cursor.fetchall()
    max_remaining = max(row[1] for row in employees_remaining) if employees_remaining else 1
    
    for name, remaining in employees_remaining:
        bar_length = int((remaining / max_remaining) * 30) if max_remaining > 0 else 0
        bar = "█" * bar_length
        short_name = name[:12] + ".." if len(name) > 14 else name
        
        if remaining < 5:
            color_bar = f"🔴{bar}"
        elif remaining < 10:
            color_bar = f"🟡{bar}"
        else:
            color_bar = f"🟢{bar}"
        
        print(f"{short_name:<14}: {color_bar} ({remaining} يوم)")
    
    conn.close()
    
    print()
    print("🎉 النظام يعمل بشكل مثالي!")
    print()
    print("🚀 الواجهات المتاحة للتشغيل:")
    print("  1. python text_interface.py - واجهة نصية تفاعلية كاملة")
    print("  2. python master_control_panel.py - لوحة التحكم الرئيسية")
    print("  3. python main.py - النظام الأساسي")
    print("  4. python simple_demo.py - عرض توضيحي")
    print("  5. python analytics_dashboard.py - لوحة التحليلات")
    print()
    print("💡 إذا لم تظهر الواجهة الرسومية، استخدم الواجهة النصية")
    print("=" * 60)
    print("✅ النظام جاهز للاستخدام الفوري!")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    input("\nاضغط Enter للخروج...")

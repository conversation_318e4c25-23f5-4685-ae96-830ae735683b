#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إدارة المستخدمين والصلاحيات
User Management and Permissions Interface
"""

import sys
import os
from datetime import datetime
from database import VacationDatabase
import json

class UserManagementInterface:
    def __init__(self):
        self.db = VacationDatabase()
        self.current_user_id = None
        self.current_user_permissions = []
    
    def set_current_user(self, user_id):
        """تعيين المستخدم الحالي"""
        self.current_user_id = user_id
        self.current_user_permissions = self.db.get_user_permissions(user_id)
    
    def check_permission(self, permission):
        """التحقق من صلاحية المستخدم الحالي"""
        return permission in self.current_user_permissions
    
    def display_main_menu(self):
        """عرض القائمة الرئيسية"""
        users = self.db.get_all_users()
        roles = self.db.get_all_roles()
        
        print("\n" + "="*70)
        print("👥 نظام إدارة المستخدمين والصلاحيات")
        print("="*70)
        print(f"📊 الإحصائيات: {len(users)} مستخدم | {len(roles)} دور")
        print("-"*70)
        
        if self.check_permission('user_management'):
            print("1. عرض جميع المستخدمين")
            print("2. إنشاء مستخدم جديد")
            print("3. تعديل مستخدم")
            print("4. حذف مستخدم")
            print("5. إدارة صلاحيات المستخدم")
        
        if self.check_permission('role_management'):
            print("6. عرض الأدوار")
            print("7. إنشاء دور جديد")
            print("8. تعديل دور")
        
        if self.check_permission('view_logs'):
            print("9. عرض سجل النشاط")
        
        print("10. عرض صلاحياتي")
        print("0. خروج")
        print("="*70)
    
    def list_users(self):
        """عرض قائمة المستخدمين"""
        if not self.check_permission('user_management'):
            print("❌ ليس لديك صلاحية لعرض المستخدمين")
            return
        
        users = self.db.get_all_users()
        
        if not users:
            print("\n📭 لا يوجد مستخدمين")
            return
        
        print(f"\n👥 قائمة المستخدمين ({len(users)} مستخدم)")
        print("-" * 100)
        print("المعرف".ljust(5) + "اسم المستخدم".ljust(15) + "الاسم الكامل".ljust(20) + "الدور".ljust(15) + "القسم".ljust(15) + "الحالة".ljust(8) + "آخر دخول")
        print("-" * 100)
        
        for user in users:
            user_id, username, role, full_name, email, department, is_active, last_login, created_at = user
            
            status = "نشط" if is_active else "معطل"
            last_login_str = last_login[:16] if last_login else "لم يدخل"
            full_name = full_name or "غير محدد"
            department = department or "غير محدد"
            
            print(f"{user_id:<5} {username:<15} {full_name[:18]:<20} {role:<15} {department[:13]:<15} {status:<8} {last_login_str}")
    
    def create_user(self):
        """إنشاء مستخدم جديد"""
        if not self.check_permission('user_management'):
            print("❌ ليس لديك صلاحية لإنشاء المستخدمين")
            return
        
        print("\n👤 إنشاء مستخدم جديد")
        print("-" * 30)
        
        username = input("اسم المستخدم: ").strip()
        if not username:
            print("⚠️ اسم المستخدم مطلوب")
            return
        
        password = input("كلمة المرور: ").strip()
        if not password:
            print("⚠️ كلمة المرور مطلوبة")
            return
        
        full_name = input("الاسم الكامل: ").strip()
        email = input("البريد الإلكتروني: ").strip()
        department = input("القسم: ").strip()
        
        # عرض الأدوار المتاحة
        roles = self.db.get_all_roles()
        print("\nالأدوار المتاحة:")
        for i, role in enumerate(roles, 1):
            print(f"  {i}. {role[1]} - {role[2]}")
        
        try:
            role_choice = int(input("اختر رقم الدور: ")) - 1
            if 0 <= role_choice < len(roles):
                role = roles[role_choice][1]
            else:
                print("⚠️ اختيار غير صحيح")
                return
        except ValueError:
            print("⚠️ يرجى إدخال رقم صحيح")
            return
        
        success, message = self.db.create_user(username, password, role, full_name, email, department)
        
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
    
    def edit_user(self):
        """تعديل مستخدم"""
        if not self.check_permission('user_management'):
            print("❌ ليس لديك صلاحية لتعديل المستخدمين")
            return
        
        self.list_users()
        
        try:
            user_id = int(input("\nأدخل معرف المستخدم للتعديل: "))
            user = self.db.get_user_by_id(user_id)
            
            if not user:
                print("⚠️ المستخدم غير موجود")
                return
            
            print(f"\nتعديل المستخدم: {user[1]}")
            print("-" * 30)
            
            # عرض البيانات الحالية وطلب التحديث
            updates = {}
            
            new_username = input(f"اسم المستخدم الجديد (الحالي: {user[1]}): ").strip()
            if new_username:
                updates['username'] = new_username
            
            new_password = input("كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية): ").strip()
            if new_password:
                updates['password'] = new_password
            
            new_full_name = input(f"الاسم الكامل الجديد (الحالي: {user[3] or 'غير محدد'}): ").strip()
            if new_full_name:
                updates['full_name'] = new_full_name
            
            new_email = input(f"البريد الإلكتروني الجديد (الحالي: {user[4] or 'غير محدد'}): ").strip()
            if new_email:
                updates['email'] = new_email
            
            new_department = input(f"القسم الجديد (الحالي: {user[5] or 'غير محدد'}): ").strip()
            if new_department:
                updates['department'] = new_department
            
            # تغيير الحالة
            current_status = "نشط" if user[6] else "معطل"
            new_status = input(f"الحالة الجديدة (الحالية: {current_status}) [active/inactive]: ").strip().lower()
            if new_status in ['active', 'inactive']:
                updates['is_active'] = new_status == 'active'
            
            # تغيير الدور
            roles = self.db.get_all_roles()
            print(f"\nالدور الحالي: {user[2]}")
            print("الأدوار المتاحة:")
            for i, role in enumerate(roles, 1):
                print(f"  {i}. {role[1]} - {role[2]}")
            
            role_choice = input("اختر رقم الدور الجديد (أو اتركه فارغاً): ").strip()
            if role_choice:
                try:
                    role_index = int(role_choice) - 1
                    if 0 <= role_index < len(roles):
                        updates['role'] = roles[role_index][1]
                except ValueError:
                    print("⚠️ رقم الدور غير صحيح")
            
            if updates:
                success, message = self.db.update_user(user_id, **updates)
                if success:
                    print(f"✅ {message}")
                else:
                    print(f"❌ {message}")
            else:
                print("ℹ️ لم يتم إجراء أي تغييرات")
                
        except ValueError:
            print("⚠️ يرجى إدخال معرف صحيح")
    
    def delete_user(self):
        """حذف مستخدم"""
        if not self.check_permission('user_management'):
            print("❌ ليس لديك صلاحية لحذف المستخدمين")
            return
        
        self.list_users()
        
        try:
            user_id = int(input("\nأدخل معرف المستخدم للحذف: "))
            user = self.db.get_user_by_id(user_id)
            
            if not user:
                print("⚠️ المستخدم غير موجود")
                return
            
            print(f"\n⚠️ تحذير: سيتم حذف المستخدم {user[1]} نهائياً!")
            confirm = input("هل أنت متأكد؟ (y/n): ").lower()
            
            if confirm in ['y', 'yes', 'نعم', 'ن']:
                success, message = self.db.delete_user(user_id)
                if success:
                    print(f"✅ {message}")
                else:
                    print(f"❌ {message}")
            else:
                print("❌ تم إلغاء العملية")
                
        except ValueError:
            print("⚠️ يرجى إدخال معرف صحيح")
    
    def manage_user_permissions(self):
        """إدارة صلاحيات المستخدم"""
        if not self.check_permission('user_management'):
            print("❌ ليس لديك صلاحية لإدارة الصلاحيات")
            return
        
        self.list_users()
        
        try:
            user_id = int(input("\nأدخل معرف المستخدم لإدارة صلاحياته: "))
            user = self.db.get_user_by_id(user_id)
            
            if not user:
                print("⚠️ المستخدم غير موجود")
                return
            
            permissions = self.db.get_user_permissions(user_id)
            
            print(f"\n🔐 صلاحيات المستخدم: {user[1]}")
            print("-" * 40)
            
            if permissions:
                print("الصلاحيات الحالية:")
                for i, perm in enumerate(permissions, 1):
                    print(f"  {i}. {perm}")
            else:
                print("لا توجد صلاحيات مخصصة")
            
            print("\nالعمليات المتاحة:")
            print("1. منح صلاحية")
            print("2. سحب صلاحية")
            print("0. العودة")
            
            choice = input("اختر العملية: ").strip()
            
            if choice == '1':
                self._grant_permission_to_user(user_id)
            elif choice == '2':
                self._revoke_permission_from_user(user_id)
            elif choice != '0':
                print("⚠️ اختيار غير صحيح")
                
        except ValueError:
            print("⚠️ يرجى إدخال معرف صحيح")
    
    def _grant_permission_to_user(self, user_id):
        """منح صلاحية للمستخدم"""
        available_permissions = [
            'user_management', 'role_management', 'system_settings',
            'view_all_data', 'edit_all_data', 'delete_data',
            'generate_reports', 'backup_restore', 'view_logs',
            'approve_requests', 'manage_employees', 'view_department_data'
        ]
        
        print("\nالصلاحيات المتاحة:")
        for i, perm in enumerate(available_permissions, 1):
            print(f"  {i}. {perm}")
        
        try:
            perm_choice = int(input("اختر رقم الصلاحية: ")) - 1
            if 0 <= perm_choice < len(available_permissions):
                permission = available_permissions[perm_choice]
                success, message = self.db.grant_permission(user_id, permission, self.current_user_id)
                
                if success:
                    print(f"✅ {message}")
                else:
                    print(f"❌ {message}")
            else:
                print("⚠️ اختيار غير صحيح")
        except ValueError:
            print("⚠️ يرجى إدخال رقم صحيح")
    
    def _revoke_permission_from_user(self, user_id):
        """سحب صلاحية من المستخدم"""
        permissions = self.db.get_user_permissions(user_id)
        
        if not permissions:
            print("⚠️ لا توجد صلاحيات لسحبها")
            return
        
        print("\nالصلاحيات الحالية:")
        for i, perm in enumerate(permissions, 1):
            print(f"  {i}. {perm}")
        
        try:
            perm_choice = int(input("اختر رقم الصلاحية لسحبها: ")) - 1
            if 0 <= perm_choice < len(permissions):
                permission = permissions[perm_choice]
                success, message = self.db.revoke_permission(user_id, permission, self.current_user_id)
                
                if success:
                    print(f"✅ {message}")
                else:
                    print(f"❌ {message}")
            else:
                print("⚠️ اختيار غير صحيح")
        except ValueError:
            print("⚠️ يرجى إدخال رقم صحيح")

    def list_roles(self):
        """عرض قائمة الأدوار"""
        if not self.check_permission('role_management'):
            print("❌ ليس لديك صلاحية لعرض الأدوار")
            return

        roles = self.db.get_all_roles()

        if not roles:
            print("\n📭 لا توجد أدوار")
            return

        print(f"\n🎭 قائمة الأدوار ({len(roles)} دور)")
        print("-" * 80)

        for role in roles:
            role_id, role_name, description, permissions_json, created_at = role

            try:
                permissions = json.loads(permissions_json)
                permissions_count = len(permissions)
            except json.JSONDecodeError:
                permissions_count = 0

            print(f"🎭 {role_name}")
            print(f"   📝 الوصف: {description or 'غير محدد'}")
            print(f"   🔐 عدد الصلاحيات: {permissions_count}")
            print(f"   📅 تاريخ الإنشاء: {created_at[:16]}")

            if permissions_count > 0:
                print(f"   📋 الصلاحيات: {', '.join(permissions[:5])}")
                if permissions_count > 5:
                    print(f"      ... و {permissions_count - 5} صلاحية أخرى")
            print()

    def create_role(self):
        """إنشاء دور جديد"""
        if not self.check_permission('role_management'):
            print("❌ ليس لديك صلاحية لإنشاء الأدوار")
            return

        print("\n🎭 إنشاء دور جديد")
        print("-" * 30)

        role_name = input("اسم الدور: ").strip()
        if not role_name:
            print("⚠️ اسم الدور مطلوب")
            return

        description = input("وصف الدور: ").strip()

        # اختيار الصلاحيات
        available_permissions = [
            'user_management', 'role_management', 'system_settings',
            'view_all_data', 'edit_all_data', 'delete_data',
            'generate_reports', 'backup_restore', 'view_logs',
            'approve_requests', 'manage_employees', 'view_department_data',
            'view_own_data', 'submit_requests', 'view_own_reports',
            'view_limited_data', 'view_public_reports'
        ]

        print("\nالصلاحيات المتاحة:")
        for i, perm in enumerate(available_permissions, 1):
            print(f"  {i}. {perm}")

        print("\nأدخل أرقام الصلاحيات مفصولة بفاصلة (مثال: 1,3,5):")
        permissions_input = input("الصلاحيات: ").strip()

        selected_permissions = []
        if permissions_input:
            try:
                perm_indices = [int(x.strip()) - 1 for x in permissions_input.split(',')]
                for index in perm_indices:
                    if 0 <= index < len(available_permissions):
                        selected_permissions.append(available_permissions[index])
            except ValueError:
                print("⚠️ تنسيق غير صحيح للصلاحيات")
                return

        success, message = self.db.create_role(role_name, description, selected_permissions)

        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")

    def edit_role(self):
        """تعديل دور"""
        if not self.check_permission('role_management'):
            print("❌ ليس لديك صلاحية لتعديل الأدوار")
            return

        self.list_roles()

        role_name = input("\nأدخل اسم الدور للتعديل: ").strip()
        if not role_name:
            print("⚠️ اسم الدور مطلوب")
            return

        # الحصول على الدور الحالي
        roles = self.db.get_all_roles()
        current_role = None

        for role in roles:
            if role[1] == role_name:
                current_role = role
                break

        if not current_role:
            print("⚠️ الدور غير موجود")
            return

        print(f"\nتعديل الدور: {role_name}")
        print("-" * 30)

        # تعديل الوصف
        current_description = current_role[2]
        new_description = input(f"الوصف الجديد (الحالي: {current_description or 'غير محدد'}): ").strip()

        # تعديل الصلاحيات
        try:
            current_permissions = json.loads(current_role[3])
        except json.JSONDecodeError:
            current_permissions = []

        print(f"\nالصلاحيات الحالية: {', '.join(current_permissions)}")

        available_permissions = [
            'user_management', 'role_management', 'system_settings',
            'view_all_data', 'edit_all_data', 'delete_data',
            'generate_reports', 'backup_restore', 'view_logs',
            'approve_requests', 'manage_employees', 'view_department_data',
            'view_own_data', 'submit_requests', 'view_own_reports',
            'view_limited_data', 'view_public_reports'
        ]

        print("\nالصلاحيات المتاحة:")
        for i, perm in enumerate(available_permissions, 1):
            marker = "✓" if perm in current_permissions else " "
            print(f"  {marker} {i}. {perm}")

        print("\nأدخل أرقام الصلاحيات الجديدة مفصولة بفاصلة (أو اتركها فارغة للاحتفاظ بالحالية):")
        permissions_input = input("الصلاحيات: ").strip()

        new_permissions = None
        if permissions_input:
            try:
                perm_indices = [int(x.strip()) - 1 for x in permissions_input.split(',')]
                new_permissions = []
                for index in perm_indices:
                    if 0 <= index < len(available_permissions):
                        new_permissions.append(available_permissions[index])
            except ValueError:
                print("⚠️ تنسيق غير صحيح للصلاحيات")
                return

        # تطبيق التحديثات
        updates_made = False

        if new_description and new_description != current_description:
            success, message = self.db.update_role(role_name, description=new_description)
            if success:
                print(f"✅ تم تحديث الوصف")
                updates_made = True
            else:
                print(f"❌ خطأ في تحديث الوصف: {message}")

        if new_permissions is not None:
            success, message = self.db.update_role(role_name, permissions=new_permissions)
            if success:
                print(f"✅ تم تحديث الصلاحيات")
                updates_made = True
            else:
                print(f"❌ خطأ في تحديث الصلاحيات: {message}")

        if not updates_made:
            print("ℹ️ لم يتم إجراء أي تغييرات")

    def view_activity_log(self):
        """عرض سجل النشاط"""
        if not self.check_permission('view_logs'):
            print("❌ ليس لديك صلاحية لعرض سجل النشاط")
            return

        print("\n📋 سجل النشاط")
        print("-" * 50)

        print("1. عرض سجل جميع المستخدمين")
        print("2. عرض سجل مستخدم محدد")

        choice = input("اختر نوع السجل: ").strip()

        user_id = None
        if choice == '2':
            self.list_users()
            try:
                user_id = int(input("أدخل معرف المستخدم: "))
            except ValueError:
                print("⚠️ معرف غير صحيح")
                return
        elif choice != '1':
            print("⚠️ اختيار غير صحيح")
            return

        logs = self.db.get_activity_log(user_id, limit=50)

        if not logs:
            print("📭 لا توجد سجلات")
            return

        print(f"\n📋 سجل النشاط ({len(logs)} عملية)")
        print("-" * 100)
        print("التاريخ".ljust(20) + "المستخدم".ljust(15) + "العملية".ljust(20) + "النوع".ljust(15) + "التفاصيل")
        print("-" * 100)

        for log in logs:
            log_id, user_id, action, target_type, target_id, details, ip_address, timestamp, username = log

            timestamp_str = timestamp[:16] if timestamp else ""
            username = username or f"ID:{user_id}"
            target_type = target_type or ""
            details = details[:30] + "..." if details and len(details) > 30 else details or ""

            print(f"{timestamp_str:<20} {username:<15} {action:<20} {target_type:<15} {details}")

    def view_my_permissions(self):
        """عرض صلاحيات المستخدم الحالي"""
        if not self.current_user_id:
            print("⚠️ لم يتم تسجيل الدخول")
            return

        user = self.db.get_user_by_id(self.current_user_id)
        permissions = self.db.get_user_permissions(self.current_user_id)

        print(f"\n🔐 صلاحياتك - {user[1] if user else 'غير معروف'}")
        print("-" * 40)

        if user:
            print(f"👤 الاسم الكامل: {user[3] or 'غير محدد'}")
            print(f"🎭 الدور: {user[2]}")
            print(f"🏢 القسم: {user[5] or 'غير محدد'}")

        print(f"\n🔐 الصلاحيات ({len(permissions)}):")
        if permissions:
            for i, perm in enumerate(permissions, 1):
                print(f"  {i}. {perm}")
        else:
            print("  لا توجد صلاحيات مخصصة")

    def run(self):
        """تشغيل واجهة إدارة المستخدمين"""
        print("👥 مرحباً بك في نظام إدارة المستخدمين والصلاحيات!")

        # محاكاة تسجيل الدخول (في التطبيق الحقيقي سيتم من خلال نظام المصادقة)
        if not self.current_user_id:
            print("⚠️ يجب تسجيل الدخول أولاً")
            # للاختبار، سنستخدم المدير الافتراضي
            self.set_current_user(1)  # افتراض أن المدير له معرف 1

        while True:
            try:
                self.display_main_menu()
                choice = input("اختر العملية المطلوبة: ").strip()

                if choice == '0':
                    print("👋 شكراً لاستخدام نظام إدارة المستخدمين!")
                    break
                elif choice == '1':
                    self.list_users()
                elif choice == '2':
                    self.create_user()
                elif choice == '3':
                    self.edit_user()
                elif choice == '4':
                    self.delete_user()
                elif choice == '5':
                    self.manage_user_permissions()
                elif choice == '6':
                    self.list_roles()
                elif choice == '7':
                    self.create_role()
                elif choice == '8':
                    self.edit_role()
                elif choice == '9':
                    self.view_activity_log()
                elif choice == '10':
                    self.view_my_permissions()
                else:
                    print("⚠️ اختيار غير صحيح")

                if choice != '0':
                    input("\nاضغط Enter للمتابعة...")

            except KeyboardInterrupt:
                print("\n\n👋 تم إيقاف البرنامج")
                break
            except Exception as e:
                print(f"\n❌ خطأ غير متوقع: {e}")
                input("اضغط Enter للمتابعة...")

if __name__ == "__main__":
    interface = UserManagementInterface()
    interface.run()

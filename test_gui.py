#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الواجهة الرسومية
GUI Test
"""

import sys

def test_gui():
    print("🧪 اختبار الواجهة الرسومية...")
    print("-" * 40)
    
    try:
        print("1. اختبار PyQt5...")
        from PyQt5.QtWidgets import QApplication, QMessageBox, QWidget
        from PyQt5.QtCore import Qt
        
        print("✅ تم استيراد PyQt5 بنجاح")
        
        # إنشاء تطبيق
        app = QApplication(sys.argv)
        
        print("2. إنشاء نافذة اختبار...")
        
        # إنشاء نافذة بسيطة
        window = QWidget()
        window.setWindowTitle("اختبار نظام إدارة الإجازات")
        window.setGeometry(100, 100, 400, 200)
        
        # جعل النافذة في المقدمة
        window.setWindowFlags(Qt.WindowStaysOnTopHint)
        window.show()
        window.raise_()
        window.activateWindow()
        
        print("✅ تم إنشاء النافذة بنجاح")
        print("💡 يجب أن ترى نافذة اختبار على الشاشة")
        print("🔄 إغلاق النافذة خلال 5 ثوان...")
        
        # إغلاق تلقائي بعد 5 ثوان
        from PyQt5.QtCore import QTimer
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(5000)  # 5 ثوان
        
        # تشغيل التطبيق
        app.exec_()
        
        print("✅ اختبار الواجهة الرسومية مكتمل")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد PyQt5: {e}")
        print("💡 الحل: pip install PyQt5")
        return False
    except Exception as e:
        print(f"❌ خطأ في الواجهة الرسومية: {e}")
        print("💡 استخدم الواجهة النصية بدلاً من ذلك")
        return False

def test_console():
    print("\n🧪 اختبار الواجهة النصية...")
    print("-" * 40)
    
    try:
        import sqlite3
        import os
        from datetime import datetime
        
        print("✅ جميع المكتبات الأساسية متاحة")
        
        # اختبار قاعدة البيانات
        conn = sqlite3.connect(':memory:')  # قاعدة بيانات مؤقتة
        cursor = conn.cursor()
        
        cursor.execute('CREATE TABLE test (id INTEGER, name TEXT)')
        cursor.execute('INSERT INTO test VALUES (1, "اختبار")')
        cursor.execute('SELECT * FROM test')
        result = cursor.fetchone()
        
        conn.close()
        
        if result:
            print("✅ قاعدة البيانات تعمل بشكل صحيح")
        
        print("✅ الواجهة النصية جاهزة للاستخدام")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الواجهة النصية: {e}")
        return False

if __name__ == "__main__":
    print("🎯 اختبار أنظمة الواجهة")
    print("=" * 50)
    
    # اختبار الواجهة النصية أولاً
    console_ok = test_console()
    
    # اختبار الواجهة الرسومية
    gui_ok = test_gui()
    
    print("\n" + "=" * 50)
    print("📋 نتائج الاختبار:")
    print(f"  🖥️ الواجهة النصية: {'✅ تعمل' if console_ok else '❌ لا تعمل'}")
    print(f"  🎨 الواجهة الرسومية: {'✅ تعمل' if gui_ok else '❌ لا تعمل'}")
    
    print("\n💡 التوصيات:")
    if console_ok and not gui_ok:
        print("  🔸 استخدم الواجهة النصية: python text_interface.py")
        print("  🔸 لحل مشكلة الواجهة الرسومية: pip install --upgrade PyQt5")
    elif console_ok and gui_ok:
        print("  🔸 يمكنك استخدام أي واجهة")
        print("  🔸 الواجهة الرسومية: python master_control_panel.py")
        print("  🔸 الواجهة النصية: python text_interface.py")
    else:
        print("  🔸 تحقق من تثبيت Python والمكتبات")
    
    print("=" * 50)

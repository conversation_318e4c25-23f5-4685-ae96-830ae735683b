#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تحسين ارتفاع مربعات النص في البرنامج
إضافة طبقة عامة لتحسين مظهر جميع مربعات النص
"""

import sys
from PyQt5.QtWidgets import QApplication, QStyleFactory
from PyQt5.QtCore import QTimer

def setup_global_text_input_styles():
    """تطبيق تنسيق شامل لجميع مربعات النص"""
    
    # النمط الشامل لجميع مربعات النص
    global_style = """
    /* نمط عام لجميع مربعات النص */
    QLineEdit {
        min-height: 40px;
        max-height: 50px;
        padding: 12px;
        border: 2px solid #ddd;
        border-radius: 8px;
        font-size: 14px;
        font-family: '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON><PERSON>, Geneva, Verdana, sans-serif;
        background-color: #ffffff;
        color: #333333;
    }
    
    QLineEdit:focus {
        border-color: #4CAF50;
        background-color: #f9f9f9;
    }
    
    QLineEdit:hover {
        border-color: #999999;
    }
    
    QLineEdit[readOnly="true"] {
        background-color: #f5f5f5;
        color: #666666;
    }
    
    /* نمط مربعات النص الكبيرة */
    QTextEdit {
        min-height: 120px;
        padding: 12px;
        border: 2px solid #ddd;
        border-radius: 8px;
        font-size: 14px;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background-color: #ffffff;
        color: #333333;
        line-height: 1.4;
    }
    
    QTextEdit:focus {
        border-color: #4CAF50;
        background-color: #f9f9f9;
    }
    
    QTextEdit:hover {
        border-color: #999999;
    }
    
    QTextEdit[readOnly="true"] {
        background-color: #f5f5f5;
        color: #666666;
    }
    
    /* نمط مربعات النص في النوافذ المحدثة */
    QWidget[class="modern"] QLineEdit {
        min-height: 45px;
        max-height: 55px;
        padding: 15px;
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        font-size: 15px;
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                    stop: 0 #ffffff, stop: 1 #f8f8f8);
    }
    
    QWidget[class="modern"] QLineEdit:focus {
        border-color: #2196F3;
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                    stop: 0 #ffffff, stop: 1 #f0f8ff);
    }
    
    QWidget[class="modern"] QTextEdit {
        min-height: 150px;
        padding: 15px;
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        font-size: 15px;
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                    stop: 0 #ffffff, stop: 1 #f8f8f8);
    }
    
    QWidget[class="modern"] QTextEdit:focus {
        border-color: #2196F3;
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                    stop: 0 #ffffff, stop: 1 #f0f8ff);
    }
    
    /* نمط مربعات البحث */
    QLineEdit[class="search"] {
        min-height: 45px;
        max-height: 55px;
        padding: 15px 20px;
        border: 2px solid #e0e0e0;
        border-radius: 25px;
        font-size: 16px;
        background-color: #f9f9f9;
    }
    
    QLineEdit[class="search"]:focus {
        border-color: #FF9800;
        background-color: #ffffff;
    }
    
    /* نمط مربعات النص في النماذج */
    QLineEdit[class="form"] {
        min-height: 42px;
        max-height: 52px;
        padding: 14px;
        border: 2px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        background-color: #ffffff;
    }
    
    QLineEdit[class="form"]:focus {
        border-color: #673AB7;
        background-color: #fafafa;
    }
    
    /* نمط مربعات النص في التقارير */
    QTextEdit[class="report"] {
        min-height: 300px;
        padding: 20px;
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        font-size: 14px;
        background-color: #ffffff;
        font-family: 'Courier New', monospace;
    }
    
    /* نمط مربعات النص الصغيرة */
    QLineEdit[class="small"] {
        min-height: 35px;
        max-height: 40px;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 13px;
    }
    
    /* نمط مربعات النص الكبيرة */
    QLineEdit[class="large"] {
        min-height: 50px;
        max-height: 60px;
        padding: 18px;
        border: 3px solid #ddd;
        border-radius: 12px;
        font-size: 16px;
    }
    """
    
    return global_style

def apply_text_input_improvements():
    """تطبيق التحسينات على مربعات النص"""
    
    if QApplication.instance() is None:
        print("⚠️  لا يوجد تطبيق Qt قيد التشغيل")
        return
    
    app = QApplication.instance()
    
    # تطبيق النمط الشامل
    global_style = setup_global_text_input_styles()
    current_style = app.styleSheet()
    
    # دمج الأنماط
    if current_style:
        combined_style = current_style + "\n\n" + global_style
    else:
        combined_style = global_style
    
    app.setStyleSheet(combined_style)
    
    print("✅ تم تطبيق تحسينات مربعات النص بنجاح!")
    print("📏 الارتفاع الافتراضي لـ QLineEdit: 40-50px")
    print("📏 الارتفاع الافتراضي لـ QTextEdit: 120px+")
    print("🎨 تم تحسين الألوان والحدود")
    print("📱 تم تحسين التجاوب مع المستخدم")

def get_recommended_heights():
    """الحصول على الارتفاعات المُوصى بها"""
    
    recommendations = {
        "QLineEdit": {
            "صغير": "35-40px",
            "عادي": "40-50px", 
            "كبير": "50-60px",
            "البحث": "45-55px"
        },
        "QTextEdit": {
            "صغير": "80-100px",
            "عادي": "120-200px",
            "كبير": "200-400px",
            "التقارير": "300px+"
        },
        "QDateEdit": {
            "عادي": "40-50px"
        },
        "QSpinBox": {
            "عادي": "40-50px"
        }
    }
    
    return recommendations

def print_recommendations():
    """طباعة التوصيات"""
    
    recommendations = get_recommended_heights()
    
    print("\n" + "="*60)
    print("🎯 توصيات ارتفاع مربعات النص")
    print("="*60)
    
    for widget_type, sizes in recommendations.items():
        print(f"\n📋 {widget_type}:")
        for size_name, height in sizes.items():
            print(f"   • {size_name}: {height}")
    
    print("\n" + "="*60)
    print("💡 نصائح إضافية:")
    print("   • استخدم padding مناسب (12-18px)")
    print("   • استخدم border-radius للحصول على مظهر حديث")
    print("   • تأكد من وضوح النص بألوان مناسبة")
    print("   • اختبر على أحجام شاشة مختلفة")
    print("="*60)

if __name__ == "__main__":
    print("🔧 أداة تحسين ارتفاع مربعات النص")
    print("="*50)
    
    # طباعة التوصيات
    print_recommendations()
    
    # إذا كان هناك تطبيق Qt يعمل، طبق التحسينات
    if QApplication.instance() is not None:
        apply_text_input_improvements()
    else:
        print("\n⚠️  لتطبيق التحسينات، قم بتشغيل هذا المكون مع تطبيق Qt")
        print("   مثال: من داخل main_window.py أو run_app.py")
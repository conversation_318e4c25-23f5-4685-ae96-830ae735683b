#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
النظام النهائي المحسّن - مقاسات مناسبة + إدراج يدوي كامل للأيام والساعات
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime
import json
import os

class CompactDialog(QDialog):
    """نافذة حوار مدمجة بمقاسات صغيرة ومريحة"""
    
    def __init__(self, title, width=350, height=280):
        super().__init__()
        self.setWindowTitle(title)
        self.setFixedSize(width, height)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        self.move(x, y)
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد الواجهة الأساسية"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(8, 8, 8, 8)
        self.main_layout.setSpacing(4)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Sakkal Majalla", "Arial", sans-serif;
            }
        """)
    
    def create_form_row(self, label_text, widget):
        """إنشاء صف في النموذج بحجم مدمج"""
        row_widget = QWidget()
        row_layout = QHBoxLayout(row_widget)
        row_layout.setContentsMargins(1, 1, 1, 1)
        row_layout.setSpacing(4)
        
        # التسمية
        label = QLabel(label_text)
        label.setFixedWidth(70)
        label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 10px;
                font-weight: bold;
                color: #2c3e50;
                padding: 1px;
            }
        """)
        
        # تطبيق الأنماط على العناصر
        if isinstance(widget, (QLineEdit, QSpinBox)):
            widget.setLayoutDirection(Qt.RightToLeft)
            widget.setFixedHeight(22)
            widget.setStyleSheet("""
                QLineEdit, QSpinBox {
                    font-family: "Sakkal Majalla", "Arial", sans-serif;
                    background: white;
                    border: 1px solid #bdc3c7;
                    border-radius: 3px;
                    padding: 3px;
                    font-size: 9px;
                    color: #2c3e50;
                }
                QLineEdit:focus, QSpinBox:focus {
                    border: 2px solid #3498db;
                    background: #f8f9fa;
                }
            """)
        
        row_layout.addWidget(label)
        row_layout.addWidget(widget)
        
        self.main_layout.addWidget(row_widget)
    
    def add_button(self, text, style_type, callback):
        """إضافة زر صغير"""
        button = QPushButton(text)
        button.setFixedHeight(24)
        button.setFixedWidth(70)
        
        colors = {
            "primary": ("#27ae60", "#2ecc71"),
            "secondary": ("#95a5a6", "#bdc3c7"),
            "danger": ("#e74c3c", "#c0392b")
        }
        
        color, hover_color = colors.get(style_type, colors["secondary"])
        
        button.setStyleSheet(f"""
            QPushButton {{
                background: {color};
                border: none;
                border-radius: 4px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 9px;
                font-weight: bold;
                padding: 3px;
            }}
            QPushButton:hover {{
                background: {hover_color};
            }}
        """)
        
        button.clicked.connect(callback)
        
        # إضافة الزر في layout أفقي
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(button)
        button_layout.addStretch()
        
        self.main_layout.addLayout(button_layout)

class FinalDailyVacationWindow(CompactDialog):
    """نافذة طلب الإجازة اليومية النهائية المحسّنة"""
    
    def __init__(self):
        super().__init__("📅 إجازة يومية", 360, 300)
        self.setup_content()
        
    def setup_content(self):
        """إعداد محتوى النافذة"""
        
        # العنوان
        title_label = QLabel("📅 طلب إجازة يومية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #e74c3c, stop: 1 #c0392b);
                border-radius: 4px;
                padding: 6px;
                margin: 1px;
            }
        """)
        self.main_layout.addWidget(title_label)
        
        # اسم الموظف (إدراج يدوي)
        self.employee_name = QLineEdit()
        self.employee_name.setPlaceholderText("اسم الموظف...")
        self.create_form_row("👤 الموظف:", self.employee_name)
        
        # تاريخ البداية (إدراج يدوي)
        self.start_date = QLineEdit()
        self.start_date.setPlaceholderText("2024-12-01")
        self.create_form_row("📅 من:", self.start_date)
        
        # تاريخ النهاية (إدراج يدوي)
        self.end_date = QLineEdit()
        self.end_date.setPlaceholderText("2024-12-03")
        self.create_form_row("📅 إلى:", self.end_date)
        
        # عدد الأيام (إدراج يدوي)
        self.days_count = QSpinBox()
        self.days_count.setMinimum(1)
        self.days_count.setMaximum(365)
        self.days_count.setValue(1)
        self.days_count.setSuffix(" يوم")
        self.create_form_row("📊 الأيام:", self.days_count)
        
        # نوع الإجازة (إدراج يدوي)
        self.vacation_type = QLineEdit()
        self.vacation_type.setPlaceholderText("إجازة اعتيادية...")
        self.create_form_row("📋 النوع:", self.vacation_type)
        
        # السبب
        self.reason = QLineEdit()
        self.reason.setPlaceholderText("السبب...")
        self.create_form_row("📝 السبب:", self.reason)
        
        # ملاحظات
        self.notes = QLineEdit()
        self.notes.setPlaceholderText("ملاحظات...")
        self.create_form_row("📄 ملاحظة:", self.notes)
        
        # مساحة فارغة
        self.main_layout.addStretch()
        
        # معلومات إرشادية
        info_label = QLabel("💡 إدراج يدوي لجميع البيانات")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 8px;
                color: #7f8c8d;
                background: #ecf0f1;
                border-radius: 2px;
                padding: 3px;
            }
        """)
        self.main_layout.addWidget(info_label)
        
        # الأزرار
        self.add_button("💾 حفظ", "primary", self.save_request)
        self.add_button("❌ إلغاء", "secondary", self.reject)
        
    def save_request(self):
        """حفظ طلب الإجازة"""
        # التحقق من البيانات
        if not self.employee_name.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الموظف!")
            return
            
        if not self.vacation_type.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال نوع الإجازة!")
            return
        
        # إعداد البيانات
        vacation_data = {
            "type": "daily",
            "employee_name": self.employee_name.text().strip(),
            "start_date": self.start_date.text().strip(),
            "end_date": self.end_date.text().strip(),
            "days_count_manual": self.days_count.value(),
            "vacation_type": self.vacation_type.text().strip(),
            "reason": self.reason.text().strip(),
            "notes": self.notes.text().strip(),
            "request_datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # حفظ البيانات
        self.save_data(vacation_data)
        
        # رسالة النجاح
        QMessageBox.information(self, "✅ تم", 
            f"حُفظ الطلب!\n\n"
            f"الموظف: {vacation_data['employee_name']}\n"
            f"الأيام: {vacation_data['days_count_manual']} يوم")
        
        self.accept()
    
    def save_data(self, data):
        """حفظ البيانات في ملف JSON"""
        try:
            filename = "vacation_final.json"
            
            if os.path.exists(filename):
                with open(filename, "r", encoding="utf-8") as f:
                    requests = json.load(f)
            else:
                requests = []
            
            requests.append(data)
            
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(requests, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الحفظ:\n{e}")

class FinalHourlyVacationWindow(CompactDialog):
    """نافذة طلب الإجازة الساعية النهائية المحسّنة"""
    
    def __init__(self):
        super().__init__("⏱️ إجازة ساعية", 360, 280)
        self.setup_content()
        
    def setup_content(self):
        """إعداد محتوى النافذة"""
        
        # العنوان
        title_label = QLabel("⏱️ طلب إجازة ساعية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #f39c12, stop: 1 #e67e22);
                border-radius: 4px;
                padding: 6px;
                margin: 1px;
            }
        """)
        self.main_layout.addWidget(title_label)
        
        # اسم الموظف (إدراج يدوي)
        self.employee_name = QLineEdit()
        self.employee_name.setPlaceholderText("اسم الموظف...")
        self.create_form_row("👤 الموظف:", self.employee_name)
        
        # التاريخ (إدراج يدوي)
        self.date = QLineEdit()
        self.date.setPlaceholderText("2024-12-01")
        self.create_form_row("📅 التاريخ:", self.date)
        
        # وقت البداية (إدراج يدوي)
        self.start_time = QLineEdit()
        self.start_time.setPlaceholderText("09:00")
        self.create_form_row("🕘 من:", self.start_time)
        
        # وقت النهاية (إدراج يدوي)
        self.end_time = QLineEdit()
        self.end_time.setPlaceholderText("11:00")
        self.create_form_row("🕐 إلى:", self.end_time)
        
        # عدد الساعات (إدراج يدوي)
        self.hours_count = QSpinBox()
        self.hours_count.setMinimum(1)
        self.hours_count.setMaximum(24)
        self.hours_count.setValue(2)
        self.hours_count.setSuffix(" ساعة")
        self.create_form_row("⏰ الساعات:", self.hours_count)
        
        # نوع الإجازة (إدراج يدوي)
        self.vacation_type = QLineEdit()
        self.vacation_type.setPlaceholderText("موعد طبي...")
        self.create_form_row("📋 النوع:", self.vacation_type)
        
        # السبب
        self.reason = QLineEdit()
        self.reason.setPlaceholderText("السبب...")
        self.create_form_row("📝 السبب:", self.reason)
        
        # مساحة فارغة
        self.main_layout.addStretch()
        
        # معلومات إرشادية
        info_label = QLabel("💡 إدراج يدوي لجميع البيانات")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 8px;
                color: #7f8c8d;
                background: #ecf0f1;
                border-radius: 2px;
                padding: 3px;
            }
        """)
        self.main_layout.addWidget(info_label)
        
        # الأزرار
        self.add_button("💾 حفظ", "primary", self.save_request)
        self.add_button("❌ إلغاء", "secondary", self.reject)
        
    def save_request(self):
        """حفظ طلب الإجازة الساعية"""
        # التحقق من البيانات
        if not self.employee_name.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الموظف!")
            return
            
        if not self.vacation_type.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال نوع الإجازة!")
            return
        
        # إعداد البيانات
        vacation_data = {
            "type": "hourly",
            "employee_name": self.employee_name.text().strip(),
            "date": self.date.text().strip(),
            "start_time": self.start_time.text().strip(),
            "end_time": self.end_time.text().strip(),
            "hours_count_manual": self.hours_count.value(),
            "vacation_type": self.vacation_type.text().strip(),
            "reason": self.reason.text().strip(),
            "request_datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # حفظ البيانات
        self.save_data(vacation_data)
        
        # رسالة النجاح
        QMessageBox.information(self, "✅ تم", 
            f"حُفظ الطلب!\n\n"
            f"الموظف: {vacation_data['employee_name']}\n"
            f"الساعات: {vacation_data['hours_count_manual']} ساعة")
        
        self.accept()
    
    def save_data(self, data):
        """حفظ البيانات في ملف JSON"""
        try:
            filename = "vacation_final.json"
            
            if os.path.exists(filename):
                with open(filename, "r", encoding="utf-8") as f:
                    requests = json.load(f)
            else:
                requests = []
            
            requests.append(data)
            
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(requests, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الحفظ:\n{e}")

class TinyLoginWindow(QDialog):
    """نافذة تسجيل دخول صغيرة جداً"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔐 دخول")
        self.setFixedSize(200, 100)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - 200) // 2
        y = (screen.height() - 100) // 2
        self.move(x, y)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة تسجيل الدخول"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 5, 8, 5)
        layout.setSpacing(3)
        
        # العنوان
        title = QLabel("🔐 دخول")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 10px;
                font-weight: bold;
                color: white;
                background: #3498db;
                border-radius: 3px;
                padding: 4px;
            }
        """)
        layout.addWidget(title)
        
        # اسم المستخدم
        self.username = QLineEdit()
        self.username.setPlaceholderText("المستخدم")
        self.username.setText("admin")
        self.username.setFixedHeight(18)
        self.username.setLayoutDirection(Qt.RightToLeft)
        self.username.setStyleSheet("""
            QLineEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                border: 1px solid #bdc3c7;
                border-radius: 2px;
                padding: 2px;
                font-size: 8px;
                background: white;
            }
        """)
        layout.addWidget(self.username)
        
        # كلمة المرور
        self.password = QLineEdit()
        self.password.setPlaceholderText("كلمة المرور")
        self.password.setText("admin123")
        self.password.setEchoMode(QLineEdit.Password)
        self.password.setFixedHeight(18)
        self.password.setLayoutDirection(Qt.RightToLeft)
        self.password.setStyleSheet(self.username.styleSheet())
        layout.addWidget(self.password)
        
        # زر الدخول
        login_btn = QPushButton("✅ دخول")
        login_btn.setFixedHeight(20)
        login_btn.setStyleSheet("""
            QPushButton {
                background: #27ae60;
                border: none;
                border-radius: 2px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2ecc71;
            }
        """)
        login_btn.clicked.connect(self.login)
        layout.addWidget(login_btn)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }
        """)
    
    def login(self):
        """عملية تسجيل الدخول"""
        if self.username.text() == "admin" and self.password.text() == "admin123":
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ", "بيانات خاطئة!")

class FinalMainWindow(QMainWindow):
    """النافذة الرئيسية النهائية المدمجة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🏢 نظام الإجازات النهائي")
        self.setGeometry(100, 100, 320, 220)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # توسيط النافذة
        screen = QApplication.instance().primaryScreen().geometry()
        x = (screen.width() - 320) // 2
        y = (screen.height() - 220) // 2
        self.move(x, y)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد الواجهة الرئيسية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(6)
        
        # العنوان
        title = QLabel("🏢 نظام إدارة الإجازات")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2c3e50, stop: 1 #34495e);
                border-radius: 5px;
                padding: 8px;
            }
        """)
        layout.addWidget(title)
        
        # وصف النظام
        desc = QLabel("📝 إدراج يدوي + مقاسات مناسبة")
        desc.setAlignment(Qt.AlignCenter)
        desc.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 9px;
                color: #7f8c8d;
                padding: 2px;
            }
        """)
        layout.addWidget(desc)
        
        # الأزرار الرئيسية
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(4)
        
        # زر الإجازة اليومية
        daily_btn = self.create_main_button(
            "📅 إجازة يومية", 
            "#e74c3c",
            self.open_daily_vacation
        )
        buttons_layout.addWidget(daily_btn)
        
        # زر الإجازة الساعية
        hourly_btn = self.create_main_button(
            "⏱️ إجازة ساعية", 
            "#f39c12",
            self.open_hourly_vacation
        )
        buttons_layout.addWidget(hourly_btn)
        
        # زر عرض الطلبات
        view_btn = self.create_main_button(
            "📋 عرض الطلبات",
            "#27ae60", 
            self.view_requests
        )
        buttons_layout.addWidget(view_btn)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        # معلومات النظام
        info = QLabel("💡 مقاسات صغيرة + إدراج يدوي لكل شيء")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 7px;
                color: #95a5a6;
                background: #ecf0f1;
                border-radius: 2px;
                padding: 3px;
            }
        """)
        layout.addWidget(info)
        
        # الأنماط العامة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }
        """)
    
    def create_main_button(self, title, color, callback):
        """إنشاء زر رئيسي صغير"""
        button = QPushButton(title)
        button.setFixedHeight(30)
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color}, stop: 1 rgba(0,0,0,0.1));
                border: none;
                border-radius: 4px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 10px;
                font-weight: bold;
                text-align: center;
                padding: 4px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 rgba(255,255,255,0.2), stop: 1 {color});
            }}
        """)
        
        button.clicked.connect(callback)
        return button
    
    def open_daily_vacation(self):
        """فتح نافذة الإجازة اليومية"""
        window = FinalDailyVacationWindow()
        window.exec_()
    
    def open_hourly_vacation(self):
        """فتح نافذة الإجازة الساعية"""
        window = FinalHourlyVacationWindow()
        window.exec_()
    
    def view_requests(self):
        """عرض الطلبات المحفوظة"""
        try:
            filename = "vacation_final.json"
            
            if not os.path.exists(filename):
                QMessageBox.information(self, "معلومات", "لا توجد طلبات!")
                return
            
            with open(filename, "r", encoding="utf-8") as f:
                requests = json.load(f)
            
            if not requests:
                QMessageBox.information(self, "معلومات", "لا توجد طلبات!")
                return
            
            # عرض الطلبات
            self.show_requests_window(requests)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في القراءة:\n{e}")
    
    def show_requests_window(self, requests):
        """عرض نافذة الطلبات مدمجة"""
        window = QDialog(self)
        window.setWindowTitle("📋 الطلبات")
        window.setFixedSize(380, 250)
        window.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout(window)
        layout.setContentsMargins(8, 8, 8, 8)
        
        # العنوان
        title = QLabel(f"📋 عدد الطلبات: {len(requests)}")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 10px;
                font-weight: bold;
                color: white;
                background: #3498db;
                border-radius: 3px;
                padding: 4px;
            }
        """)
        layout.addWidget(title)
        
        # جدول الطلبات
        table = QTextEdit()
        table.setReadOnly(True)
        table.setLayoutDirection(Qt.RightToLeft)
        table.setStyleSheet("""
            QTextEdit {
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 2px;
                padding: 4px;
                background: white;
            }
        """)
        
        # تنسيق النصوص
        content = ""
        for i, request in enumerate(requests, 1):
            content += f"طلب {i}:\n"
            content += f"👤 {request.get('employee_name', 'غير محدد')}\n"
            
            if request.get('type') == 'hourly':
                content += f"⏱️ ساعية - {request.get('hours_count_manual', 0)} ساعة\n"
                content += f"📅 {request.get('date', 'غير محدد')}\n"
            else:
                content += f"📅 يومية - {request.get('days_count_manual', 0)} يوم\n"
                content += f"📅 {request.get('start_date', 'غير محدد')} إلى {request.get('end_date', 'غير محدد')}\n"
            
            content += f"📋 {request.get('vacation_type', 'غير محدد')}\n"
            content += f"📝 {request.get('reason', 'غير محدد')}\n"
            content += "-" * 25 + "\n\n"
        
        table.setPlainText(content)
        layout.addWidget(table)
        
        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setFixedHeight(20)
        close_btn.setStyleSheet("""
            QPushButton {
                background: #95a5a6;
                border: none;
                border-radius: 2px;
                color: white;
                font-family: "Sakkal Majalla", "Arial", sans-serif;
                font-size: 8px;
                font-weight: bold;
            }
        """)
        close_btn.clicked.connect(window.close)
        layout.addWidget(close_btn)
        
        window.exec_()

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي
    font = QFont("Sakkal Majalla", 7)
    app.setFont(font)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🚀 النظام النهائي المحسّن")
    print("=" * 30)
    print("✅ مقاسات صغيرة ومناسبة تماماً")
    print("✅ إدراج يدوي كامل:")
    print("   👤 أسماء الموظفين")
    print("   📅 التواريخ والأوقات")  
    print("   📊 عدد الأيام (يدوي)")
    print("   ⏰ عدد الساعات (يدوي)")
    print("   📋 أنواع الإجازات")
    print("✅ نوافذ مدمجة جداً")
    print("✅ أزرار وحقول صغيرة")
    print("=" * 30)
    
    # تسجيل الدخول
    login = TinyLoginWindow()
    if login.exec_() == QDialog.Accepted:
        print("✅ تم الدخول!")
        
        # النظام الرئيسي
        main_window = FinalMainWindow()
        main_window.show()
        
        return app.exec_()
    else:
        print("❌ تم الإلغاء")
        return 0

if __name__ == "__main__":
    sys.exit(main())
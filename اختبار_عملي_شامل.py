#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار عملي شامل لنظام إدارة الإجازات
يحاكي الاستخدام الفعلي للنظام
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import VacationDatabase

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "="*80)
    print(f"🔥 {title}")
    print("="*80)

def print_step(step_num, description):
    """طباعة خطوة مع رقم"""
    print(f"\n📋 الخطوة {step_num}: {description}")
    print("-" * 60)

def practical_test():
    """اختبار عملي شامل"""
    
    print_header("اختبار عملي شامل لنظام إدارة الإجازات")
    
    # إنشاء قاعدة بيانات للاختبار
    test_db_name = 'test_practical.db'
    if os.path.exists(test_db_name):
        os.remove(test_db_name)
    
    db = VacationDatabase(test_db_name)
    
    try:
        # الخطوة 1: اختبار تسجيل الدخول
        print_step(1, "اختبار تسجيل الدخول")
        
        # اختبار بيانات خاطئة
        user = db.authenticate_user('wrong', 'wrong')
        if user is None:
            print("✅ رفض البيانات الخاطئة بنجاح")
        else:
            print("❌ قبل بيانات خاطئة!")
            
        # اختبار البيانات الصحيحة
        user = db.authenticate_user('admin', 'admin123')
        if user:
            print(f"✅ تسجيل دخول ناجح: {user['username']}")
        else:
            print("❌ فشل في تسجيل الدخول!")
            return False
        
        # الخطوة 2: استيراد الرصيد الابتدائي
        print_step(2, "استيراد الرصيد الابتدائي من Excel")
        
        excel_file = 'نموذج_الرصيد_الابتدائي.xlsx'
        if os.path.exists(excel_file):
            df = pd.read_excel(excel_file)
            print(f"📊 ملف Excel يحتوي على {len(df)} موظف")
            
            # استيراد البيانات
            imported_count = 0
            for _, row in df.iterrows():
                success, _ = db.import_initial_balance_data(
                    row['الاسم واللقب'],
                    row['الرتبة'], 
                    row['عدد الأيام'],
                    row['التاريخ']
                )
                if success:
                    imported_count += 1
            
            print(f"✅ تم استيراد {imported_count}/{len(df)} موظف بنجاح")
        else:
            print("❌ ملف Excel غير موجود!")
            return False
        
        # الخطوة 3: اختبار طلبات الإجازة اليومية
        print_step(3, "إضافة طلبات إجازة يومية")
        
        daily_requests = [
            ('أحمد محمد علي', '12345', 'موظف', 'الإدارة', 'سنوية', '2024-07-01', 5),
            ('فاطمة عبد الله', '12346', 'مشرف', 'المالية', 'مرضية', '2024-07-05', 3),
            ('محمد عبد الرحمن', '12347', 'موظف', 'التسويق', 'سنوية', '2024-07-10', 7),
        ]
        
        for name, emp_id, position, department, vacation_type, date, days in daily_requests:
            success, message = db.add_daily_request(name, emp_id, position, department, vacation_type, date, days)
            if success:
                print(f"✅ تم إضافة طلب إجازة يومية: {name} - {days} أيام")
            else:
                print(f"❌ فشل في إضافة طلب: {name} - {message}")
        
        # الخطوة 4: اختبار طلبات الإجازة الساعية
        print_step(4, "إضافة طلبات إجازة ساعية")
        
        hourly_requests = [
            ('أحمد محمد علي', '2024-07-15', 8),    # 8 ساعات = 1 يوم
            ('فاطمة عبد الله', '2024-07-16', 4),    # 4 ساعات = 0.5 يوم
            ('عائشة سعيد', '2024-07-17', 12),       # 12 ساعة = 1.5 يوم
        ]
        
        for name, date, hours in hourly_requests:
            success, message = db.add_hourly_request(name, date, hours)
            days_equivalent = (hours * 3) / 24
            if success:
                print(f"✅ تم إضافة طلب إجازة ساعية: {name} - {hours} ساعة ({days_equivalent:.2f} يوم)")
            else:
                print(f"❌ فشل في إضافة طلب: {name} - {message}")
        
        # الخطوة 5: إدراج إجازات إضافية
        print_step(5, "إدراج إجازات إضافية")
        
        additional_vacations = [
            ('أحمد محمد علي', '2024-07-20', 5, 'مكافأة أداء ممتاز'),
            ('محمد عبد الرحمن', '2024-07-22', 3, 'تعويض عمل إضافي'),
        ]
        
        for name, date, days, reason in additional_vacations:
            success, message = db.add_vacation(name, date, days, reason)
            if success:
                print(f"✅ تم إدراج إجازة: {name} - {days} أيام - {reason}")
            else:
                print(f"❌ فشل في إدراج إجازة: {name} - {message}")
        
        # الخطوة 6: حساب الأرصدة والتقارير
        print_step(6, "حساب الأرصدة وتوليد التقارير")
        
        # قائمة الموظفين للاختبار
        test_employees = ['أحمد محمد علي', 'فاطمة عبد الله', 'محمد عبد الرحمن', 'عائشة سعيد']
        
        for employee in test_employees:
            balance = db.get_employee_balance(employee)
            if balance:
                print(f"\n👤 الموظف: {employee}")
                print(f"   📊 الرصيد الابتدائي: {balance['initial_balance']} يوم")
                print(f"   ➕ الإجازات المدرجة: {balance['added_vacations']} يوم")
                print(f"   📝 الإجازات اليومية: {balance['daily_used']} يوم")
                print(f"   ⏱️  الإجازات الساعية: {balance['hourly_used']} يوم")
                print(f"   💰 الرصيد الصافي: {balance['net_balance']} يوم")
                
                # تحديد حالة الرصيد
                if balance['net_balance'] > 0:
                    print(f"   ✅ الحالة: رصيد إيجابي")
                elif balance['net_balance'] == 0:
                    print(f"   ⚖️  الحالة: رصيد متوازن")
                else:
                    print(f"   ⚠️  الحالة: عجز في الرصيد")
        
        # الخطوة 7: اختبار البحث والاستعلام
        print_step(7, "اختبار البحث والاستعلام")
        
        search_results = db.search_employee('أحمد')
        if search_results:
            print(f"🔍 نتائج البحث عن 'أحمد': {len(search_results)} نتيجة")
            for employee in search_results:
                print(f"   - {employee}")
        else:
            print("❌ لم يتم العثور على نتائج")
        
        # الخطوة 8: اختبار إدارة البيانات
        print_step(8, "اختبار عرض وإدارة البيانات")
        
        # عرض طلبات الإجازة اليومية
        daily_requests_data = db.get_all_daily_requests()
        print(f"📋 عدد طلبات الإجازة اليومية: {len(daily_requests_data)}")
        
        # عرض طلبات الإجازة الساعية
        hourly_requests_data = db.get_all_hourly_requests()
        print(f"⏱️  عدد طلبات الإجازة الساعية: {len(hourly_requests_data)}")
        
        # عرض الإجازات المدرجة
        added_vacations_data = db.get_all_added_vacations()
        print(f"➕ عدد الإجازات المدرجة: {len(added_vacations_data)}")
        
        # الخطوة 9: اختبار إحصائيات شاملة
        print_step(9, "إحصائيات شاملة للنظام")
        
        # حساب إجمالي الإجازات
        total_daily = sum([req.get('عدد الأيام', 0) for req in daily_requests_data])
        total_hourly = sum([req.get('المعادل بالأيام', 0) for req in hourly_requests_data])
        total_added = sum([vac.get('عدد الأيام', 0) for vac in added_vacations_data])
        
        print(f"📊 إحصائيات شاملة:")
        print(f"   📝 إجمالي الإجازات اليومية: {total_daily} يوم")
        print(f"   ⏱️  إجمالي الإجازات الساعية: {total_hourly:.2f} يوم")
        print(f"   ➕ إجمالي الإجازات المدرجة: {total_added} يوم")
        print(f"   📈 صافي الاستهلاك: {total_daily + total_hourly - total_added:.2f} يوم")
        
        # النتيجة النهائية
        print_header("نتائج الاختبار العملي")
        
        print("🎉 تم اختبار جميع وظائف النظام بنجاح!")
        print("✅ جميع العمليات تعمل بشكل صحيح")
        print("✅ قاعدة البيانات تحفظ وتسترجع البيانات بدقة")
        print("✅ الحسابات الرياضية صحيحة")
        print("✅ النظام جاهز للاستخدام العملي")
        
        print("\n🚀 لتشغيل البرنامج فعلياً:")
        print("   🖱️  انقر نقراً مزدوجاً على: تشغيل_البرنامج.bat")
        print("   💻 أو من سطر الأوامر: python run_app.py")
        
        print("\n📋 بيانات الدخول:")
        print("   👤 اسم المستخدم: admin")
        print("   🔐 كلمة المرور: admin123")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ أثناء الاختبار: {e}")
        return False
        
    finally:
        # تنظيف ملف الاختبار
        if os.path.exists(test_db_name):
            os.remove(test_db_name)
            print(f"\n🧹 تم حذف ملف الاختبار: {test_db_name}")

if __name__ == '__main__':
    practical_test()
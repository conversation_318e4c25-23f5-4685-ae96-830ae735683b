// qgeosatelliteinfo.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QGeoSatelliteInfo
{
%TypeHeaderCode
#include <qgeosatelliteinfo.h>
%End

public:
    enum Attribute
    {
        Elevation,
        Azimuth,
    };

    enum SatelliteSystem
    {
        Undefined,
        GPS,
        GLONASS,
    };

    QGeoSatelliteInfo();
    QGeoSatelliteInfo(const QGeoSatelliteInfo &other);
    ~QGeoSatelliteInfo();
    bool operator==(const QGeoSatelliteInfo &other) const;
    bool operator!=(const QGeoSatelliteInfo &other) const;
    void setSatelliteSystem(QGeoSatelliteInfo::SatelliteSystem system);
    QGeoSatelliteInfo::SatelliteSystem satelliteSystem() const;
    void setSatelliteIdentifier(int satId);
    int satelliteIdentifier() const;
    void setSignalStrength(int signalStrength);
    int signalStrength() const;
    void setAttribute(QGeoSatelliteInfo::Attribute attribute, qreal value);
    qreal attribute(QGeoSatelliteInfo::Attribute attribute) const;
    void removeAttribute(QGeoSatelliteInfo::Attribute attribute);
    bool hasAttribute(QGeoSatelliteInfo::Attribute attribute) const;
};

%End
%If (Qt_5_2_0 -)
QDataStream &operator<<(QDataStream &stream, const QGeoSatelliteInfo &info /Constrained/);
%End
%If (Qt_5_2_0 -)
QDataStream &operator>>(QDataStream &stream, QGeoSatelliteInfo &info /Constrained/);
%End

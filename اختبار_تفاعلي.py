






#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تفاعلي لنظام إدارة الإجازات
محاكاة الاستخدام الفعلي للنظام
"""

import sys
import os
import time
from datetime import datetime, timedelta
import pandas as pd

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import VacationDatabase

class VacationSystemTester:
    """فئة اختبار نظام إدارة الإجازات"""
    
    def __init__(self):
        self.db = VacationDatabase()
        self.current_user = None
        
    def print_header(self, title):
        """طباعة عنوان مع تنسيق"""
        print("\n" + "="*80)
        print(f"🎯 {title}")
        print("="*80)
    
    def print_success(self, message):
        """طباعة رسالة نجاح"""
        print(f"✅ {message}")
    
    def print_error(self, message):
        """طباعة رسالة خطأ"""
        print(f"❌ {message}")
    
    def print_info(self, message):
        """طباعة رسالة معلومات"""
        print(f"ℹ️  {message}")
    
    def wait_for_input(self, message="اضغط Enter للمتابعة..."):
        """انتظار المستخدم"""
        input(f"\n⏳ {message}")
    
    def test_login(self):
        """اختبار تسجيل الدخول"""
        self.print_header("اختبار تسجيل الدخول")
        
        # اختبار بيانات خاطئة
        print("🔒 اختبار بيانات خاطئة...")
        user = self.db.authenticate_user('wrong', 'wrong')
        if user is None:
            self.print_success("رفض البيانات الخاطئة")
        else:
            self.print_error("قبل بيانات خاطئة!")
            
        # اختبار البيانات الصحيحة
        print("\n🔒 اختبار البيانات الصحيحة...")
        user = self.db.authenticate_user('admin', 'admin123')
        if user:
            self.current_user = user
            self.print_success(f"تم تسجيل الدخول بنجاح - المستخدم: {user['username']}")
            self.print_info(f"الصلاحية: {user['role']}")
            return True
        else:
            self.print_error("فشل في تسجيل الدخول!")
            return False
    
    def test_excel_import(self):
        """اختبار استيراد ملف Excel"""
        self.print_header("اختبار استيراد البيانات من Excel")
        
        excel_file = 'نموذج_الرصيد_الابتدائي.xlsx'
        if not os.path.exists(excel_file):
            self.print_error(f"ملف Excel غير موجود: {excel_file}")
            return False
        
        # قراءة الملف
        try:
            df = pd.read_excel(excel_file)
            self.print_success(f"تم قراءة الملف: {len(df)} سجل")
            
            # عرض عينة من البيانات
            print("\n📊 عينة من البيانات المستوردة:")
            print(df.head().to_string(index=False))
            
            # استيراد البيانات
            print("\n📥 جاري استيراد البيانات...")
            success, message = self.db.import_initial_balance(excel_file)
            
            if success:
                self.print_success(f"تم استيراد البيانات: {message}")
                return True
            else:
                self.print_error(f"فشل في الاستيراد: {message}")
                return False
                
        except Exception as e:
            self.print_error(f"خطأ في قراءة الملف: {e}")
            return False
    
    def test_daily_vacation_requests(self):
        """اختبار طلبات الإجازة اليومية"""
        self.print_header("اختبار طلبات الإجازة اليومية")
        
        # قائمة طلبات الاختبار
        test_requests = [
            {
                'name': 'أحمد محمد علي',
                'emp_id': '12345',
                'position': 'موظف',
                'department': 'الإدارة',
                'vacation_type': 'سنوية',
                'start_date': '2024-07-01',
                'days': 5
            },
            {
                'name': 'فاطمة عبد الله',
                'emp_id': '12346',
                'position': 'مشرف',
                'department': 'المالية',
                'vacation_type': 'مرضية',
                'start_date': '2024-07-05',
                'days': 3
            },
            {
                'name': 'محمد عبد الرحمن',
                'emp_id': '12347',
                'position': 'موظف',
                'department': 'التسويق',
                'vacation_type': 'سنوية',
                'start_date': '2024-07-10',
                'days': 7
            }
        ]
        
        successful_requests = 0
        
        for i, request in enumerate(test_requests, 1):
            print(f"\n📝 طلب رقم {i}:")
            print(f"   👤 الموظف: {request['name']}")
            print(f"   🏢 القسم: {request['department']}")
            print(f"   📅 التاريخ: {request['start_date']}")
            print(f"   🕐 المدة: {request['days']} أيام")
            print(f"   📋 النوع: {request['vacation_type']}")
            
            success, message = self.db.add_daily_request(
                request['name'],
                request['emp_id'],
                request['position'],
                request['department'],
                request['vacation_type'],
                request['start_date'],
                request['days']
            )
            
            if success:
                self.print_success(f"تم حفظ الطلب")
                successful_requests += 1
            else:
                self.print_error(f"فشل في حفظ الطلب: {message}")
        
        self.print_info(f"تم حفظ {successful_requests}/{len(test_requests)} طلب بنجاح")
        return successful_requests == len(test_requests)
    
    def test_hourly_vacation_requests(self):
        """اختبار طلبات الإجازة الساعية"""
        self.print_header("اختبار طلبات الإجازة الساعية")
        
        # قائمة طلبات الاختبار
        test_requests = [
            ('أحمد محمد علي', '2024-07-15', 8),    # 8 ساعات = 1 يوم
            ('فاطمة عبد الله', '2024-07-16', 4),    # 4 ساعات = 0.5 يوم
            ('عائشة سعيد', '2024-07-17', 12),       # 12 ساعة = 1.5 يوم
            ('عبد الله أحمد', '2024-07-18', 6),     # 6 ساعات = 0.75 يوم
        ]
        
        successful_requests = 0
        
        for i, (name, date, hours) in enumerate(test_requests, 1):
            # حساب المعادل
            days_equivalent = (hours * 3) / 24
            
            print(f"\n⏰ طلب رقم {i}:")
            print(f"   👤 الموظف: {name}")
            print(f"   📅 التاريخ: {date}")
            print(f"   🕐 عدد الساعات: {hours}")
            print(f"   📊 المعادل بالأيام: {days_equivalent:.2f}")
            
            success, message = self.db.add_hourly_request(name, date, hours)
            
            if success:
                self.print_success(f"تم حفظ الطلب")
                successful_requests += 1
            else:
                self.print_error(f"فشل في حفظ الطلب: {message}")
        
        self.print_info(f"تم حفظ {successful_requests}/{len(test_requests)} طلب بنجاح")
        return successful_requests == len(test_requests)
    
    def test_additional_vacations(self):
        """اختبار إدراج الإجازات الإضافية"""
        self.print_header("اختبار إدراج الإجازات الإضافية")
        
        # قائمة الإجازات الإضافية
        additional_vacations = [
            ('أحمد محمد علي', '2024-07-20', 5, 'مكافأة أداء ممتاز'),
            ('محمد عبد الرحمن', '2024-07-22', 3, 'تعويض عمل إضافي'),
            ('فاطمة عبد الله', '2024-07-25', 2, 'مكافأة مشروع ناجح'),
        ]
        
        successful_additions = 0
        
        for i, (name, date, days, reason) in enumerate(additional_vacations, 1):
            print(f"\n➕ إدراج رقم {i}:")
            print(f"   👤 الموظف: {name}")
            print(f"   📅 التاريخ: {date}")
            print(f"   🕐 عدد الأيام: {days}")
            print(f"   📝 السبب: {reason}")
            
            success, message = self.db.add_vacation(name, date, days, reason)
            
            if success:
                self.print_success(f"تم إدراج الإجازة")
                successful_additions += 1
            else:
                self.print_error(f"فشل في إدراج الإجازة: {message}")
        
        self.print_info(f"تم إدراج {successful_additions}/{len(additional_vacations)} إجازة بنجاح")
        return successful_additions == len(additional_vacations)
    
    def test_balance_calculations(self):
        """اختبار حسابات الأرصدة"""
        self.print_header("اختبار حسابات الأرصدة")
        
        # قائمة الموظفين للاختبار
        test_employees = [
            'أحمد محمد علي',
            'فاطمة عبد الله', 
            'محمد عبد الرحمن',
            'عائشة سعيد',
            'عبد الله أحمد'
        ]
        
        print("💰 تفاصيل الأرصدة:")
        print("-" * 80)
        
        for employee in test_employees:
            balance = self.db.get_employee_balance(employee)
            
            if balance:
                print(f"\n👤 {employee}:")
                print(f"   📊 الرصيد الابتدائي: {balance['initial_balance']} يوم")
                print(f"   ➕ الإجازات المدرجة: {balance['added_vacations']} يوم")
                print(f"   📝 الإجازات اليومية: {balance['daily_used']} يوم")
                print(f"   ⏱️  الإجازات الساعية: {balance['hourly_used']} يوم")
                
                # حساب الرصيد الصافي
                net_balance = balance['net_balance']
                print(f"   💎 الرصيد الصافي: {net_balance} يوم")
                
                # تحديد حالة الرصيد
                if net_balance > 0:
                    print(f"   ✅ الحالة: رصيد إيجابي")
                elif net_balance == 0:
                    print(f"   ⚖️  الحالة: رصيد متوازن")
                else:
                    print(f"   ⚠️  الحالة: عجز في الرصيد")
            else:
                print(f"\n❌ {employee}: لم يتم العثور على بيانات")
        
        return True
    
    def test_search_functionality(self):
        """اختبار وظيفة البحث"""
        self.print_header("اختبار وظيفة البحث")
        
        search_terms = ['أحمد', 'فاطمة', 'محمد', 'عائشة']
        
        for term in search_terms:
            print(f"\n🔍 البحث عن: '{term}'")
            
            results = self.db.search_employee(term)
            
            if results:
                print(f"   ✅ تم العثور على {len(results)} نتيجة:")
                for i, result in enumerate(results, 1):
                    print(f"      {i}. {result}")
            else:
                print(f"   ❌ لم يتم العثور على نتائج")
        
        return True
    
    def test_reports(self):
        """اختبار التقارير"""
        self.print_header("اختبار التقارير")
        
        # اختبار تقرير شامل
        print("📊 تقرير شامل للنظام:")
        print("-" * 60)
        
        # إحصائيات عامة
        all_employees = self.db.search_employee('')  # جميع الموظفين
        if all_employees:
            print(f"👥 عدد الموظفين: {len(all_employees)}")
            
            # حساب إجمالي الإجازات
            total_initial = 0
            total_added = 0
            total_daily_used = 0
            total_hourly_used = 0
            
            for employee in all_employees:
                balance = self.db.get_employee_balance(employee)
                if balance:
                    total_initial += balance['initial_balance']
                    total_added += balance['added_vacations']
                    total_daily_used += balance['daily_used']
                    total_hourly_used += balance['hourly_used']
            
            print(f"📈 إجمالي الرصيد الابتدائي: {total_initial} يوم")
            print(f"➕ إجمالي الإجازات المدرجة: {total_added} يوم")
            print(f"📝 إجمالي الإجازات اليومية: {total_daily_used} يوم")
            print(f"⏱️  إجمالي الإجازات الساعية: {total_hourly_used:.2f} يوم")
            
            net_total = total_initial + total_added - total_daily_used - total_hourly_used
            print(f"💎 صافي الرصيد الإجمالي: {net_total:.2f} يوم")
        
        return True
    
    def run_full_test(self):
        """تشغيل اختبار شامل"""
        self.print_header("بدء الاختبار الشامل لنظام إدارة الإجازات")
        
        tests = [
            ("تسجيل الدخول", self.test_login),
            ("استيراد Excel", self.test_excel_import),
            ("طلبات الإجازة اليومية", self.test_daily_vacation_requests),
            ("طلبات الإجازة الساعية", self.test_hourly_vacation_requests),
            ("الإجازات الإضافية", self.test_additional_vacations),
            ("حسابات الأرصدة", self.test_balance_calculations),
            ("وظيفة البحث", self.test_search_functionality),
            ("التقارير", self.test_reports),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🔄 تشغيل اختبار: {test_name}")
            
            try:
                result = test_func()
                if result:
                    self.print_success(f"اختبار {test_name} نجح")
                    passed_tests += 1
                else:
                    self.print_error(f"اختبار {test_name} فشل")
                    
            except Exception as e:
                self.print_error(f"خطأ في اختبار {test_name}: {e}")
            
            self.wait_for_input()
        
        # النتيجة النهائية
        self.print_header("نتائج الاختبار الشامل")
        
        print(f"📊 النتائج:")
        print(f"   ✅ اختبارات نجحت: {passed_tests}")
        print(f"   ❌ اختبارات فشلت: {total_tests - passed_tests}")
        print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("\n🎉 جميع الاختبارات نجحت! النظام يعمل بشكل مثالي!")
        else:
            print(f"\n⚠️  يوجد {total_tests - passed_tests} اختبار فشل")
        
        return passed_tests == total_tests

def main():
    """الدالة الرئيسية"""
    print("🚀 مرحباً بك في اختبار نظام إدارة الإجازات التفاعلي")
    print("🔥 هذا الاختبار سيتحقق من جميع وظائف النظام")
    
    tester = VacationSystemTester()
    
    try:
        success = tester.run_full_test()
        
        if success:
            print("\n🏆 النظام جاهز للاستخدام!")
            print("🖱️  يمكنك الآن تشغيل الواجهة الرسومية")
        else:
            print("\n🔧 يرجى مراجعة الأخطاء وإصلاحها")
            
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")

if __name__ == '__main__':
    main()
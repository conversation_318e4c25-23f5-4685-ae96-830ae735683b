
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل الواجهة الحديثة لنظام إدارة الإجازات
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """تشغيل الواجهة الحديثة"""
    try:
        print("🚀 تشغيل الواجهة الحديثة لنظام إدارة الإجازات...")
        print("✨ تحميل الأنماط العصرية...")
        
        # استيراد الواجهة الحديثة
        from modern_main_window import main as run_modern_ui
        
        print("🎨 الواجهة الحديثة جاهزة!")
        print("📱 مميزات الإصدار الجديد:")
        print("   • تصميم عصري وأنيق")
        print("   • ألوان متدرجة جميلة")
        print("   • بطاقات معلومات تفاعلية")
        print("   • أزرار بتصميم حديث")
        print("   • قوائم طعام شاملة")
        print("   • شريط حالة معلوماتي")
        print("   • تأثيرات بصرية رائعة")
        
        # تشغيل الواجهة
        run_modern_ui()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("🔧 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        print("🔧 راجع متطلبات النظام")
        
    input("\n⏳ اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
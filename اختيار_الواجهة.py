#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف اختيار نوع الواجهة - تقليدية أم حديثة
"""

import sys
import os

def print_header():
    """طباعة العنوان الرئيسي"""
    print("=" * 80)
    print("🏢 نظام إدارة الإجازات - الإصدار المحدث")
    print("=" * 80)
    print()

def print_options():
    """طباعة خيارات الواجهة"""
    print("🎨 اختر نوع الواجهة التي تريد استخدامها:")
    print()
    
    print("1️⃣  الواجهة الحديثة والعصرية ✨")
    print("   • تصميم عصري بألوان متدرجة")
    print("   • بطاقات معلومات تفاعلية")
    print("   • تأثيرات بصرية رائعة")
    print("   • أزرار دائرية مع أوصاف")
    print("   • قوائم شاملة مع أيقونات")
    print("   • شريط حالة معلوماتي")
    print()
    
    print("2️⃣  الواجهة التقليدية والمستقرة 🔧")
    print("   • تصميم بسيط وواضح")
    print("   • سهولة في الاستخدام")
    print("   • مختبرة ومستقرة")
    print("   • جميع الوظائف متوفرة")
    print("   • سرعة في التحميل")
    print()
    
    print("3️⃣  اختبار المقارنة 🔍")
    print("   • مقارنة بين الواجهتين")
    print("   • عرض الاختلافات")
    print("   • تجربة كلا النوعين")
    print()
    
    print("4️⃣  معلومات النظام ℹ️")
    print("   • تفاصيل النظام")
    print("   • دليل الاستخدام")
    print("   • معلومات التحديث")
    print()
    
    print("5️⃣  خروج ❌")
    print()

def show_modern_interface():
    """تشغيل الواجهة الحديثة"""
    try:
        print("🚀 تشغيل الواجهة الحديثة...")
        print("✨ جاري تحميل الأنماط العصرية...")
        
        # استيراد الواجهة الحديثة
        from modern_main_window import main as run_modern_ui
        run_modern_ui()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الواجهة الحديثة: {e}")
        print("🔧 تأكد من وجود الملفات المطلوبة")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الواجهة الحديثة: {e}")

def show_traditional_interface():
    """تشغيل الواجهة التقليدية"""
    try:
        print("🔧 تشغيل الواجهة التقليدية...")
        print("📋 جاري تحميل النظام المستقر...")
        
        # استيراد الواجهة التقليدية
        from main_window import MainWindow
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        font = QFont("Arial", 10)
        app.setFont(font)
        
        window = MainWindow()
        window.show()
        
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الواجهة التقليدية: {e}")
        print("🔧 تأكد من وجود الملفات المطلوبة")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الواجهة التقليدية: {e}")

def show_comparison():
    """عرض مقارنة الواجهات"""
    print("🔍 مقارنة الواجهات:")
    print("-" * 50)
    
    comparison_data = [
        ("التصميم العام", "بسيط وتقليدي", "عصري ومتدرج"),
        ("الألوان", "ألوان أساسية", "ألوان متدرجة جميلة"),
        ("الأزرار", "مستطيلة عادية", "دائرية مع تأثيرات"),
        ("النوافذ", "حواف حادة", "حواف مدورة"),
        ("التأثيرات", "لا توجد", "ظلال وتدرجات"),
        ("البطاقات", "غير موجودة", "بطاقات تفاعلية"),
        ("القوائم", "أساسية", "شاملة مع أيقونات"),
        ("شريط الحالة", "غير موجود", "معلوماتي ومفصل")
    ]
    
    print(f"{'العنصر':<20} {'الواجهة التقليدية':<25} {'الواجهة الحديثة':<25}")
    print("-" * 70)
    
    for item, traditional, modern in comparison_data:
        print(f"{item:<20} {traditional:<25} {modern:<25}")
    
    print("\n💡 للحصول على مقارنة مفصلة، راجع ملف: مقارنة_الواجهات.md")

def show_system_info():
    """عرض معلومات النظام"""
    print("ℹ️  معلومات النظام:")
    print("-" * 30)
    print("🏢 الاسم: نظام إدارة الإجازات")
    print("📱 الإصدار: 2.0 (محدث)")
    print("👨‍💻 المطور: فريق التطوير")
    print("📅 سنة الإنتاج: 2024")
    print("🛠️  التقنيات: Python, PyQt5, SQLite")
    print()
    
    print("📋 الواجهات المتوفرة:")
    print("   • الواجهة الحديثة (جديد!)")
    print("   • الواجهة التقليدية")
    print()
    
    print("🎯 الوظائف المتوفرة:")
    print("   • استيراد الرصيد من Excel")
    print("   • طلبات الإجازة اليومية")
    print("   • طلبات الإجازة الساعية")
    print("   • إدراج إجازات إضافية")
    print("   • التقارير والاستعلامات")
    print("   • تعديل وحذف الطلبات")
    print()
    
    print("📁 ملفات النظام:")
    files = [
        "main.py", "database.py", "main_window.py", "requirements.txt",
        "modern_main_window.py", "modern_ui_styles.py", "modern_dialogs.py",
        "run_app.py", "setup.py", "test_app.py"
    ]
    
    for i, file in enumerate(files, 1):
        status = "✅" if os.path.exists(file) else "❌"
        print(f"   {status} {file}")
    
    print()
    print("📖 للحصول على دليل مفصل، راجع:")
    print("   • README.md")
    print("   • دليل_المستخدم_السريع.md")
    print("   • مقارنة_الواجهات.md")

def main():
    """الدالة الرئيسية"""
    while True:
        # مسح الشاشة (Windows)
        os.system('cls' if os.name == 'nt' else 'clear')
        
        print_header()
        print_options()
        
        try:
            choice = input("🎯 اختر رقم الخيار (1-5): ").strip()
            
            if choice == '1':
                print("\n🎨 اخترت الواجهة الحديثة والعصرية!")
                input("⏳ اضغط Enter للمتابعة...")
                show_modern_interface()
                break
                
            elif choice == '2':
                print("\n🔧 اخترت الواجهة التقليدية والمستقرة!")
                input("⏳ اضغط Enter للمتابعة...")
                show_traditional_interface()
                break
                
            elif choice == '3':
                print("\n🔍 عرض مقارنة الواجهات:")
                print()
                show_comparison()
                input("\n⏳ اضغط Enter للعودة للقائمة...")
                
            elif choice == '4':
                print("\nℹ️  معلومات النظام:")
                print()
                show_system_info()
                input("\n⏳ اضغط Enter للعودة للقائمة...")
                
            elif choice == '5':
                print("\n👋 شكراً لاستخدام نظام إدارة الإجازات!")
                print("🎉 نتطلع لرؤيتك مرة أخرى!")
                break
                
            else:
                print("\n❌ خيار غير صحيح! يرجى اختيار رقم من 1 إلى 5")
                input("⏳ اضغط Enter للمحاولة مرة أخرى...")
                
        except KeyboardInterrupt:
            print("\n\n👋 تم الخروج من البرنامج")
            break
            
        except Exception as e:
            print(f"\n❌ حدث خطأ: {e}")
            input("⏳ اضغط Enter للمحاولة مرة أخرى...")

if __name__ == '__main__':
    main()
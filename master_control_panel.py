#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
لوحة التحكم الرئيسية لنظام إدارة الإجازات المتقدم
Master Control Panel for Advanced Vacation Management System
"""

import sys
import os
import subprocess
from datetime import datetime

class MasterControlPanel:
    def __init__(self):
        self.system_modules = {
            'main_system': {
                'name': 'النظام الرئيسي',
                'description': 'واجهة إدارة الإجازات الأساسية',
                'file': 'main.py',
                'icon': '🏠'
            },
            'reports': {
                'name': 'نظام التقارير المتقدم',
                'description': 'تقارير شاملة وتحليلات متقدمة',
                'file': 'run_reports.py',
                'icon': '📊'
            },
            'notifications': {
                'name': 'نظام الإشعارات',
                'description': 'إدارة التنبيهات والإشعارات',
                'file': 'notifications_interface.py',
                'icon': '🔔'
            },
            'analytics': {
                'name': 'لوحة التحليلات',
                'description': 'رسوم بيانية وإحصائيات تفصيلية',
                'file': 'analytics_dashboard.py',
                'icon': '📈'
            },
            'backup': {
                'name': 'نظام النسخ الاحتياطي',
                'description': 'إدارة النسخ الاحتياطية والاستعادة',
                'file': 'backup_interface.py',
                'icon': '💾'
            },
            'user_management': {
                'name': 'إدارة المستخدمين',
                'description': 'إدارة المستخدمين والصلاحيات',
                'file': 'user_management_interface.py',
                'icon': '👥'
            }
        }
    
    def display_main_menu(self):
        """عرض القائمة الرئيسية"""
        print("\n" + "="*80)
        print("🎯 لوحة التحكم الرئيسية - نظام إدارة الإجازات المتقدم")
        print("="*80)
        print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("-"*80)
        
        # عرض الأنظمة المتاحة
        for i, (key, module) in enumerate(self.system_modules.items(), 1):
            status = self._check_module_status(module['file'])
            status_icon = "✅" if status else "❌"
            
            print(f"{i}. {module['icon']} {module['name']} {status_icon}")
            print(f"   📝 {module['description']}")
            if not status:
                print(f"   ⚠️ الملف غير موجود: {module['file']}")
            print()
        
        print("=" * 80)
        print("🔧 أدوات إضافية:")
        print(f"{len(self.system_modules) + 1}. 🧪 اختبار النظام الشامل")
        print(f"{len(self.system_modules) + 2}. 📋 فحص حالة النظام")
        print(f"{len(self.system_modules) + 3}. 🔄 تحديث النظام")
        print(f"{len(self.system_modules) + 4}. ℹ️ معلومات النظام")
        print("0. 🚪 خروج")
        print("="*80)
    
    def _check_module_status(self, filename):
        """فحص حالة وحدة النظام"""
        return os.path.exists(filename)
    
    def run_module(self, module_key):
        """تشغيل وحدة نظام"""
        if module_key not in self.system_modules:
            print("❌ وحدة النظام غير موجودة")
            return
        
        module = self.system_modules[module_key]
        
        if not self._check_module_status(module['file']):
            print(f"❌ ملف الوحدة غير موجود: {module['file']}")
            return
        
        print(f"\n🚀 تشغيل {module['name']}...")
        print(f"📝 {module['description']}")
        print("-" * 50)
        
        try:
            # تشغيل الوحدة
            subprocess.run([sys.executable, module['file']], check=True)
        except subprocess.CalledProcessError as e:
            print(f"❌ خطأ في تشغيل الوحدة: {e}")
        except KeyboardInterrupt:
            print(f"\n⏹️ تم إيقاف {module['name']}")
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")
    
    def run_comprehensive_test(self):
        """تشغيل اختبار شامل للنظام"""
        print("\n🧪 اختبار النظام الشامل")
        print("=" * 50)
        
        test_files = [
            'test_vacation_system.py',
            'test_advanced_reports.py'
        ]
        
        for test_file in test_files:
            if os.path.exists(test_file):
                print(f"\n🔍 تشغيل {test_file}...")
                try:
                    result = subprocess.run([sys.executable, test_file], 
                                          capture_output=True, text=True, timeout=60)
                    
                    if result.returncode == 0:
                        print(f"✅ {test_file} - نجح")
                    else:
                        print(f"❌ {test_file} - فشل")
                        if result.stderr:
                            print(f"   خطأ: {result.stderr[:200]}...")
                            
                except subprocess.TimeoutExpired:
                    print(f"⏰ {test_file} - انتهت المهلة الزمنية")
                except Exception as e:
                    print(f"❌ {test_file} - خطأ: {e}")
            else:
                print(f"⚠️ {test_file} - غير موجود")
        
        print("\n✅ انتهى الاختبار الشامل")
    
    def check_system_status(self):
        """فحص حالة النظام"""
        print("\n📋 فحص حالة النظام")
        print("=" * 50)
        
        # فحص ملفات النظام الأساسية
        core_files = [
            'database.py',
            'vacation_system.db',
            'requirements.txt'
        ]
        
        print("📁 الملفات الأساسية:")
        for file in core_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                print(f"  ✅ {file} ({size:,} بايت)")
            else:
                print(f"  ❌ {file} - غير موجود")
        
        # فحص وحدات النظام
        print(f"\n🔧 وحدات النظام:")
        available_modules = 0
        for key, module in self.system_modules.items():
            status = self._check_module_status(module['file'])
            status_text = "متاح" if status else "غير متاح"
            status_icon = "✅" if status else "❌"
            
            print(f"  {status_icon} {module['name']}: {status_text}")
            if status:
                available_modules += 1
        
        print(f"\n📊 ملخص الحالة:")
        print(f"  🔧 الوحدات المتاحة: {available_modules}/{len(self.system_modules)}")
        
        # فحص المكتبات
        print(f"\n📦 فحص المكتبات:")
        required_libraries = ['pandas', 'openpyxl', 'sqlite3']
        
        for lib in required_libraries:
            try:
                __import__(lib)
                print(f"  ✅ {lib}")
            except ImportError:
                print(f"  ❌ {lib} - غير مثبت")
    
    def update_system(self):
        """تحديث النظام"""
        print("\n🔄 تحديث النظام")
        print("=" * 50)
        
        print("1. تحديث المكتبات")
        print("2. إنشاء البيانات النموذجية")
        print("3. تحديث قاعدة البيانات")
        print("0. العودة")
        
        choice = input("\nاختر نوع التحديث: ").strip()
        
        if choice == '1':
            self._update_libraries()
        elif choice == '2':
            self._create_sample_data()
        elif choice == '3':
            self._update_database()
        elif choice != '0':
            print("⚠️ اختيار غير صحيح")
    
    def _update_libraries(self):
        """تحديث المكتبات"""
        print("\n📦 تحديث المكتبات...")
        
        if os.path.exists('requirements.txt'):
            try:
                result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt', '--upgrade'],
                                      capture_output=True, text=True)
                
                if result.returncode == 0:
                    print("✅ تم تحديث المكتبات بنجاح")
                else:
                    print(f"❌ خطأ في تحديث المكتبات: {result.stderr}")
            except Exception as e:
                print(f"❌ خطأ في تحديث المكتبات: {e}")
        else:
            print("⚠️ ملف requirements.txt غير موجود")
    
    def _create_sample_data(self):
        """إنشاء البيانات النموذجية"""
        print("\n📊 إنشاء البيانات النموذجية...")
        
        if os.path.exists('create_sample_excel.py'):
            try:
                subprocess.run([sys.executable, 'create_sample_excel.py'], check=True)
                print("✅ تم إنشاء البيانات النموذجية")
            except Exception as e:
                print(f"❌ خطأ في إنشاء البيانات: {e}")
        else:
            print("⚠️ ملف create_sample_excel.py غير موجود")
    
    def _update_database(self):
        """تحديث قاعدة البيانات"""
        print("\n🗄️ تحديث قاعدة البيانات...")
        
        try:
            from database import VacationDatabase
            db = VacationDatabase()
            print("✅ تم تحديث قاعدة البيانات")
        except Exception as e:
            print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
    
    def show_system_info(self):
        """عرض معلومات النظام"""
        print("\n ℹ️ معلومات النظام")
        print("=" * 50)
        
        print(f"🐍 Python: {sys.version}")
        print(f"💻 النظام: {os.name}")
        print(f"📁 مجلد العمل: {os.getcwd()}")
        
        # معلومات قاعدة البيانات
        if os.path.exists('vacation_system.db'):
            db_size = os.path.getsize('vacation_system.db')
            print(f"🗄️ قاعدة البيانات: {db_size:,} بايت")
        else:
            print("🗄️ قاعدة البيانات: غير موجودة")
        
        # إحصائيات الملفات
        python_files = [f for f in os.listdir('.') if f.endswith('.py')]
        print(f"📄 ملفات Python: {len(python_files)}")
        
        # معلومات النسخ الاحتياطية
        if os.path.exists('backups'):
            backup_files = os.listdir('backups')
            print(f"💾 النسخ الاحتياطية: {len(backup_files)}")
        else:
            print("💾 النسخ الاحتياطية: لا يوجد مجلد")
    
    def run(self):
        """تشغيل لوحة التحكم الرئيسية"""
        print("🎯 مرحباً بك في لوحة التحكم الرئيسية!")
        print("نظام إدارة الإجازات المتقدم - الإصدار 2.0")
        
        while True:
            try:
                self.display_main_menu()
                choice = input("اختر الوحدة أو العملية المطلوبة: ").strip()
                
                if choice == '0':
                    print("👋 شكراً لاستخدام نظام إدارة الإجازات المتقدم!")
                    break
                
                try:
                    choice_num = int(choice)
                    
                    if 1 <= choice_num <= len(self.system_modules):
                        # تشغيل وحدة نظام
                        module_keys = list(self.system_modules.keys())
                        module_key = module_keys[choice_num - 1]
                        self.run_module(module_key)
                    
                    elif choice_num == len(self.system_modules) + 1:
                        self.run_comprehensive_test()
                    elif choice_num == len(self.system_modules) + 2:
                        self.check_system_status()
                    elif choice_num == len(self.system_modules) + 3:
                        self.update_system()
                    elif choice_num == len(self.system_modules) + 4:
                        self.show_system_info()
                    else:
                        print("⚠️ اختيار غير صحيح")
                        
                except ValueError:
                    print("⚠️ يرجى إدخال رقم صحيح")
                
                if choice != '0':
                    input("\nاضغط Enter للعودة للقائمة الرئيسية...")
                
            except KeyboardInterrupt:
                print("\n\n👋 تم إيقاف لوحة التحكم")
                break
            except Exception as e:
                print(f"\n❌ خطأ غير متوقع: {e}")
                input("اضغط Enter للمتابعة...")

if __name__ == "__main__":
    control_panel = MasterControlPanel()
    control_panel.run()

// qdatastream.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDataStream
{
%TypeHeaderCode
#include <qdatastream.h>
%End

public:
    enum Version
    {
        Qt_1_0,
        Qt_2_0,
        Qt_2_1,
        Qt_3_0,
        Qt_3_1,
        Qt_3_3,
        Qt_4_0,
        Qt_4_1,
        Qt_4_2,
        Qt_4_3,
        Qt_4_4,
        Qt_4_5,
        Qt_4_6,
        Qt_4_7,
        Qt_4_8,
        Qt_4_9,
        Qt_5_0,
%If (Qt_5_1_0 -)
        Qt_5_1,
%End
%If (Qt_5_2_0 -)
        Qt_5_2,
%End
%If (Qt_5_3_0 -)
        Qt_5_3,
%End
%If (Qt_5_4_0 -)
        Qt_5_4,
%End
%If (Qt_5_5_0 -)
        Qt_5_5,
%End
%If (Qt_5_6_0 -)
        Qt_5_6,
%End
%If (Qt_5_7_0 -)
        Qt_5_7,
%End
%If (Qt_5_8_0 -)
        Qt_5_8,
%End
%If (Qt_5_9_0 -)
        Qt_5_9,
%End
%If (Qt_5_10_0 -)
        Qt_5_10,
%End
%If (Qt_5_11_0 -)
        Qt_5_11,
%End
%If (Qt_5_12_0 -)
        Qt_5_12,
%End
%If (Qt_5_13_0 -)
        Qt_5_13,
%End
%If (Qt_5_14_0 -)
        Qt_5_14,
%End
%If (Qt_5_15_0 -)
        Qt_5_15,
%End
    };

    enum ByteOrder
    {
        BigEndian,
        LittleEndian,
    };

    enum Status
    {
        Ok,
        ReadPastEnd,
        ReadCorruptData,
        WriteFailed,
    };

    QDataStream();
    explicit QDataStream(QIODevice *);
    QDataStream(QByteArray * /Constrained/, QIODevice::OpenMode flags);
    QDataStream(const QByteArray & /Constrained/);
    ~QDataStream();
    QIODevice *device() const;
    void setDevice(QIODevice *);
    bool atEnd() const;
    QDataStream::Status status() const;
    void setStatus(QDataStream::Status status);
    void resetStatus();
    QDataStream::ByteOrder byteOrder() const;
    void setByteOrder(QDataStream::ByteOrder);
    int version() const;
    void setVersion(int v);
    int skipRawData(int len) /ReleaseGIL/;
// Extra methods to give explicit control over the simple data types being read and written.
int readInt() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

qint8 readInt8() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

quint8 readUInt8() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

qint16 readInt16() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

quint16 readUInt16() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

qint32 readInt32() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

quint32 readUInt32() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

qint64 readInt64() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

quint64 readUInt64() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

bool readBool() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

float readFloat() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

double readDouble() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

SIP_PYOBJECT readString() /ReleaseGIL,TypeHint="Py_v3:bytes;str"/;
%MethodCode
    char *s;

    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> s;
    Py_END_ALLOW_THREADS

    if (s)
    {
        sipRes = SIPBytes_FromString(s);
        delete[] s;
    }
    else
    {
        sipRes = Py_None;
        Py_INCREF(Py_None);
    }
%End
    
void writeInt(int i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeInt8(qint8 i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeUInt8(quint8 i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeInt16(qint16 i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeUInt16(quint16 i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeInt32(qint32 i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeUInt32(quint32 i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeInt64(qint64 i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeUInt64(quint64 i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeBool(bool i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeFloat(float f) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeDouble(double f) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeString(const char *str /Encoding="None"/) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End
// Extra methods to support v2 of the QString and QVariant APIs.
QString readQString() /ReleaseGIL/;
%MethodCode
    sipRes = new QString;

    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> *sipRes;
    Py_END_ALLOW_THREADS
%End

void writeQString(const QString &qstr) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << *a0;
    Py_END_ALLOW_THREADS
%End

QStringList readQStringList() /ReleaseGIL/;
%MethodCode
    sipRes = new QStringList;

    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> *sipRes;
    Py_END_ALLOW_THREADS
%End

void writeQStringList(const QStringList &qstrlst) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << *a0;
    Py_END_ALLOW_THREADS
%End

QVariant readQVariant() /ReleaseGIL/;
%MethodCode
    sipRes = new QVariant;

    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> *sipRes;
    Py_END_ALLOW_THREADS
%End

void writeQVariant(const QVariant &qvar) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << *a0;
    Py_END_ALLOW_THREADS
%End

QVariantList readQVariantList() /ReleaseGIL/;
%MethodCode
    sipRes = new QVariantList;

    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> *sipRes;
    Py_END_ALLOW_THREADS
%End

void writeQVariantList(const QVariantList &qvarlst) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << *a0;
    Py_END_ALLOW_THREADS
%End

QVariantMap readQVariantMap() /ReleaseGIL/;
%MethodCode
    sipRes = new QVariantMap;

    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> *sipRes;
    Py_END_ALLOW_THREADS
%End

void writeQVariantMap(const QVariantMap &qvarmap) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << *a0;
    Py_END_ALLOW_THREADS
%End

QVariantHash readQVariantHash() /ReleaseGIL/;
%MethodCode
    sipRes = new QVariantHash;

    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> *sipRes;
    Py_END_ALLOW_THREADS
%End

void writeQVariantHash(const QVariantHash &qvarhash) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << *a0;
    Py_END_ALLOW_THREADS
%End
    SIP_PYOBJECT readBytes() /TypeHint="Py_v3:bytes;str",ReleaseGIL/;
%MethodCode
        char *s;
        uint l;
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->readBytes(s, l);
        Py_END_ALLOW_THREADS
        
        if ((sipRes = SIPBytes_FromStringAndSize(s, l)) == NULL)
            sipIsErr = 1;
        
        if (s)
            delete[] s;
%End

    SIP_PYOBJECT readRawData(int len) /TypeHint="Py_v3:bytes;str",ReleaseGIL/;
%MethodCode
        char *s = new char[a0];
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->readRawData(s, a0);
        Py_END_ALLOW_THREADS
        
        sipRes = SIPBytes_FromStringAndSize(s, a0);
        
        if (!sipRes)
            sipIsErr = 1;
        
        delete[] s;
%End

    QDataStream &writeBytes(const char * /Array/, uint len /ArraySize/) /ReleaseGIL/;
    int writeRawData(const char * /Array/, int len /ArraySize/) /ReleaseGIL/;

    enum FloatingPointPrecision
    {
        SinglePrecision,
        DoublePrecision,
    };

    QDataStream::FloatingPointPrecision floatingPointPrecision() const;
    void setFloatingPointPrecision(QDataStream::FloatingPointPrecision precision);
%If (Qt_5_7_0 -)
    void startTransaction();
%End
%If (Qt_5_7_0 -)
    bool commitTransaction();
%End
%If (Qt_5_7_0 -)
    void rollbackTransaction();
%End
%If (Qt_5_7_0 -)
    void abortTransaction();
%End

private:
    QDataStream(const QDataStream &);
};

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
النوافذ المتقدمة للواجهة الحديثة - نظام إدارة الإجازات
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime, timedelta
import json
import pandas as pd
from modern_dialogs import ModernDialog

class AdvancedImportWindow(ModernDialog):
    """نافذة استيراد متقدمة مع معاينة شاملة"""
    
    def __init__(self):
        super().__init__("📥 استيراد البيانات المتقدم", 900, 700)
        self.imported_data = None
        self.setup_advanced_content()
        
    def setup_advanced_content(self):
        """إعداد المحتوى المتقدم"""
        layout = QVBoxLayout(self.content_area)
        layout.setSpacing(20)
        
        # شريط التقدم
        self.create_progress_section(layout)
        
        # قسم اختيار الملف المحسن
        self.create_enhanced_file_section(layout)
        
        # قسم المعاينة المتقدمة
        self.create_advanced_preview_section(layout)
        
        # قسم التحقق من البيانات
        self.create_validation_section(layout)
        
        # إعداد الأزرار
        self.add_button("🔄 إعادة تحميل", self.reload_file, "secondary")
        self.add_button("✅ تطبيق التغييرات", self.apply_changes, "primary")
        self.add_button("❌ إلغاء", self.reject, "secondary")
        
    def create_progress_section(self, layout):
        """إنشاء قسم شريط التقدم"""
        progress_frame = QFrame()
        progress_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 15px;
            }
        """)
        
        progress_layout = QVBoxLayout(progress_frame)
        
        # تسمية
        progress_label = QLabel("📊 حالة العملية:")
        progress_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        progress_layout.addWidget(progress_label)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                background: #ecf0f1;
                text-align: center;
                font-weight: bold;
                color: #2c3e50;
                height: 25px;
            }
            QProgressBar::chunk {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db, stop: 1 #2980b9
                );
                border-radius: 8px;
            }
        """)
        progress_layout.addWidget(self.progress_bar)
        
        # رسالة الحالة
        self.status_label = QLabel("🏁 جاهز للبدء...")
        self.status_label.setStyleSheet("color: #7f8c8d; font-size: 16px;")
        progress_layout.addWidget(self.status_label)
        
        layout.addWidget(progress_frame)
        
    def create_enhanced_file_section(self, layout):
        """إنشاء قسم اختيار الملف المحسن"""
        file_frame = QFrame()
        file_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #f39c12, stop: 1 #e67e22
                );
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        file_layout = QVBoxLayout(file_frame)
        
        # العنوان
        title_label = QLabel("📁 اختيار ملف Excel المتقدم")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
            background: transparent;
        """)
        file_layout.addWidget(title_label)
        
        # صف اختيار الملف
        file_row = QHBoxLayout()
        
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("اسحب وأسقط الملف هنا أو انقر تصفح...")
        self.file_path_edit.setMinimumHeight(50)
        self.file_path_edit.setMaximumHeight(60)
        self.file_path_edit.setStyleSheet("""
            QLineEdit {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
                padding: 18px;
                font-size: 16px;
                color: #2c3e50;
                min-height: 50px;
            }
            QLineEdit:focus {
                border: 2px solid white;
                background: white;
            }
        """)
        file_row.addWidget(self.file_path_edit)
        
        # أزرار التحكم
        browse_button = QPushButton("📂 تصفح")
        browse_button.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.9);
                color: #2c3e50;
                border: none;
                border-radius: 10px;
                padding: 15px 20px;
                font-weight: bold;
                margin-left: 10px;
            }
            QPushButton:hover {
                background: white;
            }
        """)
        browse_button.clicked.connect(self.browse_file)
        file_row.addWidget(browse_button)
        
        clear_button = QPushButton("🗑️ مسح")
        clear_button.setStyleSheet("""
            QPushButton {
                background: rgba(231, 76, 60, 0.9);
                color: white;
                border: none;
                border-radius: 10px;
                padding: 15px 20px;
                font-weight: bold;
                margin-left: 10px;
            }
            QPushButton:hover {
                background: #e74c3c;
            }
        """)
        clear_button.clicked.connect(self.clear_file)
        file_row.addWidget(clear_button)
        
        file_layout.addLayout(file_row)
        
        # معلومات الملف
        self.file_info_label = QLabel("ℹ️ لم يتم اختيار ملف")
        self.file_info_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            margin-top: 10px;
        """)
        file_layout.addWidget(self.file_info_label)
        
        layout.addWidget(file_frame)
        
    def create_advanced_preview_section(self, layout):
        """إنشاء قسم المعاينة المتقدمة"""
        preview_frame = QFrame()
        preview_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 20px;
            }
        """)
        
        preview_layout = QVBoxLayout(preview_frame)
        
        # شريط التحكم
        control_bar = QHBoxLayout()
        
        preview_label = QLabel("👀 معاينة البيانات المتقدمة")
        preview_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        control_bar.addWidget(preview_label)
        
        control_bar.addStretch()
        
        # عدد الصفوف المعروضة
        rows_label = QLabel("عدد الصفوف:")
        rows_label.setStyleSheet("color: #7f8c8d; font-size: 16px;")
        control_bar.addWidget(rows_label)
        
        self.rows_spinbox = QSpinBox()
        self.rows_spinbox.setRange(5, 50)
        self.rows_spinbox.setValue(10)
        self.rows_spinbox.valueChanged.connect(self.update_preview)
        control_bar.addWidget(self.rows_spinbox)
        
        preview_layout.addLayout(control_bar)
        
        # الجدول المتقدم
        self.preview_table = QTableWidget()
        self.preview_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                gridline-color: #dee2e6;
                font-size: 16px;
            }
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db, stop: 1 #2980b9
                );
                border: 1px solid #2980b9;
                padding: 10px;
                font-weight: bold;
                color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background: #e3f2fd;
                color: #1976d2;
            }
        """)
        preview_layout.addWidget(self.preview_table)
        
        layout.addWidget(preview_frame)
        
    def create_validation_section(self, layout):
        """إنشاء قسم التحقق من البيانات"""
        validation_frame = QFrame()
        validation_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2ecc71, stop: 1 #27ae60
                );
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        validation_layout = QVBoxLayout(validation_frame)
        
        # العنوان
        validation_title = QLabel("✅ نتائج التحقق من البيانات")
        validation_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
            background: transparent;
        """)
        validation_layout.addWidget(validation_title)
        
        # معلومات التحقق
        self.validation_info = QLabel("⏳ في انتظار اختيار الملف...")
        self.validation_info.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            background: transparent;
            padding: 10px;
        """)
        validation_layout.addWidget(self.validation_info)
        
        layout.addWidget(validation_frame)
        
    def browse_file(self):
        """تصفح واختيار ملف"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "اختيار ملف Excel للاستيراد", 
            "", 
            "Excel Files (*.xlsx *.xls);;All Files (*)"
        )
        
        if file_path:
            self.file_path_edit.setText(file_path)
            self.load_file(file_path)
            
    def clear_file(self):
        """مسح الملف المحدد"""
        self.file_path_edit.clear()
        self.preview_table.clear()
        self.file_info_label.setText("ℹ️ لم يتم اختيار ملف")
        self.validation_info.setText("⏳ في انتظار اختيار الملف...")
        self.progress_bar.setValue(0)
        self.status_label.setText("🏁 جاهز للبدء...")
        
    def load_file(self, file_path):
        """تحميل ملف Excel"""
        try:
            self.status_label.setText("📥 جاري قراءة الملف...")
            self.progress_bar.setValue(25)
            
            # قراءة الملف
            df = pd.read_excel(file_path)
            self.imported_data = df
            
            self.progress_bar.setValue(50)
            self.status_label.setText("🔍 جاري تحليل البيانات...")
            
            # معلومات الملف
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)
            
            self.file_info_label.setText(
                f"📊 الملف: {os.path.basename(file_path)} | "
                f"الحجم: {file_size_mb:.2f} MB | "
                f"الصفوف: {len(df)} | "
                f"الأعمدة: {len(df.columns)}"
            )
            
            self.progress_bar.setValue(75)
            self.status_label.setText("📋 جاري إعداد المعاينة...")
            
            # المعاينة
            self.update_preview()
            
            self.progress_bar.setValue(100)
            self.status_label.setText("✅ تم تحميل الملف بنجاح!")
            
            # التحقق من البيانات
            self.validate_data(df)
            
        except Exception as e:
            self.progress_bar.setValue(0)
            self.status_label.setText(f"❌ خطأ في تحميل الملف: {str(e)}")
            QMessageBox.warning(self, "خطأ", f"لا يمكن قراءة الملف:\n{str(e)}")
            
    def update_preview(self):
        """تحديث معاينة البيانات"""
        if self.imported_data is None:
            return
            
        df = self.imported_data
        rows_count = self.rows_spinbox.value()
        
        # إعداد الجدول
        display_rows = min(rows_count, len(df))
        self.preview_table.setRowCount(display_rows)
        self.preview_table.setColumnCount(len(df.columns))
        self.preview_table.setHorizontalHeaderLabels(df.columns.tolist())
        
        # ملء البيانات
        for row in range(display_rows):
            for col in range(len(df.columns)):
                value = str(df.iloc[row, col])
                item = QTableWidgetItem(value)
                
                # تلوين الخلايا حسب نوع البيانات
                if pd.isna(df.iloc[row, col]):
                    item.setBackground(QColor(255, 235, 235))  # أحمر فاتح للقيم الفارغة
                elif isinstance(df.iloc[row, col], (int, float)):
                    item.setBackground(QColor(235, 255, 235))  # أخضر فاتح للأرقام
                elif isinstance(df.iloc[row, col], str):
                    item.setBackground(QColor(235, 235, 255))  # أزرق فاتح للنصوص
                    
                self.preview_table.setItem(row, col, item)
                
        # تعديل حجم الأعمدة
        self.preview_table.resizeColumnsToContents()
        
    def validate_data(self, df):
        """التحقق من صحة البيانات"""
        issues = []
        
        # التحقق من الأعمدة المطلوبة
        required_columns = ['الاسم واللقب', 'عدد الأيام']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            issues.append(f"❌ أعمدة مفقودة: {', '.join(missing_columns)}")
        
        # التحقق من القيم الفارغة
        empty_cells = df.isnull().sum().sum()
        if empty_cells > 0:
            issues.append(f"⚠️ خلايا فارغة: {empty_cells}")
        
        # التحقق من صحة الأرقام
        if 'عدد الأيام' in df.columns:
            invalid_numbers = df['عدد الأيام'].apply(lambda x: not isinstance(x, (int, float))).sum()
            if invalid_numbers > 0:
                issues.append(f"⚠️ أرقام غير صحيحة في عدد الأيام: {invalid_numbers}")
        
        # التحقق من الأسماء المكررة
        if 'الاسم واللقب' in df.columns:
            duplicates = df['الاسم واللقب'].duplicated().sum()
            if duplicates > 0:
                issues.append(f"⚠️ أسماء مكررة: {duplicates}")
        
        # عرض النتائج
        if issues:
            self.validation_info.setText(
                f"⚠️ تم العثور على {len(issues)} مشكلة:\n" + 
                "\n".join(issues)
            )
        else:
            self.validation_info.setText(
                f"✅ البيانات صحيحة!\n"
                f"📊 عدد الصفوف: {len(df)}\n"
                f"📋 عدد الأعمدة: {len(df.columns)}\n"
                f"✨ جاهز للاستيراد!"
            )
            
    def reload_file(self):
        """إعادة تحميل الملف"""
        file_path = self.file_path_edit.text().strip()
        if file_path:
            self.load_file(file_path)
        else:
            QMessageBox.information(self, "معلومات", "يرجى اختيار ملف أولاً")
            
    def apply_changes(self):
        """تطبيق التغييرات"""
        if self.imported_data is None:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ملف صحيح أولاً")
            return
            
        # هنا سيتم إضافة كود الاستيراد الفعلي
        QMessageBox.information(self, "نجح", 
                               f"تم استيراد {len(self.imported_data)} سجل بنجاح!")
        self.accept()

class ModernReportsWindow(ModernDialog):
    """نافذة التقارير الحديثة"""
    
    def __init__(self):
        super().__init__("📊 التقارير المتقدمة", 1000, 800)
        self.setup_reports_content()
        
    def setup_reports_content(self):
        """إعداد محتوى التقارير"""
        layout = QVBoxLayout(self.content_area)
        layout.setSpacing(20)
        
        # شريط التحكم
        self.create_control_bar(layout)
        
        # التبويبات
        self.create_tabs_section(layout)
        
        # إعداد الأزرار
        self.add_button("📤 تصدير", self.export_report, "primary")
        self.add_button("🔄 تحديث", self.refresh_reports, "secondary")
        self.add_button("❌ إغلاق", self.accept, "secondary")
        
    def create_control_bar(self, layout):
        """إنشاء شريط التحكم"""
        control_frame = QFrame()
        control_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #1abc9c, stop: 1 #16a085
                );
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        control_layout = QHBoxLayout(control_frame)
        
        # العنوان
        title_label = QLabel("📊 مركز التقارير المتقدم")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: white;
            background: transparent;
        """)
        control_layout.addWidget(title_label)
        
        control_layout.addStretch()
        
        # فلترة التاريخ
        date_label = QLabel("📅 من:")
        date_label.setStyleSheet("color: white; font-weight: bold;")
        control_layout.addWidget(date_label)
        
        self.from_date = QDateEdit(QDate.currentDate().addDays(-30))
        self.from_date.setCalendarPopup(True)
        self.from_date.setStyleSheet("""
            QDateEdit {
                background: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
            }
        """)
        control_layout.addWidget(self.from_date)
        
        to_label = QLabel("إلى:")
        to_label.setStyleSheet("color: white; font-weight: bold;")
        control_layout.addWidget(to_label)
        
        self.to_date = QDateEdit(QDate.currentDate())
        self.to_date.setCalendarPopup(True)
        self.to_date.setStyleSheet("""
            QDateEdit {
                background: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
            }
        """)
        control_layout.addWidget(self.to_date)
        
        layout.addWidget(control_frame)
        
    def create_tabs_section(self, layout):
        """إنشاء قسم التبويبات"""
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                background: white;
            }
            QTabBar::tab {
                background: #f8f9fa;
                border: 2px solid #e0e0e0;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 15px 25px;
                margin-right: 2px;
                font-weight: bold;
                color: #2c3e50;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background: #d5dbdb;
            }
        """)
        
        # تبويب الإحصائيات العامة
        self.create_statistics_tab()
        
        # تبويب أرصدة الموظفين
        self.create_balances_tab()
        
        # تبويب التقارير الشهرية
        self.create_monthly_tab()
        
        # تبويب الرسوم البيانية
        self.create_charts_tab()
        
        layout.addWidget(self.tabs)
        
    def create_statistics_tab(self):
        """إنشاء تبويب الإحصائيات"""
        stats_widget = QWidget()
        stats_layout = QVBoxLayout(stats_widget)
        
        # بطاقات الإحصائيات
        cards_layout = QGridLayout()
        
        # إحصائيات وهمية
        stats_data = [
            ("👥", "إجمالي الموظفين", "245", "#3498db"),
            ("📝", "طلبات الشهر", "89", "#e74c3c"),
            ("💰", "أيام الرصيد", "5,420", "#27ae60"),
            ("⏱️", "الإجازات الساعية", "156", "#f39c12"),
            ("📊", "معدل الاستخدام", "73%", "#9b59b6"),
            ("🎯", "الكفاءة", "85%", "#1abc9c"),
        ]
        
        for i, (icon, title, value, color) in enumerate(stats_data):
            card = self.create_stat_card(icon, title, value, color)
            cards_layout.addWidget(card, i // 3, i % 3)
            
        stats_layout.addLayout(cards_layout)
        
        # جدول الإحصائيات التفصيلية
        details_table = QTableWidget()
        details_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 5px;
                gridline-color: #e0e0e0;
            }
            QHeaderView::section {
                background: #f8f9fa;
                border: 1px solid #e0e0e0;
                padding: 10px;
                font-weight: bold;
            }
        """)
        
        # بيانات وهمية للجدول
        headers = ["القسم", "عدد الموظفين", "طلبات الشهر", "متوسط الأيام"]
        data = [
            ["الإدارة", "45", "12", "5.2"],
            ["المالية", "32", "8", "4.8"],
            ["التسويق", "28", "15", "6.1"],
            ["الموارد البشرية", "22", "6", "3.9"],
            ["التقنية", "38", "18", "7.2"],
        ]
        
        details_table.setRowCount(len(data))
        details_table.setColumnCount(len(headers))
        details_table.setHorizontalHeaderLabels(headers)
        
        for row, row_data in enumerate(data):
            for col, value in enumerate(row_data):
                details_table.setItem(row, col, QTableWidgetItem(value))
                
        details_table.resizeColumnsToContents()
        stats_layout.addWidget(details_table)
        
        self.tabs.addTab(stats_widget, "📊 الإحصائيات العامة")
        
    def create_balances_tab(self):
        """إنشاء تبويب أرصدة الموظفين"""
        balances_widget = QWidget()
        balances_layout = QVBoxLayout(balances_widget)
        
        # شريط البحث
        search_layout = QHBoxLayout()
        
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        search_layout.addWidget(search_label)
        
        search_edit = QLineEdit()
        search_edit.setPlaceholderText("ابحث عن موظف...")
        search_edit.setMinimumHeight(50)
        search_edit.setMaximumHeight(60)
        search_edit.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 15px;
                font-size: 16px;
                min-height: 40px;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
        """)
        search_layout.addWidget(search_edit)
        
        filter_combo = QComboBox()
        filter_combo.addItems(["جميع الموظفين", "رصيد إيجابي", "رصيد سلبي", "رصيد صفر"])
        filter_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                min-width: 150px;
            }
        """)
        search_layout.addWidget(filter_combo)
        
        balances_layout.addLayout(search_layout)
        
        # جدول الأرصدة
        balances_table = QTableWidget()
        balances_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 5px;
                gridline-color: #e0e0e0;
                alternate-background-color: #f8f9fa;
            }
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #34495e, stop: 1 #2c3e50
                );
                color: white;
                border: 1px solid #2c3e50;
                padding: 12px;
                font-weight: bold;
            }
        """)
        
        # بيانات وهمية للأرصدة
        headers = ["اسم الموظف", "الرصيد الابتدائي", "المستخدم", "المتبقي", "الحالة"]
        data = [
            ["أحمد محمد علي", "30", "12", "18", "جيد"],
            ["فاطمة عبد الله", "25", "8", "17", "ممتاز"],
            ["محمد عبد الرحمن", "30", "25", "5", "تحذير"],
            ["عائشة سعيد", "35", "15", "20", "جيد"],
            ["عبد الله أحمد", "30", "30", "0", "منتهي"],
        ]
        
        balances_table.setRowCount(len(data))
        balances_table.setColumnCount(len(headers))
        balances_table.setHorizontalHeaderLabels(headers)
        
        for row, row_data in enumerate(data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(value)
                
                # تلوين الحالة
                if col == 4:  # عمود الحالة
                    if value == "ممتاز":
                        item.setBackground(QColor(212, 237, 218))
                    elif value == "جيد":
                        item.setBackground(QColor(255, 243, 205))
                    elif value == "تحذير":
                        item.setBackground(QColor(248, 215, 218))
                    elif value == "منتهي":
                        item.setBackground(QColor(220, 220, 220))
                        
                balances_table.setItem(row, col, item)
                
        balances_table.resizeColumnsToContents()
        balances_layout.addWidget(balances_table)
        
        self.tabs.addTab(balances_widget, "💰 أرصدة الموظفين")
        
    def create_monthly_tab(self):
        """إنشاء تبويب التقارير الشهرية"""
        monthly_widget = QWidget()
        monthly_layout = QVBoxLayout(monthly_widget)
        
        # اختيار الشهر والسنة
        month_layout = QHBoxLayout()
        
        month_label = QLabel("📅 اختر الشهر:")
        month_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        month_layout.addWidget(month_label)
        
        month_combo = QComboBox()
        months = ["يناير", "فبراير", "مارس", "إبريل", "مايو", "يونيو",
                 "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
        month_combo.addItems(months)
        month_combo.setCurrentIndex(datetime.now().month - 1)
        month_layout.addWidget(month_combo)
        
        year_spinbox = QSpinBox()
        year_spinbox.setRange(2020, 2030)
        year_spinbox.setValue(datetime.now().year)
        month_layout.addWidget(year_spinbox)
        
        month_layout.addStretch()
        
        generate_button = QPushButton("📊 إنشاء التقرير")
        generate_button.setStyleSheet("""
            QPushButton {
                background: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2980b9;
            }
        """)
        month_layout.addWidget(generate_button)
        
        monthly_layout.addLayout(month_layout)
        
        # تقرير الشهر
        monthly_report = QTextEdit()
        monthly_report.setReadOnly(True)
        monthly_report.setMinimumHeight(300)
        monthly_report.setStyleSheet("""
            QTextEdit {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
                font-size: 14px;
                line-height: 1.6;
                min-height: 300px;
            }
        """)
        
        # تقرير وهمي
        monthly_report.setHtml("""
        <h2 style="color: #2c3e50;">📊 تقرير شهر نوفمبر 2024</h2>
        
        <h3 style="color: #3498db;">📋 ملخص الطلبات:</h3>
        <ul>
            <li><strong>إجمالي الطلبات:</strong> 89 طلب</li>
            <li><strong>طلبات يومية:</strong> 65 طلب</li>
            <li><strong>طلبات ساعية:</strong> 24 طلب</li>
            <li><strong>طلبات مرفوضة:</strong> 3 طلبات</li>
        </ul>
        
        <h3 style="color: #27ae60;">💰 حالة الأرصدة:</h3>
        <ul>
            <li><strong>أيام مستخدمة:</strong> 1,245 يوم</li>
            <li><strong>أيام متبقية:</strong> 4,175 يوم</li>
            <li><strong>متوسط الاستخدام:</strong> 5.1 أيام/موظف</li>
        </ul>
        
        <h3 style="color: #e74c3c;">📈 الإحصائيات:</h3>
        <ul>
            <li><strong>أكثر الأقسام استخداماً:</strong> قسم التقنية</li>
            <li><strong>أقل الأقسام استخداماً:</strong> قسم الموارد البشرية</li>
            <li><strong>معدل الموافقة:</strong> 96.6%</li>
        </ul>
        """)
        
        monthly_layout.addWidget(monthly_report)
        
        self.tabs.addTab(monthly_widget, "📅 التقارير الشهرية")
        
    def create_charts_tab(self):
        """إنشاء تبويب الرسوم البيانية"""
        charts_widget = QWidget()
        charts_layout = QVBoxLayout(charts_widget)
        
        # رسالة المخطط
        chart_info = QLabel("""
        <div style="text-align: center; padding: 50px;">
            <h2 style="color: #3498db;">📊 الرسوم البيانية</h2>
            <p style="color: #7f8c8d; font-size: 16px;">
                سيتم إضافة الرسوم البيانية التفاعلية في الإصدار القادم
            </p>
            <p style="color: #95a5a6;">
                🔄 قائمة المخططات المخططة:<br>
                • مخطط دائري لتوزيع الإجازات<br>
                • مخطط بياني لاستخدام الأرصدة<br>
                • مخطط خطي للاتجاهات الشهرية<br>
                • مخطط أعمدة لمقارنة الأقسام
            </p>
        </div>
        """)
        chart_info.setAlignment(Qt.AlignCenter)
        charts_layout.addWidget(chart_info)
        
        self.tabs.addTab(charts_widget, "📈 الرسوم البيانية")
        
    def create_stat_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 {color}, stop: 1 {color}dd
                );
                border-radius: 15px;
                border: none;
                padding: 20px;
                min-height: 120px;
            }}
            QFrame:hover {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 {color}ee, stop: 1 {color}
                );
            }}
        """)
        
        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(10)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            font-size: 36px;
            color: white;
            background: transparent;
        """)
        card_layout.addWidget(icon_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: white;
            background: transparent;
        """)
        card_layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);
            background: transparent;
        """)
        card_layout.addWidget(title_label)
        
        return card
        
    def export_report(self):
        """تصدير التقرير"""
        QMessageBox.information(self, "تصدير", "سيتم تصدير التقرير إلى ملف Excel")
        
    def refresh_reports(self):
        """تحديث التقارير"""
        QMessageBox.information(self, "تحديث", "تم تحديث التقارير بنجاح")

def test_advanced_windows():
    """اختبار النوافذ المتقدمة"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # اختبار نافذة الاستيراد المتقدمة
    import_window = AdvancedImportWindow()
    import_window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    test_advanced_windows()
// qconcatenatetablesproxymodel.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_13_0 -)

class QConcatenateTablesProxyModel : public QAbstractItemModel
{
%TypeHeaderCode
#include <qconcatenatetablesproxymodel.h>
%End

public:
    explicit QConcatenateTablesProxyModel(QObject *parent /TransferThis/ = 0);
    virtual ~QConcatenateTablesProxyModel();
    void addSourceModel(QAbstractItemModel *sourceModel /GetWrapper/);
%MethodCode
        // We want to keep a reference to the model but this is in addition to the
        // existing ones and does not replace them - so we can't use /KeepReference/.
        sipCpp->addSourceModel(a0);
        
        // Use the user object as a list of the references.
        PyObject *user = sipGetUserObject((sipSimpleWrapper *)sipSelf);
        
        if (!user)
        {
            user = PyList_New(0);
            sipSetUserObject((sipSimpleWrapper *)sipSelf, user);
        }
        
        if (user)
            PyList_Append(user, a0Wrapper);
%End

    void removeSourceModel(QAbstractItemModel *sourceModel /GetWrapper/);
%MethodCode
        // Discard the extra model reference that we took in addSourceModel().
        sipCpp->removeSourceModel(a0);
        
        // Use the user object as a list of the references.
        PyObject *user = sipGetUserObject((sipSimpleWrapper *)sipSelf);
        
        if (user)
        {
            Py_ssize_t i = 0;
        
            // Note that we deal with an object appearing in the list more than once.
            while (i < PyList_Size(user))
                if (PyList_GetItem(user, i) == a0Wrapper)
                    PyList_SetSlice(user, i, i + 1, NULL);
                else
                    ++i;
        }
%End

    QModelIndex mapFromSource(const QModelIndex &sourceIndex) const;
    QModelIndex mapToSource(const QModelIndex &proxyIndex) const;
    virtual QVariant data(const QModelIndex &index, int role = Qt::ItemDataRole::DisplayRole) const;
    virtual bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::ItemDataRole::EditRole);
    virtual QMap<int, QVariant> itemData(const QModelIndex &proxyIndex) const;
    virtual bool setItemData(const QModelIndex &index, const QMap<int, QVariant> &roles);
    virtual Qt::ItemFlags flags(const QModelIndex &index) const;
    virtual QModelIndex index(int row, int column, const QModelIndex &parent = QModelIndex()) const;
    virtual QModelIndex parent(const QModelIndex &index) const;
    virtual int rowCount(const QModelIndex &parent = QModelIndex()) const;
    virtual QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::ItemDataRole::DisplayRole) const;
    virtual int columnCount(const QModelIndex &parent = QModelIndex()) const;
    virtual QStringList mimeTypes() const;
    virtual QMimeData *mimeData(const QModelIndexList &indexes) const /TransferBack/;
    virtual bool canDropMimeData(const QMimeData *data, Qt::DropAction action, int row, int column, const QModelIndex &parent) const;
    virtual bool dropMimeData(const QMimeData *data, Qt::DropAction action, int row, int column, const QModelIndex &parent);
    virtual QSize span(const QModelIndex &index) const;
%If (Qt_5_15_0 -)
    QList<QAbstractItemModel *> sourceModels() const;
%End
};

%End

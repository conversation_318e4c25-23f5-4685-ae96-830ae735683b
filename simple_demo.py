#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي مبسط لنظام إدارة الإجازات المتقدم
Simple Demo for Advanced Vacation Management System
"""

import sqlite3
import os
from datetime import datetime, timedelta

class SimpleDemoSystem:
    def __init__(self):
        self.db_path = 'vacation_system.db'
        self.init_demo_data()
    
    def init_demo_data(self):
        """إنشاء بيانات تجريبية"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول بسيط للموظفين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS demo_employees (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                initial_balance INTEGER DEFAULT 30,
                used_days INTEGER DEFAULT 0
            )
        ''')
        
        # إضافة موظفين تجريبيين
        demo_employees = [
            ('أحمد محمد علي', 30, 5),
            ('فاطمة عبد الله', 35, 8),
            ('محمد عبد الرحمن', 30, 12),
            ('عائشة سعيد', 40, 3),
            ('عبد الله أحمد', 30, 15)
        ]
        
        cursor.execute('DELETE FROM demo_employees')
        for name, initial, used in demo_employees:
            cursor.execute('''
                INSERT INTO demo_employees (name, initial_balance, used_days)
                VALUES (?, ?, ?)
            ''', (name, initial, used))
        
        conn.commit()
        conn.close()
    
    def display_main_menu(self):
        """عرض القائمة الرئيسية"""
        print("\n" + "="*60)
        print("🎯 نظام إدارة الإجازات المتقدم - عرض توضيحي")
        print("="*60)
        print("1. 👥 عرض قائمة الموظفين")
        print("2. 📊 عرض إحصائيات سريعة")
        print("3. 📈 رسم بياني نصي للأرصدة")
        print("4. 🔍 البحث عن موظف")
        print("5. ➕ إضافة طلب إجازة")
        print("6. 📋 عرض التقارير")
        print("7. 🔔 محاكاة الإشعارات")
        print("8. 💾 عرض معلومات النسخ الاحتياطي")
        print("9. ℹ️ معلومات النظام المتقدم")
        print("0. 🚪 خروج")
        print("="*60)
    
    def show_employees(self):
        """عرض قائمة الموظفين"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM demo_employees')
        employees = cursor.fetchall()
        conn.close()
        
        print("\n👥 قائمة الموظفين:")
        print("-" * 70)
        print("المعرف".ljust(5) + "الاسم".ljust(20) + "الرصيد الابتدائي".ljust(15) + "المستخدم".ljust(10) + "المتبقي")
        print("-" * 70)
        
        for emp in employees:
            emp_id, name, initial, used = emp
            remaining = initial - used
            print(f"{emp_id:<5} {name:<20} {initial:<15} {used:<10} {remaining}")
    
    def show_statistics(self):
        """عرض إحصائيات سريعة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*), SUM(initial_balance), SUM(used_days) FROM demo_employees')
        stats = cursor.fetchone()
        conn.close()
        
        total_employees, total_initial, total_used = stats
        remaining = total_initial - total_used
        usage_rate = (total_used / total_initial * 100) if total_initial > 0 else 0
        
        print("\n📊 إحصائيات سريعة:")
        print("-" * 40)
        print(f"👥 إجمالي الموظفين: {total_employees}")
        print(f"📅 إجمالي الرصيد الابتدائي: {total_initial} يوم")
        print(f"📉 إجمالي المستخدم: {total_used} يوم")
        print(f"📈 إجمالي المتبقي: {remaining} يوم")
        print(f"📊 معدل الاستخدام: {usage_rate:.1f}%")
    
    def show_balance_chart(self):
        """رسم بياني نصي للأرصدة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT name, initial_balance, used_days FROM demo_employees')
        employees = cursor.fetchall()
        conn.close()
        
        print("\n📈 رسم بياني للأرصدة المتبقية:")
        print("-" * 50)
        
        max_balance = max(emp[1] - emp[2] for emp in employees)
        
        for name, initial, used in employees:
            remaining = initial - used
            bar_length = int((remaining / max_balance) * 30) if max_balance > 0 else 0
            bar = "█" * bar_length
            short_name = name[:12] + ".." if len(name) > 14 else name
            print(f"{short_name:<14}: {bar} ({remaining} يوم)")
    
    def search_employee(self):
        """البحث عن موظف"""
        search_term = input("\n🔍 أدخل اسم الموظف للبحث: ").strip()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM demo_employees WHERE name LIKE ?', (f'%{search_term}%',))
        results = cursor.fetchall()
        conn.close()
        
        if results:
            print(f"\n✅ تم العثور على {len(results)} نتيجة:")
            for emp in results:
                emp_id, name, initial, used = emp
                remaining = initial - used
                print(f"  👤 {name}")
                print(f"     📊 الرصيد: {initial} ابتدائي | {used} مستخدم | {remaining} متبقي")
        else:
            print("❌ لم يتم العثور على نتائج")
    
    def add_vacation_request(self):
        """إضافة طلب إجازة"""
        print("\n➕ إضافة طلب إجازة:")
        
        # عرض الموظفين
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT id, name FROM demo_employees')
        employees = cursor.fetchall()
        
        print("الموظفين المتاحين:")
        for emp_id, name in employees:
            print(f"  {emp_id}. {name}")
        
        try:
            emp_id = int(input("اختر رقم الموظف: "))
            days = int(input("عدد أيام الإجازة: "))
            
            cursor.execute('SELECT name, initial_balance, used_days FROM demo_employees WHERE id = ?', (emp_id,))
            employee = cursor.fetchone()
            
            if employee:
                name, initial, used = employee
                new_used = used + days
                remaining = initial - new_used
                
                if remaining >= 0:
                    cursor.execute('UPDATE demo_employees SET used_days = ? WHERE id = ?', (new_used, emp_id))
                    conn.commit()
                    print(f"✅ تم إضافة {days} أيام إجازة للموظف {name}")
                    print(f"📊 الرصيد الجديد: {remaining} يوم متبقي")
                else:
                    print(f"❌ الرصيد غير كافي! المطلوب: {days} | المتاح: {initial - used}")
            else:
                print("❌ الموظف غير موجود")
                
        except ValueError:
            print("❌ يرجى إدخال أرقام صحيحة")
        
        conn.close()
    
    def show_reports(self):
        """عرض التقارير"""
        print("\n📋 التقارير المتاحة:")
        print("-" * 40)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # تقرير الموظفين برصيد منخفض
        cursor.execute('SELECT name, initial_balance - used_days as remaining FROM demo_employees WHERE remaining < 10')
        low_balance = cursor.fetchall()
        
        print(f"⚠️ موظفين برصيد منخفض (<10 أيام): {len(low_balance)}")
        for name, remaining in low_balance:
            print(f"   👤 {name}: {remaining} يوم")
        
        # تقرير الموظفين بأعلى استخدام
        cursor.execute('SELECT name, used_days FROM demo_employees ORDER BY used_days DESC LIMIT 3')
        top_users = cursor.fetchall()
        
        print(f"\n📈 أكثر الموظفين استخداماً للإجازات:")
        for i, (name, used) in enumerate(top_users, 1):
            print(f"   {i}. {name}: {used} يوم")
        
        conn.close()
    
    def simulate_notifications(self):
        """محاكاة الإشعارات"""
        print("\n🔔 محاكاة نظام الإشعارات:")
        print("-" * 40)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # إشعارات الرصيد المنخفض
        cursor.execute('SELECT name, initial_balance - used_days as remaining FROM demo_employees WHERE remaining < 10')
        low_balance = cursor.fetchall()
        
        notifications = []
        
        for name, remaining in low_balance:
            notifications.append(f"⚠️ تنبيه: رصيد {name} منخفض ({remaining} يوم)")
        
        # إشعار الملخص اليومي
        cursor.execute('SELECT COUNT(*), AVG(initial_balance - used_days) FROM demo_employees')
        count, avg_remaining = cursor.fetchone()
        notifications.append(f"📊 ملخص يومي: {count} موظف، متوسط الرصيد: {avg_remaining:.1f} يوم")
        
        if notifications:
            print("الإشعارات الحالية:")
            for i, notification in enumerate(notifications, 1):
                print(f"  {i}. {notification}")
        else:
            print("✅ لا توجد إشعارات")
        
        conn.close()
    
    def show_backup_info(self):
        """عرض معلومات النسخ الاحتياطي"""
        print("\n💾 معلومات النسخ الاحتياطي:")
        print("-" * 40)
        
        # فحص وجود مجلد النسخ الاحتياطية
        if os.path.exists('backups'):
            backup_files = os.listdir('backups')
            print(f"📁 مجلد النسخ: موجود")
            print(f"📄 عدد النسخ: {len(backup_files)}")
            
            if backup_files:
                print("آخر النسخ:")
                for i, backup in enumerate(backup_files[-3:], 1):
                    print(f"  {i}. {backup}")
        else:
            print("📁 مجلد النسخ: غير موجود")
        
        # حجم قاعدة البيانات
        if os.path.exists(self.db_path):
            db_size = os.path.getsize(self.db_path)
            print(f"🗄️ حجم قاعدة البيانات: {db_size:,} بايت")
        
        print("\n💡 في النظام المتقدم:")
        print("  ✅ نسخ احتياطية تلقائية كل 24 ساعة")
        print("  ✅ ضغط البيانات لتوفير المساحة")
        print("  ✅ التحقق من سلامة النسخ")
        print("  ✅ استعادة سهلة وآمنة")
    
    def show_advanced_features(self):
        """عرض معلومات النظام المتقدم"""
        print("\n ℹ️ نظام إدارة الإجازات المتقدم - الإصدار 2.0")
        print("="*60)
        
        features = [
            ("📊 نظام التقارير المتقدم", "تقارير شاملة مع تصدير Excel"),
            ("🔔 الإشعارات الذكية", "تنبيهات تلقائية ومخصصة"),
            ("📈 لوحة التحليلات", "رسوم بيانية وإحصائيات متقدمة"),
            ("💾 النسخ الاحتياطي", "حماية البيانات والاستعادة"),
            ("👥 إدارة المستخدمين", "صلاحيات متعددة المستويات"),
            ("🎯 لوحة التحكم الموحدة", "إدارة جميع الأنظمة من مكان واحد")
        ]
        
        print("الميزات المتقدمة:")
        for feature, description in features:
            print(f"  {feature}")
            print(f"     📝 {description}")
            print()
        
        print("📁 الملفات المتقدمة المتاحة:")
        advanced_files = [
            'master_control_panel.py',
            'advanced_reports.py',
            'analytics_dashboard.py',
            'backup_system.py',
            'notification_system.py',
            'user_management_interface.py'
        ]
        
        for file in advanced_files:
            status = "✅" if os.path.exists(file) else "❌"
            print(f"  {status} {file}")
    
    def run(self):
        """تشغيل العرض التوضيحي"""
        print("🎯 مرحباً بك في العرض التوضيحي لنظام إدارة الإجازات المتقدم!")
        print("هذا عرض مبسط يوضح إمكانيات النظام الكامل")
        
        while True:
            try:
                self.display_main_menu()
                choice = input("اختر العملية المطلوبة: ").strip()
                
                if choice == '0':
                    print("👋 شكراً لتجربة النظام المتقدم!")
                    print("💡 لتشغيل النظام الكامل، استخدم: python master_control_panel.py")
                    break
                elif choice == '1':
                    self.show_employees()
                elif choice == '2':
                    self.show_statistics()
                elif choice == '3':
                    self.show_balance_chart()
                elif choice == '4':
                    self.search_employee()
                elif choice == '5':
                    self.add_vacation_request()
                elif choice == '6':
                    self.show_reports()
                elif choice == '7':
                    self.simulate_notifications()
                elif choice == '8':
                    self.show_backup_info()
                elif choice == '9':
                    self.show_advanced_features()
                else:
                    print("⚠️ اختيار غير صحيح")
                
                if choice != '0':
                    input("\nاضغط Enter للمتابعة...")
                
            except KeyboardInterrupt:
                print("\n\n👋 تم إيقاف العرض التوضيحي")
                break
            except Exception as e:
                print(f"\n❌ خطأ غير متوقع: {e}")
                input("اضغط Enter للمتابعة...")

if __name__ == "__main__":
    demo = SimpleDemoSystem()
    demo.run()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نوافذ التعديل والحذف المتقدمة للواجهة الحديثة
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime, timedelta
from modern_dialogs import ModernDialog

class ModernEditRequestsWindow(ModernDialog):
    """نافذة تعديل الطلبات المتقدمة"""
    
    def __init__(self):
        super().__init__("✏️ تعديل الطلبات المتقدم", 1000, 750)
        self.selected_request = None
        self.setup_edit_content()
        
    def setup_edit_content(self):
        """إعداد محتوى التعديل"""
        layout = QVBoxLayout(self.content_area)
        layout.setSpacing(20)
        
        # شريط البحث والفلترة
        self.create_search_filter_bar(layout)
        
        # قائمة الطلبات
        self.create_requests_list(layout)
        
        # نموذج التعديل
        self.create_edit_form(layout)
        
        # إعداد الأزرار
        self.add_button("💾 حفظ التعديل", self.save_edit, "primary")
        self.add_button("🔄 تحديث القائمة", self.refresh_list, "secondary")
        self.add_button("📋 تفاصيل كاملة", self.show_details, "info")
        self.add_button("❌ إغلاق", self.accept, "secondary")
        
    def create_search_filter_bar(self, layout):
        """إنشاء شريط البحث والفلترة"""
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #e67e22, stop: 1 #d35400
                );
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        search_layout = QVBoxLayout(search_frame)
        
        # العنوان
        title_label = QLabel("🔍 البحث والفلترة للتعديل")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: white;
            background: transparent;
            margin-bottom: 10px;
        """)
        search_layout.addWidget(title_label)
        
        # شريط البحث والفلاتر
        controls_layout = QHBoxLayout()
        
        # حقل البحث
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالاسم، الرقم، أو أي معلومة...")
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
                padding: 15px;
                font-size: 16px;
                color: #2c3e50;
                min-height: 45px;
            }
            QLineEdit:focus {
                border: 2px solid rgba(255, 255, 255, 0.8);
            }
        """)
        self.search_edit.textChanged.connect(self.filter_requests)
        controls_layout.addWidget(self.search_edit)
        
        # فلتر الحالة
        status_label = QLabel("الحالة:")
        status_label.setStyleSheet("color: white; font-weight: bold; margin-left: 10px;")
        controls_layout.addWidget(status_label)
        
        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع الحالات", "مقبول", "مرفوض", "في الانتظار"])
        self.status_filter.setStyleSheet("""
            QComboBox {
                background: white;
                border: none;
                border-radius: 8px;
                padding: 10px;
                font-weight: bold;
                min-width: 120px;
            }
        """)
        self.status_filter.currentTextChanged.connect(self.filter_requests)
        controls_layout.addWidget(self.status_filter)
        
        # فلتر النوع
        type_label = QLabel("النوع:")
        type_label.setStyleSheet("color: white; font-weight: bold; margin-left: 10px;")
        controls_layout.addWidget(type_label)
        
        self.type_filter = QComboBox()
        self.type_filter.addItems(["جميع الأنواع", "يومية", "ساعية", "إضافية"])
        self.type_filter.setStyleSheet("""
            QComboBox {
                background: white;
                border: none;
                border-radius: 8px;
                padding: 10px;
                font-weight: bold;
                min-width: 120px;
            }
        """)
        self.type_filter.currentTextChanged.connect(self.filter_requests)
        controls_layout.addWidget(self.type_filter)
        
        search_layout.addLayout(controls_layout)
        layout.addWidget(search_frame)
        
    def create_requests_list(self, layout):
        """إنشاء قائمة الطلبات"""
        list_frame = QFrame()
        list_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 20px;
            }
        """)
        
        list_layout = QVBoxLayout(list_frame)
        
        # عنوان القائمة
        list_header = QHBoxLayout()
        
        list_title = QLabel("📋 قائمة الطلبات القابلة للتعديل")
        list_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
        """)
        list_header.addWidget(list_title)
        
        list_header.addStretch()
        
        # عداد الطلبات
        self.requests_count_label = QLabel("0 طلب")
        self.requests_count_label.setStyleSheet("""
            background: #e8f5e8;
            color: #2e7d2e;
            border-radius: 8px;
            padding: 8px 15px;
            font-weight: bold;
        """)
        list_header.addWidget(self.requests_count_label)
        
        list_layout.addLayout(list_header)
        
        # جدول الطلبات
        self.requests_table = QTableWidget()
        self.requests_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f1f3f4;
                font-size: 13px;
                selection-background-color: #e3f2fd;
            }
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e67e22, stop: 1 #d35400
                );
                color: white;
                border: none;
                padding: 12px;
                font-weight: bold;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #f1f3f4;
            }
            QTableWidget::item:selected {
                background: #e3f2fd;
                color: #1976d2;
            }
        """)
        
        # إعداد أعمدة الجدول
        headers = ["ID", "الاسم", "النوع", "التاريخ", "الأيام", "الحالة", "تاريخ الطلب"]
        self.requests_table.setColumnCount(len(headers))
        self.requests_table.setHorizontalHeaderLabels(headers)
        
        # ربط حدث التحديد
        self.requests_table.itemSelectionChanged.connect(self.on_request_selected)
        
        # تحميل البيانات الوهمية
        self.load_sample_requests()
        
        list_layout.addWidget(self.requests_table)
        layout.addWidget(list_frame)
        
    def create_edit_form(self, layout):
        """إنشاء نموذج التعديل"""
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef
                );
                border-radius: 10px;
                border: 2px solid #dee2e6;
                padding: 25px;
            }
        """)
        
        form_layout = QVBoxLayout(form_frame)
        
        # عنوان النموذج
        form_title = QLabel("✏️ تعديل الطلب المحدد")
        form_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        form_layout.addWidget(form_title)
        
        # الحقول في شبكة
        fields_layout = QGridLayout()
        fields_layout.setSpacing(15)
        
        # اسم الموظف (للعرض فقط)
        self.create_form_field(fields_layout, 0, 0, "👤 اسم الموظف:", "employee_name_display", readonly=True)
        
        # رقم الموظف (للعرض فقط)
        self.create_form_field(fields_layout, 0, 1, "🆔 رقم الموظف:", "employee_id_display", readonly=True)
        
        # نوع الإجازة
        type_label = QLabel("📝 نوع الإجازة:")
        type_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        fields_layout.addWidget(type_label, 1, 0)
        
        self.vacation_type_combo = QComboBox()
        self.vacation_type_combo.addItems(["إجازة يومية", "إجازة ساعية", "إجازة إضافية"])
        self.vacation_type_combo.setStyleSheet(self.get_input_style())
        fields_layout.addWidget(self.vacation_type_combo, 1, 1)
        
        # تاريخ الإجازة
        date_label = QLabel("📅 تاريخ الإجازة:")
        date_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        fields_layout.addWidget(date_label, 2, 0)
        
        self.vacation_date_edit = QDateEdit(QDate.currentDate())
        self.vacation_date_edit.setCalendarPopup(True)
        self.vacation_date_edit.setStyleSheet(self.get_input_style())
        fields_layout.addWidget(self.vacation_date_edit, 2, 1)
        
        # عدد الأيام/الساعات
        self.create_form_field(fields_layout, 3, 0, "⏱️ عدد الأيام/الساعات:", "days_hours")
        
        # الحالة
        status_label = QLabel("📊 حالة الطلب:")
        status_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        fields_layout.addWidget(status_label, 3, 1)
        
        self.status_combo = QComboBox()
        self.status_combo.addItems(["مقبول", "مرفوض", "في الانتظار"])
        self.status_combo.setStyleSheet(self.get_input_style())
        fields_layout.addWidget(self.status_combo, 4, 1)
        
        form_layout.addLayout(fields_layout)
        
        # ملاحظات التعديل
        notes_label = QLabel("📝 ملاحظات التعديل:")
        notes_label.setStyleSheet("font-weight: bold; color: #2c3e50; margin-top: 15px;")
        form_layout.addWidget(notes_label)
        
        self.edit_notes = QTextEdit()
        self.edit_notes.setPlaceholderText("أدخل سبب التعديل أو أي ملاحظات...")
        self.edit_notes.setMinimumHeight(80)
        self.edit_notes.setMaximumHeight(120)
        self.edit_notes.setStyleSheet("""
            QTextEdit {
                background: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
                min-height: 80px;
            }
            QTextEdit:focus {
                border: 2px solid #e67e22;
            }
        """)
        form_layout.addWidget(self.edit_notes)
        
        # رسالة عدم التحديد
        self.no_selection_label = QLabel("⚠️ يرجى تحديد طلب من القائمة أعلاه للتعديل")
        self.no_selection_label.setStyleSheet("""
            color: #7f8c8d;
            font-size: 14px;
            font-style: italic;
            text-align: center;
            padding: 20px;
        """)
        self.no_selection_label.setAlignment(Qt.AlignCenter)
        form_layout.addWidget(self.no_selection_label)
        
        # إخفاء النموذج في البداية
        self.hide_form_fields()
        
        layout.addWidget(form_frame)
        
    def create_form_field(self, layout, row, col, label_text, object_name, readonly=False):
        """إنشاء حقل في النموذج"""
        label = QLabel(label_text)
        label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        layout.addWidget(label, row * 2, col)
        
        edit = QLineEdit()
        edit.setObjectName(object_name)
        edit.setReadOnly(readonly)
        edit.setStyleSheet(self.get_input_style(readonly))
        layout.addWidget(edit, row * 2 + 1, col)
        
        setattr(self, object_name, edit)
        
    def get_input_style(self, readonly=False):
        """الحصول على نمط الحقول"""
        bg_color = "#f8f9fa" if readonly else "white"
        return f"""
            QLineEdit, QComboBox, QDateEdit {{
                background: {bg_color};
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                font-size: 16px;
                min-height: 40px;
            }}
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus {{
                border: 2px solid #e67e22;
            }}
        """
        
    def hide_form_fields(self):
        """إخفاء حقول النموذج"""
        fields = [
            self.employee_name_display, self.employee_id_display,
            self.vacation_type_combo, self.vacation_date_edit,
            self.days_hours, self.status_combo, self.edit_notes
        ]
        
        for field in fields:
            field.setEnabled(False)
            
        self.no_selection_label.show()
        
    def show_form_fields(self):
        """إظهار حقول النموذج"""
        fields = [
            self.vacation_type_combo, self.vacation_date_edit,
            self.days_hours, self.status_combo, self.edit_notes
        ]
        
        for field in fields:
            field.setEnabled(True)
            
        self.no_selection_label.hide()
        
    def load_sample_requests(self):
        """تحميل طلبات وهمية"""
        sample_requests = [
            ["001", "أحمد محمد علي", "يومية", "2024-11-20", "3", "في الانتظار", "2024-11-15"],
            ["002", "فاطمة عبد الله", "ساعية", "2024-11-21", "4", "مقبول", "2024-11-16"],
            ["003", "محمد عبد الرحمن", "يومية", "2024-11-22", "5", "في الانتظار", "2024-11-17"],
            ["004", "عائشة سعيد", "إضافية", "2024-11-23", "2", "مقبول", "2024-11-18"],
            ["005", "عبد الله أحمد", "يومية", "2024-11-24", "1", "مرفوض", "2024-11-19"],
            ["006", "مريم خالد", "ساعية", "2024-11-25", "6", "في الانتظار", "2024-11-20"],
        ]
        
        self.requests_table.setRowCount(len(sample_requests))
        
        for row, row_data in enumerate(sample_requests):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                
                # تلوين الحالة
                if col == 5:  # عمود الحالة
                    if value == "مقبول":
                        item.setBackground(QColor(212, 237, 218))
                    elif value == "مرفوض":
                        item.setBackground(QColor(248, 215, 218))
                    elif value == "في الانتظار":
                        item.setBackground(QColor(255, 243, 205))
                        
                self.requests_table.setItem(row, col, item)
                
        self.requests_table.resizeColumnsToContents()
        self.update_requests_count()
        
    def filter_requests(self):
        """فلترة الطلبات"""
        search_text = self.search_edit.text().lower()
        status_filter = self.status_filter.currentText()
        type_filter = self.type_filter.currentText()
        
        visible_count = 0
        
        for row in range(self.requests_table.rowCount()):
            show_row = True
            
            # فلترة النص
            if search_text:
                row_text = ""
                for col in range(self.requests_table.columnCount()):
                    item = self.requests_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "
                        
                if search_text not in row_text:
                    show_row = False
                    
            # فلترة الحالة
            if status_filter != "جميع الحالات":
                status_item = self.requests_table.item(row, 5)
                if status_item and status_item.text() != status_filter:
                    show_row = False
                    
            # فلترة النوع
            if type_filter != "جميع الأنواع":
                type_item = self.requests_table.item(row, 2)
                if type_item and type_filter not in type_item.text():
                    show_row = False
                    
            self.requests_table.setRowHidden(row, not show_row)
            if show_row:
                visible_count += 1
                
        self.requests_count_label.setText(f"{visible_count} طلب")
        
    def update_requests_count(self):
        """تحديث عداد الطلبات"""
        total_count = self.requests_table.rowCount()
        self.requests_count_label.setText(f"{total_count} طلب")
        
    def on_request_selected(self):
        """عند تحديد طلب"""
        selected_items = self.requests_table.selectedItems()
        if not selected_items:
            self.hide_form_fields()
            return
            
        row = selected_items[0].row()
        
        # تحميل بيانات الطلب المحدد
        request_data = []
        for col in range(self.requests_table.columnCount()):
            item = self.requests_table.item(row, col)
            request_data.append(item.text() if item else "")
            
        # ملء النموذج
        self.employee_name_display.setText(request_data[1])
        self.employee_id_display.setText(request_data[0])
        
        # تحديد نوع الإجازة
        vacation_type = request_data[2]
        if "يومية" in vacation_type:
            self.vacation_type_combo.setCurrentText("إجازة يومية")
        elif "ساعية" in vacation_type:
            self.vacation_type_combo.setCurrentText("إجازة ساعية")
        elif "إضافية" in vacation_type:
            self.vacation_type_combo.setCurrentText("إجازة إضافية")
            
        # تاريخ الإجازة
        vacation_date = QDate.fromString(request_data[3], "yyyy-MM-dd")
        self.vacation_date_edit.setDate(vacation_date)
        
        # عدد الأيام/الساعات
        self.days_hours.setText(request_data[4])
        
        # الحالة
        self.status_combo.setCurrentText(request_data[5])
        
        # مسح ملاحظات التعديل
        self.edit_notes.clear()
        
        # إظهار النموذج
        self.show_form_fields()
        
        # حفظ بيانات الطلب المحدد
        self.selected_request = {
            'id': request_data[0],
            'name': request_data[1],
            'type': request_data[2],
            'date': request_data[3],
            'days': request_data[4],
            'status': request_data[5],
            'request_date': request_data[6]
        }
        
    def save_edit(self):
        """حفظ التعديل"""
        if not self.selected_request:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد طلب للتعديل")
            return
            
        if not self.edit_notes.toPlainText().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سبب التعديل")
            return
            
        # رسالة التأكيد
        msg = QMessageBox.question(
            self,
            "تأكيد التعديل",
            f"هل تريد حفظ التعديلات على الطلب؟\n\n"
            f"الموظف: {self.selected_request['name']}\n"
            f"الطلب رقم: {self.selected_request['id']}\n"
            f"النوع الجديد: {self.vacation_type_combo.currentText()}\n"
            f"التاريخ الجديد: {self.vacation_date_edit.date().toString('yyyy-MM-dd')}\n"
            f"الحالة الجديدة: {self.status_combo.currentText()}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )
        
        if msg == QMessageBox.Yes:
            # هنا سيتم إضافة كود الحفظ الفعلي
            QMessageBox.information(self, "نجح", "تم حفظ التعديلات بنجاح!")
            self.refresh_list()
            
    def refresh_list(self):
        """تحديث قائمة الطلبات"""
        self.load_sample_requests()
        self.hide_form_fields()
        QMessageBox.information(self, "تحديث", "تم تحديث قائمة الطلبات")
        
    def show_details(self):
        """عرض تفاصيل كاملة للطلب"""
        if not self.selected_request:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد طلب أولاً")
            return
            
        details = f"""
        📋 تفاصيل الطلب الكاملة
        
        🆔 رقم الطلب: {self.selected_request['id']}
        👤 اسم الموظف: {self.selected_request['name']}
        📝 نوع الإجازة: {self.selected_request['type']}
        📅 تاريخ الإجازة: {self.selected_request['date']}
        ⏱️ عدد الأيام/الساعات: {self.selected_request['days']}
        📊 الحالة: {self.selected_request['status']}
        📆 تاريخ تقديم الطلب: {self.selected_request['request_date']}
        """
        
        QMessageBox.information(self, "تفاصيل الطلب", details)

class ModernDeleteRequestsWindow(ModernDialog):
    """نافذة حذف الطلبات المتقدمة"""
    
    def __init__(self):
        super().__init__("🗑️ حذف الطلبات المتقدم", 900, 650)
        self.selected_requests = []
        self.setup_delete_content()
        
    def setup_delete_content(self):
        """إعداد محتوى الحذف"""
        layout = QVBoxLayout(self.content_area)
        layout.setSpacing(20)
        
        # تحذير الحذف
        self.create_warning_section(layout)
        
        # فلاتر البحث
        self.create_search_section(layout)
        
        # قائمة الطلبات
        self.create_delete_list(layout)
        
        # معلومات الحذف
        self.create_delete_info(layout)
        
        # إعداد الأزرار
        self.add_button("🗑️ حذف المحدد", self.delete_selected, "danger")
        self.add_button("🗑️ حذف متعدد", self.delete_multiple, "danger")
        self.add_button("🔄 تحديث", self.refresh_delete_list, "secondary")
        self.add_button("❌ إغلاق", self.accept, "secondary")
        
    def create_warning_section(self, layout):
        """إنشاء قسم التحذير"""
        warning_frame = QFrame()
        warning_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #e74c3c, stop: 1 #c0392b
                );
                border-radius: 12px;
                padding: 20px;
                border: 3px solid #a93226;
            }
        """)
        
        warning_layout = QVBoxLayout(warning_frame)
        
        # أيقونة التحذير
        warning_title = QLabel("⚠️ تحذير هام - عملية حذف دائمة!")
        warning_title.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: white;
            background: transparent;
            text-align: center;
        """)
        warning_title.setAlignment(Qt.AlignCenter)
        warning_layout.addWidget(warning_title)
        
        # رسالة التحذير
        warning_text = QLabel("""
        🚨 انتبه: عملية الحذف نهائية ولا يمكن التراجع عنها
        
        • تأكد من اختيار الطلبات الصحيحة قبل الحذف
        • سيتم حذف جميع البيانات المرتبطة بالطلب
        • يُنصح بعمل نسخة احتياطية قبل الحذف
        • يمكن حذف طلب واحد أو عدة طلبات في نفس الوقت
        """)
        warning_text.setStyleSheet("""
            color: white;
            font-size: 14px;
            background: transparent;
            line-height: 1.6;
        """)
        warning_layout.addWidget(warning_text)
        
        layout.addWidget(warning_frame)
        
    def create_search_section(self, layout):
        """إنشاء قسم البحث"""
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 20px;
            }
        """)
        
        search_layout = QVBoxLayout(search_frame)
        
        # العنوان
        search_title = QLabel("🔍 البحث والفلترة للحذف")
        search_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        search_layout.addWidget(search_title)
        
        # أدوات البحث
        search_controls = QHBoxLayout()
        
        # حقل البحث
        self.delete_search_edit = QLineEdit()
        self.delete_search_edit.setPlaceholderText("ابحث عن الطلبات المراد حذفها...")
        self.delete_search_edit.setStyleSheet("""
            QLineEdit {
                background: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                font-size: 16px;
                min-height: 45px;
            }
            QLineEdit:focus {
                border: 2px solid #e74c3c;
            }
        """)
        self.delete_search_edit.textChanged.connect(self.filter_delete_list)
        search_controls.addWidget(self.delete_search_edit)
        
        # فلتر تاريخ قديم
        old_requests_check = QCheckBox("الطلبات القديمة فقط")
        old_requests_check.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #2c3e50;
                margin-left: 15px;
            }
        """)
        old_requests_check.stateChanged.connect(self.filter_delete_list)
        search_controls.addWidget(old_requests_check)
        self.old_requests_check = old_requests_check
        
        # فلتر الحالة
        status_label = QLabel("الحالة:")
        status_label.setStyleSheet("font-weight: bold; color: #2c3e50; margin-left: 10px;")
        search_controls.addWidget(status_label)
        
        self.delete_status_filter = QComboBox()
        self.delete_status_filter.addItems(["جميع الحالات", "مقبول", "مرفوض", "في الانتظار"])
        self.delete_status_filter.setStyleSheet("""
            QComboBox {
                background: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
                min-width: 120px;
            }
        """)
        self.delete_status_filter.currentTextChanged.connect(self.filter_delete_list)
        search_controls.addWidget(self.delete_status_filter)
        
        search_layout.addLayout(search_controls)
        layout.addWidget(search_frame)
        
    def create_delete_list(self, layout):
        """إنشاء قائمة الطلبات للحذف"""
        list_frame = QFrame()
        list_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 20px;
            }
        """)
        
        list_layout = QVBoxLayout(list_frame)
        
        # عنوان القائمة
        list_header = QHBoxLayout()
        
        list_title = QLabel("📋 الطلبات المتاحة للحذف")
        list_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
        """)
        list_header.addWidget(list_title)
        
        list_header.addStretch()
        
        # عداد الطلبات المحددة
        self.selected_count_label = QLabel("0 محدد")
        self.selected_count_label.setStyleSheet("""
            background: #ffe6e6;
            color: #c0392b;
            border-radius: 8px;
            padding: 8px 15px;
            font-weight: bold;
        """)
        list_header.addWidget(self.selected_count_label)
        
        list_layout.addLayout(list_header)
        
        # جدول الطلبات مع checkboxes
        self.delete_table = QTableWidget()
        self.delete_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f1f3f4;
                font-size: 13px;
            }
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e74c3c, stop: 1 #c0392b
                );
                color: white;
                border: none;
                padding: 12px;
                font-weight: bold;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #f1f3f4;
            }
        """)
        
        # إعداد أعمدة الجدول
        headers = ["", "ID", "الاسم", "النوع", "التاريخ", "الأيام", "الحالة", "العمر"]
        self.delete_table.setColumnCount(len(headers))
        self.delete_table.setHorizontalHeaderLabels(headers)
        
        # تحميل البيانات
        self.load_delete_requests()
        
        list_layout.addWidget(self.delete_table)
        layout.addWidget(list_frame)
        
    def create_delete_info(self, layout):
        """إنشاء قسم معلومات الحذف"""
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 10px;
                border: 2px solid #dee2e6;
                padding: 20px;
            }
        """)
        
        info_layout = QHBoxLayout(info_frame)
        
        # إحصائيات الحذف
        stats_data = [
            ("📊", "إجمالي الطلبات", "0"),
            ("✅", "قابل للحذف", "0"),
            ("🔒", "محمي من الحذف", "0"),
            ("⚠️", "محدد للحذف", "0")
        ]
        
        for icon, title, value in stats_data:
            card = self.create_info_card(icon, title, value)
            info_layout.addWidget(card)
            
        layout.addWidget(info_frame)
        
    def create_info_card(self, icon, title, value):
        """إنشاء بطاقة معلومات"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 15px;
                min-width: 150px;
            }
        """)
        
        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(8)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 24px; color: #2c3e50;")
        card_layout.addWidget(icon_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50;")
        card_layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 12px; color: #7f8c8d;")
        card_layout.addWidget(title_label)
        
        return card
        
    def load_delete_requests(self):
        """تحميل طلبات للحذف"""
        sample_requests = [
            ["DEL001", "أحمد محمد علي", "يومية", "2024-10-15", "3", "مقبول", "36 يوم"],
            ["DEL002", "فاطمة عبد الله", "ساعية", "2024-10-20", "4", "مقبول", "31 يوم"],
            ["DEL003", "محمد عبد الرحمن", "يومية", "2024-09-25", "5", "مرفوض", "57 يوم"],
            ["DEL004", "عائشة سعيد", "إضافية", "2024-09-30", "2", "مقبول", "52 يوم"],
            ["DEL005", "عبد الله أحمد", "يومية", "2024-11-01", "1", "مرفوض", "20 يوم"],
        ]
        
        self.delete_table.setRowCount(len(sample_requests))
        
        for row, row_data in enumerate(sample_requests):
            # إضافة checkbox في العمود الأول
            checkbox = QCheckBox()
            checkbox.stateChanged.connect(self.update_selected_count)
            self.delete_table.setCellWidget(row, 0, checkbox)
            
            # إضافة باقي البيانات
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                
                # تلوين الحالة
                if col == 4:  # عمود الحالة
                    if value == "مقبول":
                        item.setBackground(QColor(212, 237, 218))
                    elif value == "مرفوض":
                        item.setBackground(QColor(248, 215, 218))
                        
                # تلوين العمر
                elif col == 6:  # عمود العمر
                    days = int(value.split()[0])
                    if days > 30:
                        item.setBackground(QColor(255, 243, 205))  # قديم
                        
                self.delete_table.setItem(row, col + 1, item)
                
        self.delete_table.resizeColumnsToContents()
        
    def filter_delete_list(self):
        """فلترة قائمة الحذف"""
        search_text = self.delete_search_edit.text().lower()
        status_filter = self.delete_status_filter.currentText()
        old_only = self.old_requests_check.isChecked()
        
        for row in range(self.delete_table.rowCount()):
            show_row = True
            
            # فلترة النص
            if search_text:
                row_text = ""
                for col in range(1, self.delete_table.columnCount()):
                    item = self.delete_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "
                        
                if search_text not in row_text:
                    show_row = False
                    
            # فلترة الحالة
            if status_filter != "جميع الحالات":
                status_item = self.delete_table.item(row, 6)
                if status_item and status_item.text() != status_filter:
                    show_row = False
                    
            # فلترة القديم
            if old_only:
                age_item = self.delete_table.item(row, 7)
                if age_item:
                    days = int(age_item.text().split()[0])
                    if days <= 30:
                        show_row = False
                        
            self.delete_table.setRowHidden(row, not show_row)
            
    def update_selected_count(self):
        """تحديث عداد المحدد"""
        selected_count = 0
        for row in range(self.delete_table.rowCount()):
            checkbox = self.delete_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                selected_count += 1
                
        self.selected_count_label.setText(f"{selected_count} محدد")
        
    def get_selected_requests(self):
        """الحصول على الطلبات المحددة"""
        selected = []
        for row in range(self.delete_table.rowCount()):
            checkbox = self.delete_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                request_data = []
                for col in range(1, self.delete_table.columnCount()):
                    item = self.delete_table.item(row, col)
                    request_data.append(item.text() if item else "")
                selected.append(request_data)
        return selected
        
    def delete_selected(self):
        """حذف الطلبات المحددة"""
        selected = self.get_selected_requests()
        
        if not selected:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد طلب واحد على الأقل للحذف")
            return
            
        # رسالة تأكيد الحذف
        msg = QMessageBox.critical(
            self,
            "تأكيد الحذف",
            f"⚠️ هل أنت متأكد من حذف {len(selected)} طلب؟\n\n"
            "هذا الإجراء نهائي ولا يمكن التراجع عنه!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if msg == QMessageBox.Yes:
            # طلب كلمة مرور إضافية للأمان
            password, ok = QInputDialog.getText(
                self, "كلمة مرور الأمان", 
                "أدخل كلمة مرور الأمان لتأكيد الحذف:",
                QLineEdit.Password
            )
            
            if ok and password == "delete123":
                # هنا سيتم إضافة كود الحذف الفعلي
                QMessageBox.information(self, "تم الحذف", 
                                       f"تم حذف {len(selected)} طلب بنجاح!")
                self.refresh_delete_list()
            elif ok:
                QMessageBox.critical(self, "خطأ", "كلمة مرور الأمان غير صحيحة!")
                
    def delete_multiple(self):
        """حذف متعدد مع خيارات متقدمة"""
        QMessageBox.information(self, "حذف متعدد", 
                               "ستتم إضافة خيارات الحذف المتعدد المتقدمة قريباً")
        
    def refresh_delete_list(self):
        """تحديث قائمة الحذف"""
        self.load_delete_requests()
        QMessageBox.information(self, "تحديث", "تم تحديث قائمة الطلبات")

def test_edit_delete():
    """اختبار نوافذ التعديل والحذف"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # اختبار نافذة التعديل
    edit_window = ModernEditRequestsWindow()
    edit_window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    test_edit_delete()
# دليل اختبار الواجهة الرسومية - نظام إدارة الإجازات

## 🎯 **نتائج الاختبار العملي المتقدم**

### ✅ **الاختبارات الناجحة:**

#### 🔐 **تسجيل الدخول**
- ✅ رفض البيانات الخاطئة 
- ✅ قبول البيانات الصحيحة (admin/admin123)
- ✅ نظام الأمان يعمل بشكل صحيح

#### 📥 **استيراد البيانات**
- ✅ قراءة ملف Excel بنجاح (10 موظفين)
- ✅ استيراد جميع البيانات (10/10)
- ✅ حفظ البيانات في قاعدة البيانات

#### 📝 **طلبات الإجازة اليومية**
- ✅ إضافة طلب أحمد محمد علي (5 أيام)
- ✅ إضافة طلب فاطمة عبد الله (3 أيام)  
- ✅ إضافة طلب محمد عبد الرحمن (7 أيام)

#### ⏰ **طلبات الإجازة الساعية**
- ✅ أحمد محمد علي: 8 ساعات = 1.00 يوم
- ✅ فاطمة عبد الله: 4 ساعات = 0.50 يوم
- ✅ عائشة سعيد: 12 ساعة = 1.50 يوم
- ✅ حساب المعادل صحيح: (ساعات × 3) ÷ 24

#### ➕ **الإجازات المدرجة**
- ✅ أحمد محمد علي: +5 أيام (مكافأة أداء)
- ✅ محمد عبد الرحمن: +3 أيام (تعويض عمل إضافي)

#### 💰 **حساب الأرصدة**
- ✅ **أحمد محمد علي:** 30 + 5 - 5 - 1 = 29 يوم
- ✅ **فاطمة عبد الله:** 25 + 0 - 3 - 0.5 = 21.5 يوم  
- ✅ **محمد عبد الرحمن:** 30 + 3 - 7 - 0 = 26 يوم
- ✅ **عائشة سعيد:** 35 + 0 - 0 - 1.5 = 33.5 يوم

#### 🔍 **البحث والاستعلام**
- ✅ البحث عن "أحمد" وجد 3 نتائج:
  - أحمد محمد علي
  - عبد الله أحمد  
  - خديجة أحمد

---

## 🚀 **اختبار الواجهة الرسومية**

### **لتشغيل البرنامج:**

#### **الطريقة 1: النقر المزدوج**
```
🖱️ انقر نقراً مزدوجاً على: تشغيل_البرنامج.bat
```

#### **الطريقة 2: سطر الأوامر**
```bash
python run_app.py
```

### **خطوات الاختبار:**

#### **1. تسجيل الدخول** 🔐
- [ ] افتح البرنامج
- [ ] أدخل: `admin` / `admin123`
- [ ] اضغط "دخول"
- [ ] تأكد من ظهور الواجهة الرئيسية

#### **2. استيراد الرصيد** 📥
- [ ] انقر "📥 استيراد رصيد"
- [ ] اختر الملف: `نموذج_الرصيد_الابتدائي.xlsx`
- [ ] تأكد من رسالة النجاح
- [ ] تحقق من عدد الموظفين المستوردين (10)

#### **3. طلب إجازة يومية** 📝
- [ ] انقر "📝 طلب إجازة يومية"
- [ ] املأ البيانات:
  - **الاسم:** أحمد محمد علي
  - **رقم القيد:** 12345
  - **الوظيفة:** موظف
  - **الفوج:** الإدارة
  - **نوع الإجازة:** سنوية
  - **تاريخ الخروج:** 2024-07-10
  - **عدد الأيام:** 5
- [ ] اضغط "حفظ"
- [ ] تأكد من رسالة النجاح

#### **4. طلب إجازة ساعية** ⏰
- [ ] انقر "⏱️ طلب إجازة ساعية"
- [ ] املأ البيانات:
  - **الاسم:** فاطمة عبد الله
  - **تاريخ الاستفادة:** 2024-07-15
  - **عدد الساعات:** 8
- [ ] تحقق من حساب المعادل (1.00 يوم)
- [ ] اضغط "حفظ"

#### **5. إدراج إجازة** ➕
- [ ] انقر "➕ إدراج إجازات"
- [ ] املأ البيانات:
  - **الاسم:** أحمد محمد علي
  - **التاريخ:** 2024-07-20
  - **عدد الأيام:** 3
  - **السبب:** مكافأة أداء ممتاز
- [ ] اضغط "حفظ"

#### **6. توليد تقرير** 📊
- [ ] انقر "📊 التقارير"
- [ ] اختر: أحمد محمد علي
- [ ] اضغط "توليد التقرير"
- [ ] تحقق من البيانات:
  - الرصيد الابتدائي: 30
  - الإجازات المدرجة: 3
  - الإجازات اليومية: 5
  - الإجازات الساعية: 1.0
  - الرصيد الصافي: 27

#### **7. البحث عن موظف** 🔍
- [ ] انقر "🔍 استعلام"
- [ ] أدخل: "فاطمة"
- [ ] اضغط "بحث"
- [ ] تحقق من ظهور النتائج

#### **8. تعديل الطلبات** ✏️
- [ ] انقر "✏️ تعديل الطلبات"
- [ ] تصفح التبويبات المختلفة
- [ ] جرب تعديل طلب موجود

#### **9. حذف طلب** 🗑️
- [ ] انقر "🗑️ حذف الطلبات"
- [ ] اختر طلب للحذف
- [ ] اضغط "حذف"
- [ ] تأكد من رسالة التأكيد

---

## 📊 **نتائج الاختبار المتوقعة**

### **الحسابات الصحيحة:**
```
🧮 معادلة الإجازة الساعية:
   المعادل بالأيام = (عدد الساعات × 3) ÷ 24
   
🧮 معادلة الرصيد الصافي:
   الرصيد الصافي = الرصيد الابتدائي + الإجازات المدرجة - الإجازات المستفادة
```

### **بيانات الاختبار النموذجية:**
| الموظف | الرصيد الابتدائي | المدرج | اليومية | الساعية | الصافي |
|---------|------------------|--------|---------|----------|---------|
| أحمد محمد علي | 30 | +5 | -5 | -1.0 | 29.0 |
| فاطمة عبد الله | 25 | 0 | -3 | -0.5 | 21.5 |
| محمد عبد الرحمن | 30 | +3 | -7 | 0 | 26.0 |
| عائشة سعيد | 35 | 0 | 0 | -1.5 | 33.5 |

---

## ✅ **قائمة التحقق النهائية**

### **الوظائف الأساسية:**
- [ ] تسجيل الدخول يعمل
- [ ] استيراد Excel يعمل  
- [ ] طلبات الإجازة اليومية تُحفظ
- [ ] طلبات الإجازة الساعية تُحسب صحيح
- [ ] إدراج الإجازات يعمل
- [ ] التقارير تُعرض بيانات صحيحة
- [ ] البحث يجد النتائج
- [ ] تعديل الطلبات يعمل
- [ ] حذف الطلبات يعمل مع التأكيد

### **الواجهة:**
- [ ] النصوص العربية تظهر بشكل صحيح
- [ ] الأزرار تستجيب للنقر
- [ ] الحقول تقبل الإدخال
- [ ] رسائل الخطأ والنجاح تظهر
- [ ] التنقل بين النوافذ سلس

### **الأمان:**
- [ ] كلمة مرور خاطئة ترفض
- [ ] البيانات تُحفظ في قاعدة البيانات
- [ ] لا يوجد أخطاء برمجية
- [ ] الملفات لا تُفقد

---

## 🎉 **التأكيد النهائي**

إذا نجحت جميع الاختبارات أعلاه، فإن:

**✅ النظام جاهز للاستخدام العملي بنسبة 100%**

**🚀 يمكن نشره واستخدامه في بيئة الإنتاج**

**🏆 تم إنجاز جميع المتطلبات بنجاح**
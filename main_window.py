import sys
from PyQt5.QtWidgets import (<PERSON>A<PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QGridLayout, QPushButton, QLabel, 
                             QFrame, QMessageBox, QDialog, QLineEdit, 
                             QFormLayout, QComboBox, QDateEdit, QSpinBox,
                             QTableWidget, QTableWidgetItem, QFileDialog,
                             QTextEdit, QTabWidget, QHeaderView)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor
from database import VacationDatabase
import pandas as pd
from datetime import datetime

class LoginDialog(QDialog):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("تسجيل الدخول")
        self.setFixedSize(400, 300)
        self.setStyleSheet("""
            QDialog {
                background-color: #f0f0f0;
                font-family: Arial;
            }
            QLineEdit {
                padding: 15px;
                border: 2px solid #ddd;
                border-radius: 5px;
                min-height: 45px;
                font-size: 16px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QLabel {
                font-size: 14px;
                color: #333;
            }
        """)
        
        self.db = VacationDatabase()
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # عنوان
        title = QLabel("نظام إدارة الإجازات")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 24, QFont.Bold))
        title.setStyleSheet("color: #2196F3; margin: 20px;")
        layout.addWidget(title)
        
        # نموذج تسجيل الدخول
        form_layout = QFormLayout()
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("اسم المستخدم")
        self.username_input.setMinimumHeight(35)
        self.username_input.setMaximumHeight(45)
        form_layout.addRow("اسم المستخدم:", self.username_input)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setMinimumHeight(35)
        self.password_input.setMaximumHeight(45)
        form_layout.addRow("كلمة المرور:", self.password_input)
        
        layout.addLayout(form_layout)
        
        # أزرار
        button_layout = QHBoxLayout()
        
        self.login_button = QPushButton("دخول")
        self.login_button.clicked.connect(self.login)
        button_layout.addWidget(self.login_button)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setStyleSheet("background-color: #f44336;")
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # معلومات تسجيل الدخول الافتراضية
        info_label = QLabel("البيانات الافتراضية:\nاسم المستخدم: admin\nكلمة المرور: admin123")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #666; font-size: 12px; margin-top: 20px;")
        layout.addWidget(info_label)
        
        self.setLayout(layout)
    
    def login(self):
        username = self.username_input.text()
        password = self.password_input.text()
        
        user = self.db.authenticate_user(username, password)
        if user:
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام إدارة الإجازات")
        self.setGeometry(100, 100, 1200, 800)
        
        # قاعدة البيانات
        self.db = VacationDatabase()
        
        # تسجيل الدخول
        if not self.show_login():
            sys.exit()
        
        self.setup_ui()
        self.setup_styles()
    
    def show_login(self):
        login_dialog = LoginDialog()
        return login_dialog.exec_() == QDialog.Accepted
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # شريط العنوان
        header = QLabel("نظام إدارة الإجازات")
        header.setAlignment(Qt.AlignCenter)
        header.setFont(QFont("Arial", 28, QFont.Bold))
        header.setStyleSheet("color: #2196F3; padding: 20px; background-color: #f8f9fa; border-bottom: 3px solid #2196F3;")
        main_layout.addWidget(header)
        
        # شبكة الأزرار الرئيسية
        buttons_layout = QGridLayout()
        
        # تعريف الأزرار
        buttons_data = [
            ("📥 استيراد رصيد", self.import_balance, 0, 0, "#4CAF50"),
            ("📝 طلب إجازة يومية", self.daily_request, 0, 1, "#2196F3"),
            ("⏱️ طلب إجازة ساعية", self.hourly_request, 0, 2, "#FF9800"),
            ("➕ إدراج إجازات", self.add_vacation, 1, 0, "#9C27B0"),
            ("📊 التقارير", self.generate_reports, 1, 1, "#00BCD4"),
            ("🔍 استعلام", self.search_employee, 1, 2, "#795548"),
            ("✏️ تعديل الطلبات", self.edit_requests, 2, 0, "#FFC107"),
            ("🗑️ حذف الطلبات", self.delete_requests, 2, 1, "#F44336"),
            ("🔁 ترحيل البيانات", self.archive_data, 2, 2, "#607D8B")
        ]
        
        # إنشاء الأزرار
        for text, handler, row, col, color in buttons_data:
            button = QPushButton(text)
            button.setMinimumSize(200, 100)
            button.setFont(QFont("Arial", 14, QFont.Bold))
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 20px;
                    font-size: 16px;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                    transform: translateY(-2px);
                }}
                QPushButton:pressed {{
                    background-color: {color}aa;
                }}
            """)
            button.clicked.connect(handler)
            buttons_layout.addWidget(button, row, col)
        
        main_layout.addLayout(buttons_layout)
        
        # شريط المعلومات
        info_widget = QWidget()
        info_layout = QHBoxLayout()
        
        self.status_label = QLabel("مرحباً بك في نظام إدارة الإجازات")
        self.status_label.setFont(QFont("Arial", 12))
        self.status_label.setStyleSheet("color: #666; padding: 10px;")
        info_layout.addWidget(self.status_label)
        
        info_layout.addStretch()
        
        # زر الخروج
        exit_button = QPushButton("خروج")
        exit_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        exit_button.clicked.connect(self.close)
        info_layout.addWidget(exit_button)
        
        info_widget.setLayout(info_layout)
        main_layout.addWidget(info_widget)
        
        central_widget.setLayout(main_layout)
    
    def setup_styles(self):
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QWidget {
                font-family: Arial, sans-serif;
            }
        """)
    
    def import_balance(self):
        """استيراد الرصيد الابتدائي"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختر ملف Excel", "", "Excel Files (*.xlsx *.xls)")
        
        if file_path:
            success, message = self.db.import_initial_balance(file_path)
            if success:
                QMessageBox.information(self, "نجح", message)
                self.status_label.setText("تم استيراد الرصيد بنجاح")
            else:
                QMessageBox.warning(self, "خطأ", message)
    
    def daily_request(self):
        """طلب إجازة يومية"""
        dialog = DailyRequestDialog(self.db)
        if dialog.exec_() == QDialog.Accepted:
            self.status_label.setText("تم إضافة طلب إجازة يومية")
    
    def hourly_request(self):
        """طلب إجازة ساعية"""
        dialog = HourlyRequestDialog(self.db)
        if dialog.exec_() == QDialog.Accepted:
            self.status_label.setText("تم إضافة طلب إجازة ساعية")
    
    def add_vacation(self):
        """إدراج إجازة"""
        dialog = AddVacationDialog(self.db)
        if dialog.exec_() == QDialog.Accepted:
            self.status_label.setText("تم إدراج إجازة جديدة")
    
    def generate_reports(self):
        """توليد التقارير"""
        dialog = ReportsDialog(self.db)
        dialog.exec_()
    
    def search_employee(self):
        """البحث عن موظف"""
        dialog = SearchDialog(self.db)
        dialog.exec_()
    
    def edit_requests(self):
        """تعديل الطلبات"""
        dialog = EditRequestsDialog(self.db)
        dialog.exec_()
    
    def delete_requests(self):
        """حذف الطلبات"""
        dialog = DeleteRequestsDialog(self.db)
        dialog.exec_()
    
    def archive_data(self):
        """ترحيل البيانات"""
        reply = QMessageBox.question(
            self, "ترحيل البيانات", 
            "هل أنت متأكد من ترحيل بيانات السنة الحالية؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "معلومات", "سيتم تطبيق هذه الميزة لاحقاً")


class DailyRequestDialog(QDialog):
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.setWindowTitle("طلب إجازة يومية")
        self.setFixedSize(500, 400)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QFormLayout()
        
        self.full_name = QLineEdit()
        self.full_name.setMinimumHeight(35)
        self.full_name.setMaximumHeight(45)
        self.employee_id = QLineEdit()
        self.employee_id.setMinimumHeight(35)
        self.employee_id.setMaximumHeight(45)
        self.job_title = QLineEdit()
        self.job_title.setMinimumHeight(35)
        self.job_title.setMaximumHeight(45)
        self.department = QLineEdit()
        self.department.setMinimumHeight(35)
        self.department.setMaximumHeight(45)
        
        self.vacation_type = QComboBox()
        self.vacation_type.addItems(["سنوية", "مرضية", "طارئة", "أمومة", "أخرى"])
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate())
        self.start_date.setCalendarPopup(True)
        
        self.days_count = QSpinBox()
        self.days_count.setRange(1, 365)
        self.days_count.setValue(1)
        
        layout.addRow("الاسم واللقب:", self.full_name)
        layout.addRow("رقم القيد:", self.employee_id)
        layout.addRow("الوظيفة:", self.job_title)
        layout.addRow("الفوج:", self.department)
        layout.addRow("نوع الإجازة:", self.vacation_type)
        layout.addRow("تاريخ الخروج:", self.start_date)
        layout.addRow("عدد الأيام:", self.days_count)
        
        # أزرار
        button_layout = QHBoxLayout()
        
        save_button = QPushButton("حفظ")
        save_button.clicked.connect(self.save_request)
        button_layout.addWidget(save_button)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)
        
        layout.addRow(button_layout)
        self.setLayout(layout)
    
    def save_request(self):
        success, message = self.db.add_daily_request(
            self.full_name.text(),
            self.employee_id.text(),
            self.job_title.text(),
            self.department.text(),
            self.vacation_type.currentText(),
            self.start_date.date().toString("yyyy-MM-dd"),
            self.days_count.value()
        )
        
        if success:
            QMessageBox.information(self, "نجح", message)
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ", message)


class HourlyRequestDialog(QDialog):
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.setWindowTitle("طلب إجازة ساعية")
        self.setFixedSize(400, 300)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QFormLayout()
        
        self.full_name = QLineEdit()
        self.full_name.setMinimumHeight(35)
        self.full_name.setMaximumHeight(45)
        self.usage_date = QDateEdit()
        self.usage_date.setDate(QDate.currentDate())
        self.usage_date.setCalendarPopup(True)
        
        self.hours_count = QSpinBox()
        self.hours_count.setRange(1, 24)
        self.hours_count.setValue(1)
        
        layout.addRow("الاسم واللقب:", self.full_name)
        layout.addRow("تاريخ الاستفادة:", self.usage_date)
        layout.addRow("عدد الساعات:", self.hours_count)
        
        # عرض المعادل بالأيام
        self.days_equivalent = QLabel("0.125")
        self.hours_count.valueChanged.connect(self.update_days_equivalent)
        layout.addRow("المعادل بالأيام:", self.days_equivalent)
        
        # أزرار
        button_layout = QHBoxLayout()
        
        save_button = QPushButton("حفظ")
        save_button.clicked.connect(self.save_request)
        button_layout.addWidget(save_button)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)
        
        layout.addRow(button_layout)
        self.setLayout(layout)
    
    def update_days_equivalent(self):
        hours = self.hours_count.value()
        days = (hours * 3) / 24
        self.days_equivalent.setText(f"{days:.3f}")
    
    def save_request(self):
        success, message = self.db.add_hourly_request(
            self.full_name.text(),
            self.usage_date.date().toString("yyyy-MM-dd"),
            self.hours_count.value()
        )
        
        if success:
            QMessageBox.information(self, "نجح", message)
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ", message)


class AddVacationDialog(QDialog):
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.setWindowTitle("إدراج إجازة")
        self.setFixedSize(400, 300)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QFormLayout()
        
        self.full_name = QLineEdit()
        self.full_name.setMinimumHeight(35)
        self.full_name.setMaximumHeight(45)
        self.date = QDateEdit()
        self.date.setDate(QDate.currentDate())
        self.date.setCalendarPopup(True)
        
        self.days_count = QSpinBox()
        self.days_count.setRange(1, 365)
        self.days_count.setValue(1)
        
        self.reason = QLineEdit()
        self.reason.setMinimumHeight(35)
        self.reason.setMaximumHeight(45)
        
        layout.addRow("الاسم واللقب:", self.full_name)
        layout.addRow("التاريخ:", self.date)
        layout.addRow("عدد الأيام:", self.days_count)
        layout.addRow("السبب:", self.reason)
        
        # أزرار
        button_layout = QHBoxLayout()
        
        save_button = QPushButton("حفظ")
        save_button.clicked.connect(self.save_vacation)
        button_layout.addWidget(save_button)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)
        
        layout.addRow(button_layout)
        self.setLayout(layout)
    
    def save_vacation(self):
        success, message = self.db.add_vacation(
            self.full_name.text(),
            self.date.date().toString("yyyy-MM-dd"),
            self.days_count.value(),
            self.reason.text()
        )
        
        if success:
            QMessageBox.information(self, "نجح", message)
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ", message)


class ReportsDialog(QDialog):
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.setWindowTitle("التقارير")
        self.setFixedSize(800, 600)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # اختيار الموظف
        employee_layout = QHBoxLayout()
        employee_layout.addWidget(QLabel("اختر الموظف:"))
        
        self.employee_combo = QComboBox()
        self.employee_combo.addItems(self.db.get_all_employees())
        employee_layout.addWidget(self.employee_combo)
        
        generate_button = QPushButton("توليد التقرير")
        generate_button.clicked.connect(self.generate_report)
        employee_layout.addWidget(generate_button)
        
        layout.addLayout(employee_layout)
        
        # منطقة عرض التقرير
        self.report_text = QTextEdit()
        self.report_text.setReadOnly(True)
        self.report_text.setMinimumHeight(250)
        layout.addWidget(self.report_text)
        
        # أزرار
        button_layout = QHBoxLayout()
        
        print_button = QPushButton("طباعة")
        print_button.clicked.connect(self.print_report)
        button_layout.addWidget(print_button)
        
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.accept)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def generate_report(self):
        employee = self.employee_combo.currentText()
        if not employee:
            return
        
        balance = self.db.get_employee_balance(employee)
        
        report = f"""
        تقرير الإجازات للموظف: {employee}
        =====================================
        
        الرصيد الابتدائي: {balance['initial_balance']} يوم
        الإجازات المدرجة: {balance['added_vacations']} يوم
        الإجازات اليومية المستفادة: {balance['daily_used']} يوم
        الإجازات الساعية المستفادة: {balance['hourly_used']:.3f} يوم
        
        الرصيد الصافي النهائي: {balance['net_balance']:.3f} يوم
        
        تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        self.report_text.setPlainText(report)
    
    def print_report(self):
        QMessageBox.information(self, "معلومات", "سيتم تطبيق ميزة الطباعة لاحقاً")


class SearchDialog(QDialog):
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.setWindowTitle("البحث عن موظف")
        self.setFixedSize(600, 400)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # البحث
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("اسم الموظف:"))
        
        self.search_input = QLineEdit()
        self.search_input.setMinimumHeight(35)
        self.search_input.setMaximumHeight(45)
        search_layout.addWidget(self.search_input)
        
        search_button = QPushButton("بحث")
        search_button.clicked.connect(self.search_employee)
        search_layout.addWidget(search_button)
        
        layout.addLayout(search_layout)
        
        # النتائج
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setMinimumHeight(250)
        layout.addWidget(self.results_text)
        
        # إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)
        
        self.setLayout(layout)
    
    def search_employee(self):
        employee = self.search_input.text()
        if not employee:
            return
        
        balance = self.db.get_employee_balance(employee)
        
        if balance['initial_balance'] == 0 and balance['added_vacations'] == 0 and balance['daily_used'] == 0 and balance['hourly_used'] == 0:
            self.results_text.setPlainText("لم يتم العثور على الموظف")
            return
        
        result = f"""
        معلومات الموظف: {employee}
        ========================
        
        الرصيد الابتدائي: {balance['initial_balance']} يوم
        الإجازات المدرجة: {balance['added_vacations']} يوم
        الإجازات اليومية المستفادة: {balance['daily_used']} يوم
        الإجازات الساعية المستفادة: {balance['hourly_used']:.3f} يوم
        
        الرصيد الصافي النهائي: {balance['net_balance']:.3f} يوم
        """
        
        self.results_text.setPlainText(result)


class EditRequestsDialog(QDialog):
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.setWindowTitle("تعديل الطلبات")
        self.setFixedSize(800, 600)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # تبويبات للأنواع المختلفة
        self.tabs = QTabWidget()
        
        # تبويب الطلبات اليومية
        daily_widget = QWidget()
        daily_layout = QVBoxLayout()
        
        self.daily_table = QTableWidget()
        self.load_daily_requests()
        daily_layout.addWidget(self.daily_table)
        
        daily_widget.setLayout(daily_layout)
        self.tabs.addTab(daily_widget, "الطلبات اليومية")
        
        # تبويب الطلبات الساعية
        hourly_widget = QWidget()
        hourly_layout = QVBoxLayout()
        
        self.hourly_table = QTableWidget()
        self.load_hourly_requests()
        hourly_layout.addWidget(self.hourly_table)
        
        hourly_widget.setLayout(hourly_layout)
        self.tabs.addTab(hourly_widget, "الطلبات الساعية")
        
        # تبويب الإجازات المدرجة
        added_widget = QWidget()
        added_layout = QVBoxLayout()
        
        self.added_table = QTableWidget()
        self.load_added_vacations()
        added_layout.addWidget(self.added_table)
        
        added_widget.setLayout(added_layout)
        self.tabs.addTab(added_widget, "الإجازات المدرجة")
        
        layout.addWidget(self.tabs)
        
        # أزرار
        button_layout = QHBoxLayout()
        
        refresh_button = QPushButton("تحديث")
        refresh_button.clicked.connect(self.refresh_tables)
        button_layout.addWidget(refresh_button)
        
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.accept)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def load_daily_requests(self):
        requests = self.db.get_requests_by_type('daily')
        self.daily_table.setRowCount(len(requests))
        self.daily_table.setColumnCount(8)
        self.daily_table.setHorizontalHeaderLabels([
            "ID", "الاسم", "رقم القيد", "الوظيفة", "الفوج", "نوع الإجازة", "تاريخ البداية", "عدد الأيام"
        ])
        
        for row, request in enumerate(requests):
            for col, value in enumerate(request[:8]):
                self.daily_table.setItem(row, col, QTableWidgetItem(str(value)))
    
    def load_hourly_requests(self):
        requests = self.db.get_requests_by_type('hourly')
        self.hourly_table.setRowCount(len(requests))
        self.hourly_table.setColumnCount(5)
        self.hourly_table.setHorizontalHeaderLabels([
            "ID", "الاسم", "تاريخ الاستفادة", "عدد الساعات", "المعادل بالأيام"
        ])
        
        for row, request in enumerate(requests):
            for col, value in enumerate(request[:5]):
                self.hourly_table.setItem(row, col, QTableWidgetItem(str(value)))
    
    def load_added_vacations(self):
        requests = self.db.get_requests_by_type('added')
        self.added_table.setRowCount(len(requests))
        self.added_table.setColumnCount(5)
        self.added_table.setHorizontalHeaderLabels([
            "ID", "الاسم", "التاريخ", "عدد الأيام", "السبب"
        ])
        
        for row, request in enumerate(requests):
            for col, value in enumerate(request[:5]):
                self.added_table.setItem(row, col, QTableWidgetItem(str(value)))
    
    def refresh_tables(self):
        self.load_daily_requests()
        self.load_hourly_requests()
        self.load_added_vacations()


class DeleteRequestsDialog(QDialog):
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.setWindowTitle("حذف الطلبات")
        self.setFixedSize(800, 600)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # تبويبات للأنواع المختلفة
        self.tabs = QTabWidget()
        
        # تبويب الطلبات اليومية
        daily_widget = QWidget()
        daily_layout = QVBoxLayout()
        
        self.daily_table = QTableWidget()
        self.load_daily_requests()
        daily_layout.addWidget(self.daily_table)
        
        daily_delete_button = QPushButton("حذف المحدد")
        daily_delete_button.clicked.connect(lambda: self.delete_selected('daily'))
        daily_layout.addWidget(daily_delete_button)
        
        daily_widget.setLayout(daily_layout)
        self.tabs.addTab(daily_widget, "الطلبات اليومية")
        
        # تبويب الطلبات الساعية
        hourly_widget = QWidget()
        hourly_layout = QVBoxLayout()
        
        self.hourly_table = QTableWidget()
        self.load_hourly_requests()
        hourly_layout.addWidget(self.hourly_table)
        
        hourly_delete_button = QPushButton("حذف المحدد")
        hourly_delete_button.clicked.connect(lambda: self.delete_selected('hourly'))
        hourly_layout.addWidget(hourly_delete_button)
        
        hourly_widget.setLayout(hourly_layout)
        self.tabs.addTab(hourly_widget, "الطلبات الساعية")
        
        # تبويب الإجازات المدرجة
        added_widget = QWidget()
        added_layout = QVBoxLayout()
        
        self.added_table = QTableWidget()
        self.load_added_vacations()
        added_layout.addWidget(self.added_table)
        
        added_delete_button = QPushButton("حذف المحدد")
        added_delete_button.clicked.connect(lambda: self.delete_selected('added'))
        added_layout.addWidget(added_delete_button)
        
        added_widget.setLayout(added_layout)
        self.tabs.addTab(added_widget, "الإجازات المدرجة")
        
        layout.addWidget(self.tabs)
        
        # إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)
        
        self.setLayout(layout)
    
    def load_daily_requests(self):
        requests = self.db.get_requests_by_type('daily')
        self.daily_table.setRowCount(len(requests))
        self.daily_table.setColumnCount(8)
        self.daily_table.setHorizontalHeaderLabels([
            "ID", "الاسم", "رقم القيد", "الوظيفة", "الفوج", "نوع الإجازة", "تاريخ البداية", "عدد الأيام"
        ])
        
        for row, request in enumerate(requests):
            for col, value in enumerate(request[:8]):
                self.daily_table.setItem(row, col, QTableWidgetItem(str(value)))
    
    def load_hourly_requests(self):
        requests = self.db.get_requests_by_type('hourly')
        self.hourly_table.setRowCount(len(requests))
        self.hourly_table.setColumnCount(5)
        self.hourly_table.setHorizontalHeaderLabels([
            "ID", "الاسم", "تاريخ الاستفادة", "عدد الساعات", "المعادل بالأيام"
        ])
        
        for row, request in enumerate(requests):
            for col, value in enumerate(request[:5]):
                self.hourly_table.setItem(row, col, QTableWidgetItem(str(value)))
    
    def load_added_vacations(self):
        requests = self.db.get_requests_by_type('added')
        self.added_table.setRowCount(len(requests))
        self.added_table.setColumnCount(5)
        self.added_table.setHorizontalHeaderLabels([
            "ID", "الاسم", "التاريخ", "عدد الأيام", "السبب"
        ])
        
        for row, request in enumerate(requests):
            for col, value in enumerate(request[:5]):
                self.added_table.setItem(row, col, QTableWidgetItem(str(value)))
    
    def delete_selected(self, request_type):
        if request_type == 'daily':
            table = self.daily_table
        elif request_type == 'hourly':
            table = self.hourly_table
        else:
            table = self.added_table
        
        current_row = table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد صف للحذف")
            return
        
        request_id = table.item(current_row, 0).text()
        
        reply = QMessageBox.question(
            self, "تأكيد الحذف", 
            f"هل أنت متأكد من حذف الطلب رقم {request_id}؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success, message = self.db.delete_request(request_id, request_type)
            if success:
                QMessageBox.information(self, "نجح", message)
                # تحديث الجدول
                if request_type == 'daily':
                    self.load_daily_requests()
                elif request_type == 'hourly':
                    self.load_hourly_requests()
                else:
                    self.load_added_vacations()
            else:
                QMessageBox.warning(self, "خطأ", message)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)  # للنصوص العربية
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة نصية تفاعلية لنظام إدارة الإجازات
Text-based Interactive Interface for Vacation Management System
"""

import sqlite3
import os
from datetime import datetime

class TextInterface:
    def __init__(self):
        self.db_path = 'vacation_system.db'
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والبيانات التجريبية"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول الموظفين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                department TEXT DEFAULT 'عام',
                initial_balance INTEGER DEFAULT 30,
                used_days INTEGER DEFAULT 0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء جدول طلبات الإجازات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS vacation_requests (
                id INTEGER PRIMARY KEY,
                employee_id INTEGER,
                request_date TEXT,
                start_date TEXT,
                end_date TEXT,
                days INTEGER,
                reason TEXT,
                status TEXT DEFAULT 'معلق',
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # إضافة بيانات تجريبية إذا لم تكن موجودة
        cursor.execute('SELECT COUNT(*) FROM employees')
        if cursor.fetchone()[0] == 0:
            employees_data = [
                ('أحمد محمد علي', 'الإدارة', 30, 5),
                ('فاطمة عبد الله', 'الموارد البشرية', 35, 8),
                ('محمد عبد الرحمن', 'المالية', 30, 12),
                ('عائشة سعيد', 'التسويق', 40, 3),
                ('عبد الله أحمد', 'تقنية المعلومات', 30, 15),
                ('سارة أحمد', 'المبيعات', 32, 7),
                ('يوسف محمد', 'الإنتاج', 28, 10),
                ('نور الدين', 'الجودة', 35, 4)
            ]
            
            for name, dept, initial, used in employees_data:
                cursor.execute('''
                    INSERT INTO employees (name, department, initial_balance, used_days)
                    VALUES (?, ?, ?, ?)
                ''', (name, dept, initial, used))
        
        conn.commit()
        conn.close()
    
    def display_header(self):
        """عرض رأس البرنامج"""
        print("\n" + "="*70)
        print("🎯 نظام إدارة الإجازات المتقدم - الواجهة النصية التفاعلية")
        print("="*70)
        print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🗂️ قاعدة البيانات: {self.db_path}")
        print("="*70)
    
    def display_main_menu(self):
        """عرض القائمة الرئيسية"""
        print("\n📋 القائمة الرئيسية:")
        print("-" * 40)
        print("1. 👥 إدارة الموظفين")
        print("2. 📊 عرض الإحصائيات")
        print("3. 📈 التقارير والتحليلات")
        print("4. ➕ إضافة طلب إجازة")
        print("5. 📋 عرض طلبات الإجازات")
        print("6. 🔍 البحث عن موظف")
        print("7. 🔔 التنبيهات والإشعارات")
        print("8. 💾 إدارة البيانات")
        print("9. ℹ️ معلومات النظام")
        print("0. 🚪 خروج")
        print("-" * 40)
    
    def show_employees(self):
        """عرض قائمة الموظفين"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, name, department, initial_balance, used_days 
            FROM employees ORDER BY name
        ''')
        employees = cursor.fetchall()
        conn.close()
        
        print("\n👥 قائمة الموظفين:")
        print("-" * 85)
        print("الرقم".ljust(5) + "الاسم".ljust(20) + "القسم".ljust(18) + "الرصيد".ljust(8) + "المستخدم".ljust(10) + "المتبقي".ljust(8) + "الحالة")
        print("-" * 85)
        
        total_initial = 0
        total_used = 0
        
        for emp in employees:
            emp_id, name, dept, initial, used = emp
            remaining = initial - used
            total_initial += initial
            total_used += used
            
            # تحديد حالة الرصيد
            if remaining < 5:
                status = "🔴 منخفض"
            elif remaining < 10:
                status = "🟡 متوسط"
            else:
                status = "🟢 جيد"
            
            print(f"{emp_id:<5} {name[:18]:<20} {dept[:16]:<18} {initial:<8} {used:<10} {remaining:<8} {status}")
        
        print("-" * 85)
        print(f"الإجمالي:".ljust(43) + f"{total_initial:<8} {total_used:<10} {total_initial - total_used}")
        
        return employees
    
    def show_statistics(self):
        """عرض الإحصائيات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # إحصائيات عامة
        cursor.execute('SELECT COUNT(*), SUM(initial_balance), SUM(used_days) FROM employees')
        total_employees, total_initial, total_used = cursor.fetchone()
        
        remaining = total_initial - total_used
        usage_rate = (total_used / total_initial * 100) if total_initial > 0 else 0
        
        print("\n📊 الإحصائيات العامة:")
        print("-" * 50)
        print(f"👥 إجمالي الموظفين: {total_employees}")
        print(f"📈 إجمالي الرصيد الابتدائي: {total_initial} يوم")
        print(f"📉 إجمالي الأيام المستخدمة: {total_used} يوم")
        print(f"💰 إجمالي الرصيد المتبقي: {remaining} يوم")
        print(f"📊 معدل الاستخدام: {usage_rate:.1f}%")
        
        # إحصائيات حسب الأقسام
        print("\n🏢 إحصائيات حسب الأقسام:")
        print("-" * 60)
        cursor.execute('''
            SELECT department, COUNT(*) as emp_count, 
                   SUM(initial_balance) as total_initial,
                   SUM(used_days) as total_used
            FROM employees 
            GROUP BY department 
            ORDER BY total_used DESC
        ''')
        
        for dept, count, dept_initial, dept_used in cursor.fetchall():
            dept_remaining = dept_initial - dept_used
            dept_usage_rate = (dept_used / dept_initial * 100) if dept_initial > 0 else 0
            print(f"📋 {dept[:15]:<15}: {count} موظف | {dept_used}/{dept_initial} أيام ({dept_usage_rate:.1f}%)")
        
        conn.close()
    
    def add_vacation_request(self):
        """إضافة طلب إجازة"""
        print("\n➕ إضافة طلب إجازة جديد:")
        print("-" * 40)
        
        # عرض الموظفين
        employees = self.show_employees()
        
        try:
            emp_id = int(input("\nأدخل رقم الموظف: "))
            days = int(input("عدد أيام الإجازة: "))
            reason = input("سبب الإجازة (اختياري): ") or "إجازة عادية"
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود الموظف والرصيد
            cursor.execute('SELECT name, initial_balance, used_days FROM employees WHERE id = ?', (emp_id,))
            employee = cursor.fetchone()
            
            if employee:
                name, initial, used = employee
                new_used = used + days
                remaining = initial - new_used
                
                if remaining >= 0:
                    # تحديث رصيد الموظف
                    cursor.execute('UPDATE employees SET used_days = ? WHERE id = ?', (new_used, emp_id))
                    
                    # إضافة طلب الإجازة
                    cursor.execute('''
                        INSERT INTO vacation_requests 
                        (employee_id, request_date, days, reason, status)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (emp_id, datetime.now().strftime('%Y-%m-%d'), days, reason, 'موافق عليه'))
                    
                    conn.commit()
                    print(f"\n✅ تم إضافة طلب الإجازة بنجاح!")
                    print(f"👤 الموظف: {name}")
                    print(f"📅 عدد الأيام: {days}")
                    print(f"📊 الرصيد الجديد: {remaining} يوم متبقي")
                else:
                    print(f"\n❌ الرصيد غير كافي!")
                    print(f"المطلوب: {days} يوم | المتاح: {initial - used} يوم")
            else:
                print("❌ الموظف غير موجود")
            
            conn.close()
            
        except ValueError:
            print("❌ يرجى إدخال أرقام صحيحة")
        except Exception as e:
            print(f"❌ خطأ: {e}")
    
    def search_employee(self):
        """البحث عن موظف"""
        search_term = input("\n🔍 أدخل اسم الموظف أو جزء منه: ").strip()
        
        if not search_term:
            print("❌ يرجى إدخال نص للبحث")
            return
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, name, department, initial_balance, used_days 
            FROM employees 
            WHERE name LIKE ? OR department LIKE ?
            ORDER BY name
        ''', (f'%{search_term}%', f'%{search_term}%'))
        
        results = cursor.fetchall()
        conn.close()
        
        if results:
            print(f"\n✅ تم العثور على {len(results)} نتيجة:")
            print("-" * 70)
            for emp in results:
                emp_id, name, dept, initial, used = emp
                remaining = initial - used
                print(f"👤 {name} (#{emp_id})")
                print(f"   📋 القسم: {dept}")
                print(f"   📊 الرصيد: {initial} ابتدائي | {used} مستخدم | {remaining} متبقي")
                print()
        else:
            print("❌ لم يتم العثور على نتائج")
    
    def show_notifications(self):
        """عرض التنبيهات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        print("\n🔔 التنبيهات والإشعارات:")
        print("-" * 50)
        
        notifications = []
        
        # تنبيهات الرصيد المنخفض
        cursor.execute('''
            SELECT name, initial_balance - used_days as remaining 
            FROM employees 
            WHERE remaining < 10 
            ORDER BY remaining ASC
        ''')
        
        low_balance = cursor.fetchall()
        
        if low_balance:
            notifications.append("⚠️ موظفين برصيد منخفض:")
            for name, remaining in low_balance:
                if remaining < 5:
                    icon = "🔴"
                    level = "حرج"
                else:
                    icon = "🟡"
                    level = "تحذير"
                notifications.append(f"  {icon} {name}: {remaining} يوم ({level})")
        
        # إحصائيات سريعة
        cursor.execute('SELECT COUNT(*), AVG(initial_balance - used_days) FROM employees')
        count, avg_remaining = cursor.fetchone()
        notifications.append(f"\n📊 ملخص سريع:")
        notifications.append(f"  👥 إجمالي الموظفين: {count}")
        notifications.append(f"  📈 متوسط الرصيد المتبقي: {avg_remaining:.1f} يوم")
        
        if notifications:
            for notification in notifications:
                print(notification)
        else:
            print("✅ لا توجد تنبيهات")
        
        conn.close()
    
    def show_system_info(self):
        """عرض معلومات النظام"""
        print("\nℹ️ معلومات النظام:")
        print("-" * 40)
        print(f"📅 التاريخ والوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🗂️ مجلد العمل: {os.getcwd()}")
        print(f"🗄️ قاعدة البيانات: {self.db_path}")
        
        if os.path.exists(self.db_path):
            db_size = os.path.getsize(self.db_path)
            print(f"📊 حجم قاعدة البيانات: {db_size:,} بايت")
        
        print(f"🐍 Python: متاح ويعمل بشكل صحيح")
        
        # فحص المكتبات
        print("\n📦 المكتبات المتاحة:")
        libraries = ['sqlite3', 'os', 'datetime']
        for lib in libraries:
            try:
                __import__(lib)
                print(f"  ✅ {lib}")
            except ImportError:
                print(f"  ❌ {lib}")
    
    def run(self):
        """تشغيل الواجهة التفاعلية"""
        self.display_header()
        
        while True:
            try:
                self.display_main_menu()
                choice = input("اختر العملية المطلوبة (0-9): ").strip()
                
                if choice == '0':
                    print("\n👋 شكراً لاستخدام نظام إدارة الإجازات!")
                    print("💡 لتشغيل الواجهة الرسومية: python master_control_panel.py")
                    break
                elif choice == '1':
                    self.show_employees()
                elif choice == '2':
                    self.show_statistics()
                elif choice == '3':
                    print("\n📈 التقارير والتحليلات:")
                    print("هذه الميزة متاحة في الواجهة الرسومية الكاملة")
                elif choice == '4':
                    self.add_vacation_request()
                elif choice == '5':
                    print("\n📋 عرض طلبات الإجازات:")
                    print("هذه الميزة متاحة في الواجهة الرسومية الكاملة")
                elif choice == '6':
                    self.search_employee()
                elif choice == '7':
                    self.show_notifications()
                elif choice == '8':
                    print("\n💾 إدارة البيانات:")
                    print("هذه الميزة متاحة في الواجهة الرسومية الكاملة")
                elif choice == '9':
                    self.show_system_info()
                else:
                    print("⚠️ اختيار غير صحيح، يرجى المحاولة مرة أخرى")
                
                if choice != '0':
                    input("\nاضغط Enter للمتابعة...")
                
            except KeyboardInterrupt:
                print("\n\n👋 تم إيقاف البرنامج")
                break
            except Exception as e:
                print(f"\n❌ خطأ غير متوقع: {e}")
                input("اضغط Enter للمتابعة...")

if __name__ == "__main__":
    try:
        interface = TextInterface()
        interface.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")

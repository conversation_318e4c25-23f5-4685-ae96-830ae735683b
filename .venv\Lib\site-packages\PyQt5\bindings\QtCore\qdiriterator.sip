// qdiriterator.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDirIterator
{
%TypeHeaderCode
#include <qdiriterator.h>
%End

public:
    enum IteratorFlag
    {
        NoIteratorFlags,
        FollowSymlinks,
        Subdirectories,
    };

    typedef QFlags<QDirIterator::IteratorFlag> IteratorFlags;
    QDirIterator(const QDir &dir, QFlags<QDirIterator::IteratorFlag> flags = NoIteratorFlags);
    QDirIterator(const QString &path, QFlags<QDirIterator::IteratorFlag> flags = NoIteratorFlags);
    QDirIterator(const QString &path, QFlags<QDir::Filter> filters, QFlags<QDirIterator::IteratorFlag> flags = NoIteratorFlags);
    QDirIterator(const QString &path, const QStringList &nameFilters, QFlags<QDir::Filter> filters = QDir::NoFilter, QFlags<QDirIterator::IteratorFlag> flags = NoIteratorFlags);
    ~QDirIterator();
    QString next();
    bool hasNext() const;
    QString fileName() const;
    QString filePath() const;
    QFileInfo fileInfo() const;
    QString path() const;

private:
    QDirIterator(const QDirIterator &);
};

QFlags<QDirIterator::IteratorFlag> operator|(QDirIterator::IteratorFlag f1, QFlags<QDirIterator::IteratorFlag> f2);

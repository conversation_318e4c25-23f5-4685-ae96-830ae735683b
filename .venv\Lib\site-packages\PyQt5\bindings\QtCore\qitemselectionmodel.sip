// qitemselectionmodel.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QItemSelectionRange
{
%TypeHeaderCode
#include <qitemselectionmodel.h>
%End

public:
    QItemSelectionRange();
    QItemSelectionRange(const QItemSelectionRange &other);
    QItemSelectionRange(const QModelIndex &atopLeft, const QModelIndex &abottomRight);
    explicit QItemSelectionRange(const QModelIndex &index);
    int top() const;
    int left() const;
    int bottom() const;
    int right() const;
    int width() const;
    int height() const;
    const QPersistentModelIndex &topLeft() const;
    const QPersistentModelIndex &bottomRight() const;
    QModelIndex parent() const;
    const QAbstractItemModel *model() const;
    bool contains(const QModelIndex &index) const;
    bool contains(int row, int column, const QModelIndex &parentIndex) const;
    bool intersects(const QItemSelectionRange &other) const;
    bool operator==(const QItemSelectionRange &other) const;
    bool operator!=(const QItemSelectionRange &other) const;
    bool isValid() const;
    QModelIndexList indexes() const;
    QItemSelectionRange intersected(const QItemSelectionRange &other) const;
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    bool isEmpty() const;
    bool operator<(const QItemSelectionRange &other) const;
%If (Qt_5_6_0 -)
    void swap(QItemSelectionRange &other /Constrained/);
%End
};

class QItemSelectionModel : public QObject
{
%TypeHeaderCode
#include <qitemselectionmodel.h>
%End

public:
    enum SelectionFlag
    {
        NoUpdate,
        Clear,
        Select,
        Deselect,
        Toggle,
        Current,
        Rows,
        Columns,
        SelectCurrent,
        ToggleCurrent,
        ClearAndSelect,
    };

    typedef QFlags<QItemSelectionModel::SelectionFlag> SelectionFlags;
%If (Qt_5_5_0 -)
    explicit QItemSelectionModel(QAbstractItemModel *model /TransferThis/ = 0);
%End
%If (- Qt_5_5_0)
    explicit QItemSelectionModel(QAbstractItemModel *model /TransferThis/);
%End
    QItemSelectionModel(QAbstractItemModel *model, QObject *parent /TransferThis/);
    virtual ~QItemSelectionModel();
    QModelIndex currentIndex() const;
    bool isSelected(const QModelIndex &index) const;
%If (- Qt_5_15_0)
    bool isRowSelected(int row, const QModelIndex &parent) const;
%End
%If (Qt_5_15_0 -)
    bool isRowSelected(int row, const QModelIndex &parent = QModelIndex()) const;
%End
%If (- Qt_5_15_0)
    bool isColumnSelected(int column, const QModelIndex &parent) const;
%End
%If (Qt_5_15_0 -)
    bool isColumnSelected(int column, const QModelIndex &parent = QModelIndex()) const;
%End
%If (- Qt_5_15_0)
    bool rowIntersectsSelection(int row, const QModelIndex &parent) const;
%End
%If (Qt_5_15_0 -)
    bool rowIntersectsSelection(int row, const QModelIndex &parent = QModelIndex()) const;
%End
%If (- Qt_5_15_0)
    bool columnIntersectsSelection(int column, const QModelIndex &parent) const;
%End
%If (Qt_5_15_0 -)
    bool columnIntersectsSelection(int column, const QModelIndex &parent = QModelIndex()) const;
%End
    QModelIndexList selectedIndexes() const;
    const QItemSelection selection() const;
%If (Qt_5_5_0 -)
    QAbstractItemModel *model();
%End
%If (- Qt_5_5_0)
%If (Qt_5_15_1 -)
    const QAbstractItemModel *model() const;
%End
%End

public slots:
    virtual void clear();
    void clearSelection();
    virtual void reset();
    virtual void select(const QModelIndex &index, QItemSelectionModel::SelectionFlags command);
    virtual void select(const QItemSelection &selection, QItemSelectionModel::SelectionFlags command);
    virtual void setCurrentIndex(const QModelIndex &index, QItemSelectionModel::SelectionFlags command);
    virtual void clearCurrentIndex();

signals:
    void selectionChanged(const QItemSelection &selected, const QItemSelection &deselected);
    void currentChanged(const QModelIndex &current, const QModelIndex &previous);
    void currentRowChanged(const QModelIndex &current, const QModelIndex &previous);
    void currentColumnChanged(const QModelIndex &current, const QModelIndex &previous);

protected:
    void emitSelectionChanged(const QItemSelection &newSelection, const QItemSelection &oldSelection);

public:
    bool hasSelection() const;
    QModelIndexList selectedRows(int column = 0) const;
    QModelIndexList selectedColumns(int row = 0) const;
%If (Qt_5_5_0 -)
    void setModel(QAbstractItemModel *model);
%End

signals:
%If (Qt_5_5_0 -)
    void modelChanged(QAbstractItemModel *model);
%End
};

QFlags<QItemSelectionModel::SelectionFlag> operator|(QItemSelectionModel::SelectionFlag f1, QFlags<QItemSelectionModel::SelectionFlag> f2);

class QItemSelection
{
%TypeHeaderCode
#include <qitemselectionmodel.h>
%End

public:
    QItemSelection();
    QItemSelection(const QModelIndex &topLeft, const QModelIndex &bottomRight);
    void select(const QModelIndex &topLeft, const QModelIndex &bottomRight);
    bool contains(const QModelIndex &index) const;
    int __contains__(const QModelIndex &index);
%MethodCode
        // It looks like you can't assign QBool to int.
        sipRes = bool(sipCpp->contains(*a0));
%End

    QModelIndexList indexes() const;
    void merge(const QItemSelection &other, QItemSelectionModel::SelectionFlags command);
    static void split(const QItemSelectionRange &range, const QItemSelectionRange &other, QItemSelection *result);
    void __setitem__(int i, const QItemSelectionRange &range);
%MethodCode
        int len;
        
        len = sipCpp->count();
        
        if ((a0 = (int)sipConvertFromSequenceIndex(a0, len)) < 0)
            sipIsErr = 1;
        else
            (*sipCpp)[a0] = *a1;
%End

    void __setitem__(SIP_PYSLICE slice, const QItemSelection &list);
%MethodCode
        Py_ssize_t start, stop, step, slicelength;
        
        if (sipConvertFromSliceObject(a0, sipCpp->count(), &start, &stop, &step, &slicelength) < 0)
        {
            sipIsErr = 1;
        }
        else
        {
            int vlen = a1->count();
        
            if (vlen != slicelength)
            {
                sipBadLengthForSlice(vlen, slicelength);
                sipIsErr = 1;
            }
            else
            {
                QItemSelection::const_iterator it = a1->begin();
        
                for (Py_ssize_t i = 0; i < slicelength; ++i)
                {
                    (*sipCpp)[start] = *it;
                    start += step;
                    ++it;
                }
            }
        }
%End

    void __delitem__(int i);
%MethodCode
        if ((a0 = (int)sipConvertFromSequenceIndex(a0, sipCpp->count())) < 0)
            sipIsErr = 1;
        else
            sipCpp->removeAt(a0);
%End

    void __delitem__(SIP_PYSLICE slice);
%MethodCode
        Py_ssize_t start, stop, step, slicelength;
        
        if (sipConvertFromSliceObject(a0, sipCpp->count(), &start, &stop, &step, &slicelength) < 0)
        {
            sipIsErr = 1;
        }
        else
        {
            for (Py_ssize_t i = 0; i < slicelength; ++i)
            {
                sipCpp->removeAt(start);
                start += step - 1;
            }
        }
%End

    QItemSelectionRange operator[](int i);
%MethodCode
        Py_ssize_t idx = sipConvertFromSequenceIndex(a0, sipCpp->count());
        
        if (idx < 0)
            sipIsErr = 1;
        else
            sipRes = new QItemSelectionRange(sipCpp->operator[]((int)idx));
%End

    QItemSelection operator[](SIP_PYSLICE slice);
%MethodCode
        Py_ssize_t start, stop, step, slicelength;
        
        if (sipConvertFromSliceObject(a0, sipCpp->count(), &start, &stop, &step, &slicelength) < 0)
        {
            sipIsErr = 1;
        }
        else
        {
            sipRes = new QItemSelection();
        
            for (Py_ssize_t i = 0; i < slicelength; ++i)
            {
                (*sipRes) += (*sipCpp)[start];
                start += step;
            }
        }
%End

// Methods inherited from QList<QItemSelectionRange>.
bool operator!=(const QItemSelection &other) const;
bool operator==(const QItemSelection &other) const;

// Keep the following in sync with QStringList (except for mid()).
void clear();
bool isEmpty() const;
void append(const QItemSelectionRange &range);
void prepend(const QItemSelectionRange &range);
void insert(int i, const QItemSelectionRange &range);
void replace(int i, const QItemSelectionRange &range);
void removeAt(int i);
int removeAll(const QItemSelectionRange &range);
QItemSelectionRange takeAt(int i);
QItemSelectionRange takeFirst();
QItemSelectionRange takeLast();
void move(int from, int to);
void swap(int i, int j);
int count(const QItemSelectionRange &range) const;
int count() const /__len__/;
QItemSelectionRange &first();
QItemSelectionRange &last();
int indexOf(const QItemSelectionRange &value, int from = 0) const;
int lastIndexOf(const QItemSelectionRange &value, int from = -1) const;
QItemSelection &operator+=(const QItemSelection &other);
QItemSelection &operator+=(const QItemSelectionRange &value);
};

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نوافذ البحث والإعدادات المتقدمة للواجهة الحديثة
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime, timedelta
from modern_dialogs import ModernDialog
import json

class ModernSearchWindow(ModernDialog):
    """نافذة البحث المتقدمة والذكية"""
    
    def __init__(self):
        super().__init__("🔍 البحث المتقدم والذكي", 950, 700)
        self.search_results = []
        self.setup_search_content()
        
    def setup_search_content(self):
        """إعداد محتوى البحث"""
        layout = QVBoxLayout(self.content_area)
        layout.setSpacing(20)
        
        # شريط البحث المتقدم
        self.create_search_bar(layout)
        
        # فلاتر البحث
        self.create_search_filters(layout)
        
        # نتائج البحث
        self.create_results_section(layout)
        
        # إحصائيات البحث
        self.create_statistics_section(layout)
        
        # إعداد الأزرار
        self.add_button("🔍 بحث", self.perform_search, "primary")
        self.add_button("🔄 إعادة تعيين", self.reset_search, "secondary")
        self.add_button("📤 تصدير النتائج", self.export_results, "success")
        self.add_button("❌ إغلاق", self.accept, "secondary")
        
    def create_search_bar(self, layout):
        """إنشاء شريط البحث المتقدم"""
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #3498db, stop: 1 #2980b9
                );
                border-radius: 15px;
                padding: 25px;
            }
        """)
        
        search_layout = QVBoxLayout(search_frame)
        
        # العنوان
        title_label = QLabel("🔍 البحث الذكي في سجلات الإجازات")
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: white;
            background: transparent;
            margin-bottom: 10px;
        """)
        search_layout.addWidget(title_label)
        
        # شريط البحث الرئيسي
        main_search_layout = QHBoxLayout()
        
        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث عن موظف، رقم، قسم، أو أي معلومة...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 16px;
                color: #2c3e50;
            }
            QLineEdit:focus {
                border: 3px solid rgba(255, 255, 255, 0.8);
                background: rgba(255, 255, 255, 0.95);
            }
        """)
        self.search_input.returnPressed.connect(self.perform_search)
        main_search_layout.addWidget(self.search_input)
        
        # زر البحث السريع
        quick_search_btn = QPushButton("🔍 بحث سريع")
        quick_search_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                padding: 15px 25px;
                font-weight: bold;
                font-size: 14px;
                margin-left: 10px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.3);
                border: 2px solid rgba(255, 255, 255, 0.5);
            }
        """)
        quick_search_btn.clicked.connect(self.perform_search)
        main_search_layout.addWidget(quick_search_btn)
        
        search_layout.addLayout(main_search_layout)
        
        # اقتراحات البحث السريع
        suggestions_layout = QHBoxLayout()
        
        suggestions_label = QLabel("🎯 اقتراحات سريعة:")
        suggestions_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            background: transparent;
        """)
        suggestions_layout.addWidget(suggestions_label)
        
        quick_suggestions = [
            "إجازات نوفمبر",
            "رصيد سلبي", 
            "قسم التقنية",
            "طلبات اليوم",
            "ساعية"
        ]
        
        for suggestion in quick_suggestions:
            btn = QPushButton(suggestion)
            btn.setStyleSheet("""
                QPushButton {
                    background: rgba(255, 255, 255, 0.15);
                    color: white;
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 8px;
                    padding: 8px 15px;
                    font-size: 16px;
                    margin-right: 5px;
                }
                QPushButton:hover {
                    background: rgba(255, 255, 255, 0.25);
                }
            """)
            btn.clicked.connect(lambda checked, text=suggestion: self.search_input.setText(text))
            suggestions_layout.addWidget(btn)
            
        suggestions_layout.addStretch()
        search_layout.addLayout(suggestions_layout)
        
        layout.addWidget(search_frame)
        
    def create_search_filters(self, layout):
        """إنشاء فلاتر البحث المتقدمة"""
        filters_frame = QFrame()
        filters_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 20px;
            }
        """)
        
        filters_layout = QVBoxLayout(filters_frame)
        
        # عنوان الفلاتر
        filters_title = QLabel("🎛️ فلاتر البحث المتقدمة")
        filters_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        filters_layout.addWidget(filters_title)
        
        # الفلاتر في شبكة
        filters_grid = QGridLayout()
        filters_grid.setSpacing(15)
        
        # فلتر نوع الإجازة
        self.create_filter_group(filters_grid, 0, 0, "📝 نوع الإجازة:", "vacation_type",
                                ["جميع الأنواع", "إجازة يومية", "إجازة ساعية", "إجازة إضافية"])
        
        # فلتر الحالة
        self.create_filter_group(filters_grid, 0, 1, "📊 حالة الطلب:", "request_status",
                                ["جميع الحالات", "مقبول", "مرفوض", "في الانتظار"])
        
        # فلتر القسم
        self.create_filter_group(filters_grid, 1, 0, "🏢 القسم:", "department",
                                ["جميع الأقسام", "الإدارة", "المالية", "التسويق", "الموارد البشرية", "التقنية"])
        
        # فلتر الفترة الزمنية
        self.create_filter_group(filters_grid, 1, 1, "⏰ الفترة الزمنية:", "time_period",
                                ["جميع الفترات", "هذا الأسبوع", "هذا الشهر", "آخر 3 أشهر", "هذا العام"])
        
        # فلتر حالة الرصيد
        self.create_filter_group(filters_grid, 2, 0, "💰 حالة الرصيد:", "balance_status",
                                ["جميع الأرصدة", "رصيد إيجابي", "رصيد سلبي", "رصيد منتهي"])
        
        # فلتر مقدار الأيام
        self.create_filter_group(filters_grid, 2, 1, "📅 مقدار الأيام:", "days_range",
                                ["جميع المقادير", "يوم واحد", "2-5 أيام", "6-10 أيام", "أكثر من 10 أيام"])
        
        filters_layout.addLayout(filters_grid)
        
        # فلاتر التاريخ المخصصة
        date_filters_layout = QHBoxLayout()
        
        # من تاريخ
        from_date_label = QLabel("📅 من تاريخ:")
        from_date_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        date_filters_layout.addWidget(from_date_label)
        
        self.from_date_edit = QDateEdit(QDate.currentDate().addDays(-30))
        self.from_date_edit.setCalendarPopup(True)
        self.from_date_edit.setStyleSheet("""
            QDateEdit {
                background: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
            }
        """)
        date_filters_layout.addWidget(self.from_date_edit)
        
        # إلى تاريخ
        to_date_label = QLabel("إلى تاريخ:")
        to_date_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        date_filters_layout.addWidget(to_date_label)
        
        self.to_date_edit = QDateEdit(QDate.currentDate())
        self.to_date_edit.setCalendarPopup(True)
        self.to_date_edit.setStyleSheet("""
            QDateEdit {
                background: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
            }
        """)
        date_filters_layout.addWidget(self.to_date_edit)
        
        date_filters_layout.addStretch()
        filters_layout.addLayout(date_filters_layout)
        
        layout.addWidget(filters_frame)
        
    def create_results_section(self, layout):
        """إنشاء قسم نتائج البحث"""
        results_frame = QFrame()
        results_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 20px;
            }
        """)
        
        results_layout = QVBoxLayout(results_frame)
        
        # عنوان النتائج
        results_header = QHBoxLayout()
        
        results_title = QLabel("📋 نتائج البحث")
        results_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
        """)
        results_header.addWidget(results_title)
        
        results_header.addStretch()
        
        # عداد النتائج
        self.results_count_label = QLabel("0 نتيجة")
        self.results_count_label.setStyleSheet("""
            font-size: 14px;
            color: #7f8c8d;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 8px 15px;
        """)
        results_header.addWidget(self.results_count_label)
        
        results_layout.addLayout(results_header)
        
        # جدول النتائج
        self.results_table = QTableWidget()
        self.results_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f1f3f4;
                font-size: 16px;
            }
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #667eea, stop: 1 #764ba2
                );
                color: white;
                border: none;
                padding: 12px;
                font-weight: bold;
                font-size: 16px;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #f1f3f4;
            }
            QTableWidget::item:selected {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e3f2fd, stop: 1 #bbdefb
                );
                color: #1976d2;
            }
            QTableWidget::item:hover {
                background: #f5f5f5;
            }
        """)
        
        # إعداد أعمدة الجدول
        headers = ["الاسم", "الرقم", "القسم", "نوع الإجازة", "التاريخ", "الأيام", "الحالة", "الرصيد"]
        self.results_table.setColumnCount(len(headers))
        self.results_table.setHorizontalHeaderLabels(headers)
        
        # بيانات وهمية للاختبار
        self.load_sample_data()
        
        results_layout.addWidget(self.results_table)
        
        layout.addWidget(results_frame)
        
    def create_statistics_section(self, layout):
        """إنشاء قسم إحصائيات البحث"""
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #f093fb, stop: 1 #f5576c
                );
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        stats_layout = QHBoxLayout(stats_frame)
        
        # بطاقات الإحصائيات
        stats_data = [
            ("📊", "إجمالي النتائج", "0", "white"),
            ("✅", "طلبات مقبولة", "0", "white"),
            ("⏳", "في الانتظار", "0", "white"),
            ("💰", "متوسط الأيام", "0", "white")
        ]
        
        for icon, title, value, color in stats_data:
            card = self.create_stat_card(icon, title, value, color)
            stats_layout.addWidget(card)
            
        layout.addWidget(stats_frame)
        
    def create_filter_group(self, layout, row, col, label_text, object_name, items):
        """إنشاء مجموعة فلترة"""
        # التسمية
        label = QLabel(label_text)
        label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        layout.addWidget(label, row * 2, col)
        
        # القائمة المنسدلة
        combo = QComboBox()
        combo.setObjectName(object_name)
        combo.addItems(items)
        combo.setStyleSheet("""
            QComboBox {
                background: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                min-width: 200px;
            }
            QComboBox:focus {
                border: 2px solid #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iIzJjM2U1MCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
            }
        """)
        layout.addWidget(combo, row * 2 + 1, col)
        
        setattr(self, object_name, combo)
        
    def create_stat_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: rgba(255, 255, 255, 0.15);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
                padding: 15px;
                min-width: 120px;
            }}
            QFrame:hover {{
                background: rgba(255, 255, 255, 0.25);
            }}
        """)
        
        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(8)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"""
            font-size: 28px;
            color: {color};
            background: transparent;
        """)
        card_layout.addWidget(icon_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"""
            font-size: 18px;
            font-weight: bold;
            color: {color};
            background: transparent;
        """)
        card_layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
            background: transparent;
        """)
        card_layout.addWidget(title_label)
        
        return card
        
    def load_sample_data(self):
        """تحميل بيانات وهمية للاختبار"""
        sample_data = [
            ["أحمد محمد علي", "1001", "التقنية", "إجازة يومية", "2024-11-15", "3", "مقبول", "15"],
            ["فاطمة عبد الله", "1002", "المالية", "إجازة ساعية", "2024-11-14", "0.5", "مقبول", "20"],
            ["محمد عبد الرحمن", "1003", "التسويق", "إجازة يومية", "2024-11-13", "5", "في الانتظار", "5"],
            ["عائشة سعيد", "1004", "الموارد البشرية", "إجازة إضافية", "2024-11-12", "2", "مقبول", "25"],
            ["عبد الله أحمد", "1005", "الإدارة", "إجازة يومية", "2024-11-11", "1", "مرفوض", "10"],
            ["مريم خالد", "1006", "التقنية", "إجازة ساعية", "2024-11-10", "1", "مقبول", "18"],
            ["يوسف محمود", "1007", "المالية", "إجازة يومية", "2024-11-09", "7", "مقبول", "8"],
            ["زينب عبد الرحيم", "1008", "التسويق", "إجازة يومية", "2024-11-08", "2", "مقبول", "22"],
        ]
        
        self.results_table.setRowCount(len(sample_data))
        
        for row, row_data in enumerate(sample_data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                
                # تلوين الحالة
                if col == 6:  # عمود الحالة
                    if value == "مقبول":
                        item.setBackground(QColor(212, 237, 218))
                        item.setForeground(QColor(21, 87, 36))
                    elif value == "مرفوض":
                        item.setBackground(QColor(248, 215, 218))
                        item.setForeground(QColor(132, 32, 41))
                    elif value == "في الانتظار":
                        item.setBackground(QColor(255, 243, 205))
                        item.setForeground(QColor(102, 77, 3))
                        
                # تلوين الرصيد
                elif col == 7:  # عمود الرصيد
                    balance = int(value)
                    if balance > 15:
                        item.setBackground(QColor(212, 237, 218))
                    elif balance > 5:
                        item.setBackground(QColor(255, 243, 205))
                    else:
                        item.setBackground(QColor(248, 215, 218))
                        
                self.results_table.setItem(row, col, item)
                
        # تعديل حجم الأعمدة
        self.results_table.resizeColumnsToContents()
        
        # تحديث عداد النتائج
        self.results_count_label.setText(f"{len(sample_data)} نتيجة")
        
    def perform_search(self):
        """تنفيذ عملية البحث"""
        search_term = self.search_input.text().strip()
        
        if not search_term:
            QMessageBox.information(self, "معلومات", "يرجى إدخال كلمة البحث")
            return
            
        # هنا سيتم إضافة منطق البحث الفعلي
        self.show_search_progress()
        
        # محاكاة البحث
        QTimer.singleShot(2000, self.complete_search)
        
    def show_search_progress(self):
        """عرض تقدم البحث"""
        progress = QProgressDialog("جاري البحث...", "إلغاء", 0, 100, self)
        progress.setWindowModality(Qt.WindowModal)
        progress.setAutoClose(True)
        progress.setAutoReset(True)
        
        for i in range(101):
            progress.setValue(i)
            QApplication.processEvents()
            if progress.wasCanceled():
                break
                
        progress.close()
        
    def complete_search(self):
        """إكمال عملية البحث"""
        QMessageBox.information(self, "اكتمل البحث", "تم العثور على النتائج بنجاح!")
        
    def reset_search(self):
        """إعادة تعيين البحث"""
        self.search_input.clear()
        
        # إعادة تعيين جميع الفلاتر
        self.vacation_type.setCurrentIndex(0)
        self.request_status.setCurrentIndex(0)
        self.department.setCurrentIndex(0)
        self.time_period.setCurrentIndex(0)
        self.balance_status.setCurrentIndex(0)
        self.days_range.setCurrentIndex(0)
        
        # إعادة تعيين التواريخ
        self.from_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.to_date_edit.setDate(QDate.currentDate())
        
        QMessageBox.information(self, "تم", "تم إعادة تعيين البحث")
        
    def export_results(self):
        """تصدير نتائج البحث"""
        if self.results_table.rowCount() == 0:
            QMessageBox.warning(self, "تحذير", "لا توجد نتائج للتصدير")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ النتائج", f"نتائج_البحث_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            "Excel Files (*.xlsx);;CSV Files (*.csv)"
        )
        
        if file_path:
            # هنا سيتم إضافة كود التصدير الفعلي
            QMessageBox.information(self, "تم", f"تم تصدير النتائج إلى:\n{file_path}")

class ModernSettingsWindow(ModernDialog):
    """نافذة الإعدادات الشاملة والمتقدمة"""
    
    def __init__(self):
        super().__init__("⚙️ الإعدادات الشاملة", 800, 600)
        self.settings = self.load_settings()
        self.setup_settings_content()
        
    def setup_settings_content(self):
        """إعداد محتوى الإعدادات"""
        layout = QVBoxLayout(self.content_area)
        layout.setSpacing(20)
        
        # تبويبات الإعدادات
        self.create_settings_tabs(layout)
        
        # إعداد الأزرار
        self.add_button("💾 حفظ الإعدادات", self.save_settings, "primary")
        self.add_button("🔄 إعادة تعيين", self.reset_settings, "warning")
        self.add_button("📤 تصدير الإعدادات", self.export_settings, "secondary")
        self.add_button("❌ إلغاء", self.reject, "secondary")
        
    def create_settings_tabs(self, layout):
        """إنشاء تبويبات الإعدادات"""
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                background: white;
            }
            QTabBar::tab {
                background: #f8f9fa;
                border: 2px solid #e0e0e0;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
                color: #2c3e50;
                min-width: 100px;
            }
            QTabBar::tab:selected {
                background: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background: #d5dbdb;
            }
        """)
        
        # تبويب الإعدادات العامة
        self.create_general_tab()
        
        # تبويب إعدادات الإجازات
        self.create_vacation_tab()
        
        # تبويب إعدادات الواجهة
        self.create_interface_tab()
        
        # تبويب إعدادات النظام
        self.create_system_tab()
        
        # تبويب إعدادات الإشعارات
        self.create_notifications_tab()
        
        layout.addWidget(self.tabs)
        
    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        general_widget = QWidget()
        general_layout = QVBoxLayout(general_widget)
        general_layout.setSpacing(20)
        
        # معلومات الشركة
        company_frame = self.create_settings_frame("🏢 معلومات الشركة")
        company_layout = QVBoxLayout(company_frame)
        
        self.company_name = self.create_input_field("اسم الشركة:", "شركة المثال للتقنية")
        self.company_address = self.create_input_field("العنوان:", "المملكة العربية السعودية")
        self.company_phone = self.create_input_field("الهاتف:", "+966 11 123 4567")
        self.company_email = self.create_input_field("البريد الإلكتروني:", "<EMAIL>")
        
        company_layout.addWidget(self.company_name)
        company_layout.addWidget(self.company_address) 
        company_layout.addWidget(self.company_phone)
        company_layout.addWidget(self.company_email)
        
        general_layout.addWidget(company_frame)
        
        # إعدادات التاريخ واللغة
        date_lang_frame = self.create_settings_frame("📅 التاريخ واللغة")
        date_lang_layout = QVBoxLayout(date_lang_frame)
        
        # اللغة
        lang_layout = QHBoxLayout()
        lang_label = QLabel("🌐 اللغة:")
        lang_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        lang_layout.addWidget(lang_label)
        
        self.language_combo = QComboBox()
        self.language_combo.addItems(["العربية", "English", "Français"])
        self.language_combo.setStyleSheet(self.get_combo_style())
        lang_layout.addWidget(self.language_combo)
        lang_layout.addStretch()
        
        date_lang_layout.addLayout(lang_layout)
        
        # تنسيق التاريخ
        date_format_layout = QHBoxLayout()
        date_format_label = QLabel("📆 تنسيق التاريخ:")
        date_format_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        date_format_layout.addWidget(date_format_label)
        
        self.date_format_combo = QComboBox()
        self.date_format_combo.addItems(["dd/MM/yyyy", "MM/dd/yyyy", "yyyy-MM-dd"])
        self.date_format_combo.setStyleSheet(self.get_combo_style())
        date_format_layout.addWidget(self.date_format_combo)
        date_format_layout.addStretch()
        
        date_lang_layout.addLayout(date_format_layout)
        
        general_layout.addWidget(date_lang_frame)
        general_layout.addStretch()
        
        self.tabs.addTab(general_widget, "🔧 عام")
        
    def create_vacation_tab(self):
        """إنشاء تبويب إعدادات الإجازات"""
        vacation_widget = QWidget()
        vacation_layout = QVBoxLayout(vacation_widget)
        vacation_layout.setSpacing(20)
        
        # إعدادات الرصيد
        balance_frame = self.create_settings_frame("💰 إعدادات الرصيد")
        balance_layout = QVBoxLayout(balance_frame)
        
        # الرصيد الافتراضي
        default_balance_layout = QHBoxLayout()
        default_balance_label = QLabel("📊 الرصيد الافتراضي:")
        default_balance_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        default_balance_layout.addWidget(default_balance_label)
        
        self.default_balance_spin = QSpinBox()
        self.default_balance_spin.setRange(15, 50)
        self.default_balance_spin.setValue(30)
        self.default_balance_spin.setSuffix(" يوم")
        self.default_balance_spin.setStyleSheet(self.get_input_style())
        default_balance_layout.addWidget(self.default_balance_spin)
        default_balance_layout.addStretch()
        
        balance_layout.addLayout(default_balance_layout)
        
        # السماح بالرصيد السلبي
        negative_balance_layout = QHBoxLayout()
        self.allow_negative_balance = QCheckBox("السماح بالرصيد السلبي")
        self.allow_negative_balance.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #2c3e50;
                font-size: 14px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
            }
        """)
        negative_balance_layout.addWidget(self.allow_negative_balance)
        negative_balance_layout.addStretch()
        
        balance_layout.addLayout(negative_balance_layout)
        
        vacation_layout.addWidget(balance_frame)
        
        # قواعد الإجازات
        rules_frame = self.create_settings_frame("📋 قواعد الإجازات")
        rules_layout = QVBoxLayout(rules_frame)
        
        # الحد الأقصى للإجازة المتصلة
        max_consecutive_layout = QHBoxLayout()
        max_consecutive_label = QLabel("⏱️ الحد الأقصى للإجازة المتصلة:")
        max_consecutive_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        max_consecutive_layout.addWidget(max_consecutive_label)
        
        self.max_consecutive_spin = QSpinBox()
        self.max_consecutive_spin.setRange(1, 30)
        self.max_consecutive_spin.setValue(15)
        self.max_consecutive_spin.setSuffix(" يوم")
        self.max_consecutive_spin.setStyleSheet(self.get_input_style())
        max_consecutive_layout.addWidget(self.max_consecutive_spin)
        max_consecutive_layout.addStretch()
        
        rules_layout.addLayout(max_consecutive_layout)
        
        # المدة المسبقة للطلب
        advance_notice_layout = QHBoxLayout()
        advance_notice_label = QLabel("📅 المدة المسبقة للطلب:")
        advance_notice_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        advance_notice_layout.addWidget(advance_notice_label)
        
        self.advance_notice_spin = QSpinBox()
        self.advance_notice_spin.setRange(1, 30)
        self.advance_notice_spin.setValue(3)
        self.advance_notice_spin.setSuffix(" يوم")
        self.advance_notice_spin.setStyleSheet(self.get_input_style())
        advance_notice_layout.addWidget(self.advance_notice_spin)
        advance_notice_layout.addStretch()
        
        rules_layout.addLayout(advance_notice_layout)
        
        vacation_layout.addWidget(rules_frame)
        vacation_layout.addStretch()
        
        self.tabs.addTab(vacation_widget, "🏖️ الإجازات")
        
    def create_interface_tab(self):
        """إنشاء تبويب إعدادات الواجهة"""
        interface_widget = QWidget()
        interface_layout = QVBoxLayout(interface_widget)
        interface_layout.setSpacing(20)
        
        # إعدادات المظهر
        appearance_frame = self.create_settings_frame("🎨 إعدادات المظهر")
        appearance_layout = QVBoxLayout(appearance_frame)
        
        # السمة
        theme_layout = QHBoxLayout()
        theme_label = QLabel("🌈 السمة:")
        theme_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        theme_layout.addWidget(theme_label)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["الافتراضي", "داكن", "فاتح", "أزرق", "أخضر"])
        self.theme_combo.setStyleSheet(self.get_combo_style())
        theme_layout.addWidget(self.theme_combo)
        theme_layout.addStretch()
        
        appearance_layout.addLayout(theme_layout)
        
        # حجم الخط
        font_size_layout = QHBoxLayout()
        font_size_label = QLabel("📝 حجم الخط:")
        font_size_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        font_size_layout.addWidget(font_size_label)
        
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(12)
        self.font_size_spin.setSuffix(" نقطة")
        self.font_size_spin.setStyleSheet(self.get_input_style())
        font_size_layout.addWidget(self.font_size_spin)
        font_size_layout.addStretch()
        
        appearance_layout.addLayout(font_size_layout)
        
        interface_layout.addWidget(appearance_frame)
        
        # إعدادات النوافذ
        windows_frame = self.create_settings_frame("🪟 إعدادات النوافذ")
        windows_layout = QVBoxLayout(windows_frame)
        
        # تذكر موقع النوافذ
        remember_position_layout = QHBoxLayout()
        self.remember_window_position = QCheckBox("تذكر موقع وحجم النوافذ")
        self.remember_window_position.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #2c3e50;
                font-size: 14px;
            }
        """)
        remember_position_layout.addWidget(self.remember_window_position)
        remember_position_layout.addStretch()
        
        windows_layout.addLayout(remember_position_layout)
        
        # تأكيد الإغلاق
        confirm_exit_layout = QHBoxLayout()
        self.confirm_exit = QCheckBox("تأكيد الإغلاق")
        self.confirm_exit.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #2c3e50;
                font-size: 14px;
            }
        """)
        confirm_exit_layout.addWidget(self.confirm_exit)
        confirm_exit_layout.addStretch()
        
        windows_layout.addLayout(confirm_exit_layout)
        
        interface_layout.addWidget(windows_frame)
        interface_layout.addStretch()
        
        self.tabs.addTab(interface_widget, "🎨 الواجهة")
        
    def create_system_tab(self):
        """إنشاء تبويب إعدادات النظام"""
        system_widget = QWidget()
        system_layout = QVBoxLayout(system_widget)
        system_layout.setSpacing(20)
        
        # إعدادات قاعدة البيانات
        db_frame = self.create_settings_frame("🗄️ قاعدة البيانات")
        db_layout = QVBoxLayout(db_frame)
        
        # النسخ الاحتياطي التلقائي
        auto_backup_layout = QHBoxLayout()
        self.auto_backup = QCheckBox("النسخ الاحتياطي التلقائي")
        self.auto_backup.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #2c3e50;
                font-size: 14px;
            }
        """)
        auto_backup_layout.addWidget(self.auto_backup)
        auto_backup_layout.addStretch()
        
        db_layout.addLayout(auto_backup_layout)
        
        # تكرار النسخ الاحتياطي
        backup_frequency_layout = QHBoxLayout()
        backup_frequency_label = QLabel("🔄 تكرار النسخ الاحتياطي:")
        backup_frequency_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        backup_frequency_layout.addWidget(backup_frequency_label)
        
        self.backup_frequency_combo = QComboBox()
        self.backup_frequency_combo.addItems(["يومي", "أسبوعي", "شهري"])
        self.backup_frequency_combo.setStyleSheet(self.get_combo_style())
        backup_frequency_layout.addWidget(self.backup_frequency_combo)
        backup_frequency_layout.addStretch()
        
        db_layout.addLayout(backup_frequency_layout)
        
        system_layout.addWidget(db_frame)
        
        # إعدادات الأمان
        security_frame = self.create_settings_frame("🔒 إعدادات الأمان")
        security_layout = QVBoxLayout(security_frame)
        
        # مهلة تسجيل الدخول
        session_timeout_layout = QHBoxLayout()
        session_timeout_label = QLabel("⏱️ مهلة تسجيل الدخول:")
        session_timeout_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        session_timeout_layout.addWidget(session_timeout_label)
        
        self.session_timeout_spin = QSpinBox()
        self.session_timeout_spin.setRange(15, 480)
        self.session_timeout_spin.setValue(60)
        self.session_timeout_spin.setSuffix(" دقيقة")
        self.session_timeout_spin.setStyleSheet(self.get_input_style())
        session_timeout_layout.addWidget(self.session_timeout_spin)
        session_timeout_layout.addStretch()
        
        security_layout.addLayout(session_timeout_layout)
        
        # تسجيل العمليات
        log_operations_layout = QHBoxLayout()
        self.log_operations = QCheckBox("تسجيل جميع العمليات")
        self.log_operations.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #2c3e50;
                font-size: 14px;
            }
        """)
        log_operations_layout.addWidget(self.log_operations)
        log_operations_layout.addStretch()
        
        security_layout.addLayout(log_operations_layout)
        
        system_layout.addWidget(security_frame)
        system_layout.addStretch()
        
        self.tabs.addTab(system_widget, "🔧 النظام")
        
    def create_notifications_tab(self):
        """إنشاء تبويب إعدادات الإشعارات"""
        notifications_widget = QWidget()
        notifications_layout = QVBoxLayout(notifications_widget)
        notifications_layout.setSpacing(20)
        
        # إعدادات الإشعارات
        notifications_frame = self.create_settings_frame("🔔 إعدادات الإشعارات")
        notifications_frame_layout = QVBoxLayout(notifications_frame)
        
        # تفعيل الإشعارات
        enable_notifications_layout = QHBoxLayout()
        self.enable_notifications = QCheckBox("تفعيل الإشعارات")
        self.enable_notifications.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #2c3e50;
                font-size: 14px;
            }
        """)
        enable_notifications_layout.addWidget(self.enable_notifications)
        enable_notifications_layout.addStretch()
        
        notifications_frame_layout.addLayout(enable_notifications_layout)
        
        # إشعارات الطلبات الجديدة
        new_requests_layout = QHBoxLayout()
        self.notify_new_requests = QCheckBox("إشعار عند وصول طلب جديد")
        self.notify_new_requests.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #2c3e50;
                font-size: 14px;
            }
        """)
        new_requests_layout.addWidget(self.notify_new_requests)
        new_requests_layout.addStretch()
        
        notifications_frame_layout.addLayout(new_requests_layout)
        
        # إشعارات الرصيد المنخفض
        low_balance_layout = QHBoxLayout()
        self.notify_low_balance = QCheckBox("إشعار عند انخفاض الرصيد")
        self.notify_low_balance.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #2c3e50;
                font-size: 14px;
            }
        """)
        low_balance_layout.addWidget(self.notify_low_balance)
        low_balance_layout.addStretch()
        
        notifications_frame_layout.addLayout(low_balance_layout)
        
        notifications_layout.addWidget(notifications_frame)
        
        # إعدادات البريد الإلكتروني
        email_frame = self.create_settings_frame("📧 إعدادات البريد الإلكتروني")
        email_layout = QVBoxLayout(email_frame)
        
        self.email_server = self.create_input_field("خادم البريد:", "smtp.gmail.com")
        self.email_port = self.create_input_field("المنفذ:", "587")
        self.email_username = self.create_input_field("اسم المستخدم:", "")
        self.email_password = self.create_input_field("كلمة المرور:", "", password=True)
        
        email_layout.addWidget(self.email_server)
        email_layout.addWidget(self.email_port)
        email_layout.addWidget(self.email_username)
        email_layout.addWidget(self.email_password)
        
        # زر اختبار البريد
        test_email_layout = QHBoxLayout()
        test_email_btn = QPushButton("📧 اختبار الإعدادات")
        test_email_btn.setStyleSheet("""
            QPushButton {
                background: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2980b9;
            }
        """)
        test_email_btn.clicked.connect(self.test_email_settings)
        test_email_layout.addWidget(test_email_btn)
        test_email_layout.addStretch()
        
        email_layout.addLayout(test_email_layout)
        
        notifications_layout.addWidget(email_frame)
        notifications_layout.addStretch()
        
        self.tabs.addTab(notifications_widget, "🔔 الإشعارات")
        
    def create_settings_frame(self, title):
        """إنشاء إطار إعدادات"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 20px;
            }
        """)
        
        # إضافة العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        
        return frame
        
    def create_input_field(self, label_text, placeholder, password=False):
        """إنشاء حقل إدخال"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        
        label = QLabel(label_text)
        label.setStyleSheet("font-weight: bold; color: #2c3e50; min-width: 150px;")
        layout.addWidget(label)
        
        if password:
            edit = QLineEdit()
            edit.setEchoMode(QLineEdit.Password)
        else:
            edit = QLineEdit()
            
        edit.setPlaceholderText(placeholder)
        edit.setStyleSheet(self.get_input_style())
        layout.addWidget(edit)
        
        return container
        
    def get_input_style(self):
        """الحصول على نمط حقول الإدخال"""
        return """
            QLineEdit, QSpinBox {
                background: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
            }
            QLineEdit:focus, QSpinBox:focus {
                border: 2px solid #3498db;
            }
        """
        
    def get_combo_style(self):
        """الحصول على نمط القوائم المنسدلة"""
        return """
            QComboBox {
                background: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                min-width: 150px;
            }
            QComboBox:focus {
                border: 2px solid #3498db;
            }
        """
        
    def load_settings(self):
        """تحميل الإعدادات"""
        # هنا سيتم تحميل الإعدادات من ملف
        default_settings = {
            "company_name": "شركة المثال للتقنية",
            "company_address": "المملكة العربية السعودية",
            "company_phone": "+966 11 123 4567",
            "company_email": "<EMAIL>",
            "language": "العربية",
            "date_format": "dd/MM/yyyy",
            "default_balance": 30,
            "allow_negative_balance": False,
            "max_consecutive_days": 15,
            "advance_notice_days": 3,
            "theme": "الافتراضي",
            "font_size": 12,
            "remember_window_position": True,
            "confirm_exit": True,
            "auto_backup": True,
            "backup_frequency": "أسبوعي",
            "session_timeout": 60,
            "log_operations": True,
            "enable_notifications": True,
            "notify_new_requests": True,
            "notify_low_balance": True,
            "email_server": "smtp.gmail.com",
            "email_port": "587",
            "email_username": "",
            "email_password": ""
        }
        
        return default_settings
        
    def save_settings(self):
        """حفظ الإعدادات"""
        # هنا سيتم حفظ الإعدادات في ملف
        QMessageBox.information(self, "تم", "تم حفظ الإعدادات بنجاح!")
        self.accept()
        
    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        msg = QMessageBox.question(
            self,
            "إعادة تعيين",
            "هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if msg == QMessageBox.Yes:
            self.settings = self.load_settings()
            QMessageBox.information(self, "تم", "تم إعادة تعيين الإعدادات!")
            
    def export_settings(self):
        """تصدير الإعدادات"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ الإعدادات", 
            f"settings_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON Files (*.json)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.settings, f, ensure_ascii=False, indent=2)
                QMessageBox.information(self, "تم", f"تم تصدير الإعدادات إلى:\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"لا يمكن حفظ الملف:\n{str(e)}")
                
    def test_email_settings(self):
        """اختبار إعدادات البريد الإلكتروني"""
        QMessageBox.information(self, "اختبار", "سيتم اختبار إعدادات البريد الإلكتروني...")

def test_search_settings():
    """اختبار نوافذ البحث والإعدادات"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # اختبار نافذة البحث
    search_window = ModernSearchWindow()
    search_window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    test_search_settings()
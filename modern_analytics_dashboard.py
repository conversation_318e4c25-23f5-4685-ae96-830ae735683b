#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
لوحة الإحصائيات المتقدمة ولوحة التحكم التنفيذية
"""

import sys
import os
import sqlite3
import json
from datetime import datetime, timedelta, date
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from modern_dialogs import ModernDialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.dates as mdates
import numpy as np

# تعيين الخط العربي لـ matplotlib
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']

class AnalyticsEngine:
    """محرك الإحصائيات والتحليلات"""
    
    def __init__(self, db_path='vacation_system.db'):
        self.db_path = db_path
        
    def create_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            return conn
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return None
            
    def get_monthly_statistics(self, months=12):
        """الحصول على إحصائيات شهرية"""
        # بيانات وهمية للاختبار
        current_date = datetime.now()
        monthly_data = []
        
        for i in range(months):
            month_date = current_date - timedelta(days=30*i)
            month_name = month_date.strftime('%Y-%m')
            
            # بيانات وهمية
            data = {
                'month': month_name,
                'total_requests': np.random.randint(15, 45),
                'approved_requests': np.random.randint(10, 35),
                'rejected_requests': np.random.randint(1, 8),
                'total_days': np.random.randint(50, 150),
                'employees_on_vacation': np.random.randint(3, 12)
            }
            monthly_data.append(data)
            
        return list(reversed(monthly_data))
        
    def get_department_statistics(self):
        """الحصول على إحصائيات الأقسام"""
        # بيانات وهمية للاختبار
        departments = [
            {'name': 'التقنية', 'employees': 25, 'requests': 18, 'avg_balance': 22.5},
            {'name': 'المالية', 'employees': 15, 'requests': 12, 'avg_balance': 28.3},
            {'name': 'التسويق', 'employees': 20, 'requests': 15, 'avg_balance': 19.8},
            {'name': 'الموارد البشرية', 'employees': 8, 'requests': 6, 'avg_balance': 30.0},
            {'name': 'الإدارة', 'employees': 12, 'requests': 8, 'avg_balance': 25.5},
        ]
        
        return departments
        
    def get_vacation_types_distribution(self):
        """الحصول على توزيع أنواع الإجازات"""
        # بيانات وهمية للاختبار
        return {
            'يومية': 65,
            'ساعية': 20,
            'إضافية': 10,
            'طوارئ': 5
        }
        
    def get_peak_vacation_periods(self):
        """الحصول على فترات ذروة الإجازات"""
        # بيانات وهمية للاختبار
        peak_periods = []
        
        # إجازات الصيف
        peak_periods.append({
            'period': 'يونيو - أغسطس',
            'requests': 85,
            'percentage': 35,
            'reason': 'إجازات الصيف'
        })
        
        # نهاية العام
        peak_periods.append({
            'period': 'ديسمبر',
            'requests': 45,
            'percentage': 18,
            'reason': 'إجازات نهاية العام'
        })
        
        # عيد الفطر
        peak_periods.append({
            'period': 'شوال',
            'requests': 55,
            'percentage': 22,
            'reason': 'عيد الفطر'
        })
        
        return peak_periods
        
    def get_employee_balance_distribution(self):
        """الحصول على توزيع أرصدة الموظفين"""
        # بيانات وهمية للاختبار
        return {
            'ممتاز (25+ يوم)': 35,
            'جيد (15-24 يوم)': 40,
            'متوسط (5-14 يوم)': 20,
            'منخفض (1-4 يوم)': 15,
            'منتهي (0 يوم)': 5
        }
        
    def get_approval_trends(self):
        """الحصول على اتجاهات الموافقة"""
        # بيانات وهمية للاختبار
        current_date = datetime.now()
        trends = []
        
        for i in range(30):
            day_date = current_date - timedelta(days=i)
            day_name = day_date.strftime('%Y-%m-%d')
            
            data = {
                'date': day_name,
                'approval_rate': np.random.uniform(0.7, 0.95),
                'response_time': np.random.uniform(0.5, 3.0)  # بالأيام
            }
            trends.append(data)
            
        return list(reversed(trends))

class ModernAnalyticsWindow(ModernDialog):
    """نافذة الإحصائيات المتقدمة"""
    
    def __init__(self):
        super().__init__("📊 الإحصائيات المتقدمة والتحليلات", 1200, 800)
        self.analytics_engine = AnalyticsEngine()
        self.setup_analytics_content()
        
    def setup_analytics_content(self):
        """إعداد محتوى الإحصائيات"""
        layout = QVBoxLayout(self.content_area)
        layout.setSpacing(15)
        
        # شريط التحكم
        self.create_control_bar(layout)
        
        # تبويبات الإحصائيات
        self.create_analytics_tabs(layout)
        
        # إعداد الأزرار
        self.add_button("🔄 تحديث البيانات", self.refresh_data, "primary")
        self.add_button("📤 تصدير التقرير", self.export_report, "success")
        self.add_button("📧 إرسال التقرير", self.email_report, "secondary")
        self.add_button("❌ إغلاق", self.accept, "secondary")
        
    def create_control_bar(self, layout):
        """إنشاء شريط التحكم"""
        control_frame = QFrame()
        control_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #667eea, stop: 1 #764ba2
                );
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        control_layout = QHBoxLayout(control_frame)
        
        # العنوان
        title_label = QLabel("📊 لوحة الإحصائيات المتقدمة")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: white;
        """)
        control_layout.addWidget(title_label)
        
        control_layout.addStretch()
        
        # اختيار الفترة
        period_label = QLabel("الفترة:")
        period_label.setStyleSheet("color: white; font-weight: bold;")
        control_layout.addWidget(period_label)
        
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "آخر 30 يوم", "آخر 3 أشهر", "آخر 6 أشهر", 
            "آخر سنة", "السنة الحالية", "مخصص"
        ])
        self.period_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: none;
                border-radius: 6px;
                padding: 8px;
                font-weight: bold;
                min-width: 120px;
            }
        """)
        self.period_combo.currentTextChanged.connect(self.on_period_changed)
        control_layout.addWidget(self.period_combo)
        
        # زر التحديث السريع
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px 15px;
                font-weight: bold;
                margin-left: 10px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.3);
            }
        """)
        refresh_btn.clicked.connect(self.refresh_data)
        control_layout.addWidget(refresh_btn)
        
        layout.addWidget(control_frame)
        
    def create_analytics_tabs(self, layout):
        """إنشاء تبويبات الإحصائيات"""
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                background: white;
            }
            QTabBar::tab {
                background: #f8f9fa;
                border: 2px solid #e0e0e0;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
                color: #2c3e50;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background: #667eea;
                color: white;
            }
        """)
        
        # تبويب الرسوم البيانية
        self.create_charts_tab()
        
        # تبويب إحصائيات الأقسام
        self.create_departments_tab()
        
        # تبويب التوزيعات
        self.create_distributions_tab()
        
        # تبويب الاتجاهات
        self.create_trends_tab()
        
        # تبويب التقرير التنفيذي
        self.create_executive_summary_tab()
        
        layout.addWidget(self.tabs)
        
    def create_charts_tab(self):
        """إنشاء تبويب الرسوم البيانية"""
        charts_widget = QWidget()
        charts_layout = QVBoxLayout(charts_widget)
        charts_layout.setSpacing(15)
        
        # مقاييس سريعة
        self.create_quick_metrics(charts_layout)
        
        # الرسوم البيانية
        charts_container = QHBoxLayout()
        
        # رسم بياني شهري
        monthly_chart = self.create_monthly_chart()
        charts_container.addWidget(monthly_chart)
        
        # رسم بياني دائري لأنواع الإجازات
        pie_chart = self.create_pie_chart()
        charts_container.addWidget(pie_chart)
        
        charts_layout.addLayout(charts_container)
        
        self.tabs.addTab(charts_widget, "📈 الرسوم البيانية")
        
    def create_quick_metrics(self, layout):
        """إنشاء المقاييس السريعة"""
        metrics_frame = QFrame()
        metrics_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        metrics_layout = QHBoxLayout(metrics_frame)
        
        # مقاييس مختلفة
        metrics_data = [
            ("📝", "إجمالي الطلبات", "245", "#3498db"),
            ("✅", "معدل الموافقة", "87%", "#27ae60"),
            ("⏱️", "متوسط وقت الرد", "1.2 يوم", "#f39c12"),
            ("👥", "الموظفين النشطين", "156", "#e74c3c"),
            ("📅", "متوسط أيام الإجازة", "8.5", "#9b59b6"),
            ("🎯", "الكفاءة", "92%", "#1abc9c")
        ]
        
        for icon, title, value, color in metrics_data:
            metric_card = self.create_metric_card(icon, title, value, color)
            metrics_layout.addWidget(metric_card)
            
        layout.addWidget(metrics_frame)
        
    def create_metric_card(self, icon, title, value, color):
        """إنشاء بطاقة مقياس"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: {color};
                border-radius: 8px;
                padding: 15px;
                min-width: 140px;
                min-height: 80px;
            }}
        """)
        
        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(5)
        
        # الأيقونة والقيمة في نفس السطر
        top_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 20px; color: white;")
        top_layout.addWidget(icon_label)
        
        top_layout.addStretch()
        
        value_label = QLabel(value)
        value_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: white;
        """)
        top_layout.addWidget(value_label)
        
        card_layout.addLayout(top_layout)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 12px;
            color: rgba(255, 255, 255, 0.9);
            margin-top: 5px;
        """)
        card_layout.addWidget(title_label)
        
        return card
        
    def create_monthly_chart(self):
        """إنشاء الرسم البياني الشهري"""
        chart_frame = QFrame()
        chart_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        chart_layout = QVBoxLayout(chart_frame)
        
        # العنوان
        chart_title = QLabel("📊 الطلبات الشهرية")
        chart_title.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        """)
        chart_layout.addWidget(chart_title)
        
        # الرسم البياني
        figure = Figure(figsize=(8, 4), dpi=80)
        canvas = FigureCanvas(figure)
        chart_layout.addWidget(canvas)
        
        # البيانات
        monthly_data = self.analytics_engine.get_monthly_statistics(6)
        months = [data['month'] for data in monthly_data]
        total_requests = [data['total_requests'] for data in monthly_data]
        approved_requests = [data['approved_requests'] for data in monthly_data]
        
        # رسم البيانات
        ax = figure.add_subplot(111)
        ax.plot(months, total_requests, marker='o', linewidth=2, label='إجمالي الطلبات', color='#3498db')
        ax.plot(months, approved_requests, marker='s', linewidth=2, label='طلبات مقبولة', color='#27ae60')
        
        ax.set_title('الطلبات الشهرية', fontsize=12, fontweight='bold')
        ax.set_xlabel('الشهر')
        ax.set_ylabel('عدد الطلبات')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # تحسين المظهر
        figure.tight_layout()
        
        return chart_frame
        
    def create_pie_chart(self):
        """إنشاء الرسم البياني الدائري"""
        chart_frame = QFrame()
        chart_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        chart_layout = QVBoxLayout(chart_frame)
        
        # العنوان
        chart_title = QLabel("🥧 توزيع أنواع الإجازات")
        chart_title.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        """)
        chart_layout.addWidget(chart_title)
        
        # الرسم البياني
        figure = Figure(figsize=(6, 4), dpi=80)
        canvas = FigureCanvas(figure)
        chart_layout.addWidget(canvas)
        
        # البيانات
        vacation_types = self.analytics_engine.get_vacation_types_distribution()
        labels = list(vacation_types.keys())
        sizes = list(vacation_types.values())
        colors = ['#3498db', '#27ae60', '#f39c12', '#e74c3c']
        
        # رسم البيانات
        ax = figure.add_subplot(111)
        wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        
        ax.set_title('توزيع أنواع الإجازات', fontsize=12, fontweight='bold')
        
        # تحسين المظهر
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
            
        figure.tight_layout()
        
        return chart_frame
        
    def create_departments_tab(self):
        """إنشاء تبويب إحصائيات الأقسام"""
        departments_widget = QWidget()
        departments_layout = QVBoxLayout(departments_widget)
        departments_layout.setSpacing(15)
        
        # عنوان
        dept_title = QLabel("🏢 إحصائيات الأقسام التفصيلية")
        dept_title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        departments_layout.addWidget(dept_title)
        
        # جدول الأقسام
        self.departments_table = QTableWidget()
        self.departments_table.setColumnCount(5)
        self.departments_table.setHorizontalHeaderLabels([
            "القسم", "عدد الموظفين", "عدد الطلبات", "متوسط الرصيد", "معدل الاستخدام"
        ])
        self.departments_table.setStyleSheet("""
            QTableWidget {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f1f3f4;
                font-size: 14px;
            }
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #667eea, stop: 1 #764ba2
                );
                color: white;
                border: none;
                padding: 12px;
                font-weight: bold;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #f1f3f4;
            }
        """)
        
        # تحميل بيانات الأقسام
        self.load_departments_data()
        
        departments_layout.addWidget(self.departments_table)
        
        self.tabs.addTab(departments_widget, "🏢 الأقسام")
        
    def load_departments_data(self):
        """تحميل بيانات الأقسام"""
        departments = self.analytics_engine.get_department_statistics()
        
        self.departments_table.setRowCount(len(departments))
        
        for row, dept in enumerate(departments):
            # اسم القسم
            name_item = QTableWidgetItem(dept['name'])
            name_item.setBackground(QColor(240, 248, 255))
            self.departments_table.setItem(row, 0, name_item)
            
            # عدد الموظفين
            employees_item = QTableWidgetItem(str(dept['employees']))
            self.departments_table.setItem(row, 1, employees_item)
            
            # عدد الطلبات
            requests_item = QTableWidgetItem(str(dept['requests']))
            self.departments_table.setItem(row, 2, requests_item)
            
            # متوسط الرصيد
            balance_item = QTableWidgetItem(f"{dept['avg_balance']:.1f}")
            if dept['avg_balance'] > 25:
                balance_item.setBackground(QColor(212, 237, 218))
            elif dept['avg_balance'] > 15:
                balance_item.setBackground(QColor(255, 243, 205))
            else:
                balance_item.setBackground(QColor(248, 215, 218))
            self.departments_table.setItem(row, 3, balance_item)
            
            # معدل الاستخدام
            usage_rate = (dept['requests'] / dept['employees']) * 100
            usage_item = QTableWidgetItem(f"{usage_rate:.1f}%")
            self.departments_table.setItem(row, 4, usage_item)
            
        self.departments_table.resizeColumnsToContents()
        
    def create_distributions_tab(self):
        """إنشاء تبويب التوزيعات"""
        distributions_widget = QWidget()
        distributions_layout = QVBoxLayout(distributions_widget)
        distributions_layout.setSpacing(15)
        
        # توزيع أرصدة الموظفين
        balance_frame = QFrame()
        balance_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        balance_layout = QVBoxLayout(balance_frame)
        
        balance_title = QLabel("💰 توزيع أرصدة الموظفين")
        balance_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        balance_layout.addWidget(balance_title)
        
        # بطاقات التوزيع
        balance_cards_layout = QHBoxLayout()
        
        balance_distribution = self.analytics_engine.get_employee_balance_distribution()
        colors = ['#27ae60', '#f39c12', '#3498db', '#e74c3c', '#7f8c8d']
        
        for i, (category, count) in enumerate(balance_distribution.items()):
            card = self.create_distribution_card(category, count, colors[i % len(colors)])
            balance_cards_layout.addWidget(card)
            
        balance_layout.addLayout(balance_cards_layout)
        distributions_layout.addWidget(balance_frame)
        
        # فترات الذروة
        peak_frame = QFrame()
        peak_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        peak_layout = QVBoxLayout(peak_frame)
        
        peak_title = QLabel("📈 فترات ذروة الإجازات")
        peak_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        peak_layout.addWidget(peak_title)
        
        # قائمة فترات الذروة
        peak_periods = self.analytics_engine.get_peak_vacation_periods()
        
        for period in peak_periods:
            period_card = self.create_peak_period_card(period)
            peak_layout.addWidget(period_card)
            
        distributions_layout.addWidget(peak_frame)
        
        self.tabs.addTab(distributions_widget, "📊 التوزيعات")
        
    def create_distribution_card(self, category, count, color):
        """إنشاء بطاقة توزيع"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: {color};
                border-radius: 8px;
                padding: 15px;
                min-width: 130px;
            }}
        """)
        
        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(5)
        
        # العدد
        count_label = QLabel(str(count))
        count_label.setAlignment(Qt.AlignCenter)
        count_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: white;
        """)
        card_layout.addWidget(count_label)
        
        # الفئة
        category_label = QLabel(category)
        category_label.setAlignment(Qt.AlignCenter)
        category_label.setStyleSheet("""
            font-size: 11px;
            color: rgba(255, 255, 255, 0.9);
        """)
        category_label.setWordWrap(True)
        card_layout.addWidget(category_label)
        
        return card
        
    def create_peak_period_card(self, period_data):
        """إنشاء بطاقة فترة ذروة"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #f093fb, stop: 1 #f5576c
                );
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 10px;
            }
        """)
        
        card_layout = QHBoxLayout(card)
        
        # معلومات الفترة
        info_layout = QVBoxLayout()
        
        period_label = QLabel(period_data['period'])
        period_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
        """)
        info_layout.addWidget(period_label)
        
        reason_label = QLabel(period_data['reason'])
        reason_label.setStyleSheet("""
            font-size: 12px;
            color: rgba(255, 255, 255, 0.9);
        """)
        info_layout.addWidget(reason_label)
        
        card_layout.addLayout(info_layout)
        
        card_layout.addStretch()
        
        # الإحصائيات
        stats_layout = QVBoxLayout()
        
        requests_label = QLabel(f"{period_data['requests']} طلب")
        requests_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: white;
            text-align: right;
        """)
        requests_label.setAlignment(Qt.AlignRight)
        stats_layout.addWidget(requests_label)
        
        percentage_label = QLabel(f"{period_data['percentage']}%")
        percentage_label.setStyleSheet("""
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            text-align: right;
        """)
        percentage_label.setAlignment(Qt.AlignRight)
        stats_layout.addWidget(percentage_label)
        
        card_layout.addLayout(stats_layout)
        
        return card
        
    def create_trends_tab(self):
        """إنشاء تبويب الاتجاهات"""
        trends_widget = QWidget()
        trends_layout = QVBoxLayout(trends_widget)
        
        # رسم بياني للاتجاهات
        trends_chart = self.create_trends_chart()
        trends_layout.addWidget(trends_chart)
        
        self.tabs.addTab(trends_widget, "📈 الاتجاهات")
        
    def create_trends_chart(self):
        """إنشاء رسم بياني للاتجاهات"""
        chart_frame = QFrame()
        chart_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        chart_layout = QVBoxLayout(chart_frame)
        
        # العنوان
        chart_title = QLabel("📈 اتجاهات معدل الموافقة ووقت الاستجابة")
        chart_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        chart_layout.addWidget(chart_title)
        
        # الرسم البياني
        figure = Figure(figsize=(12, 6), dpi=80)
        canvas = FigureCanvas(figure)
        chart_layout.addWidget(canvas)
        
        # البيانات
        trends_data = self.analytics_engine.get_approval_trends()
        dates = [datetime.strptime(data['date'], '%Y-%m-%d') for data in trends_data[-15:]]  # آخر 15 يوم
        approval_rates = [data['approval_rate'] * 100 for data in trends_data[-15:]]
        response_times = [data['response_time'] for data in trends_data[-15:]]
        
        # رسم البيانات
        ax1 = figure.add_subplot(111)
        
        # معدل الموافقة
        line1 = ax1.plot(dates, approval_rates, color='#27ae60', marker='o', linewidth=2, label='معدل الموافقة (%)')
        ax1.set_xlabel('التاريخ')
        ax1.set_ylabel('معدل الموافقة (%)', color='#27ae60')
        ax1.tick_params(axis='y', labelcolor='#27ae60')
        ax1.grid(True, alpha=0.3)
        
        # محور ثانوي لوقت الاستجابة
        ax2 = ax1.twinx()
        line2 = ax2.plot(dates, response_times, color='#e74c3c', marker='s', linewidth=2, label='وقت الاستجابة (يوم)')
        ax2.set_ylabel('وقت الاستجابة (يوم)', color='#e74c3c')
        ax2.tick_params(axis='y', labelcolor='#e74c3c')
        
        # تنسيق التواريخ
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax1.xaxis.set_major_locator(mdates.DayLocator(interval=2))
        figure.autofmt_xdate()
        
        # الأسطورة
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
        
        ax1.set_title('اتجاهات الأداء اليومية', fontsize=14, fontweight='bold')
        
        figure.tight_layout()
        
        return chart_frame
        
    def create_executive_summary_tab(self):
        """إنشاء تبويب التقرير التنفيذي"""
        summary_widget = QWidget()
        summary_layout = QVBoxLayout(summary_widget)
        summary_layout.setSpacing(20)
        
        # عنوان التقرير
        summary_title = QLabel("📋 التقرير التنفيذي الشامل")
        summary_title.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        """)
        summary_title.setAlignment(Qt.AlignCenter)
        summary_layout.addWidget(summary_title)
        
        # معلومات التقرير
        report_info = QLabel(f"""
        📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        📊 فترة التقرير: {self.period_combo.currentText()}
        👤 تم إنشاؤه بواسطة: مدير النظام
        """)
        report_info.setStyleSheet("""
            font-size: 12px;
            color: #7f8c8d;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        """)
        summary_layout.addWidget(report_info)
        
        # النقاط الرئيسية
        key_points = self.create_executive_summary_content()
        summary_layout.addWidget(key_points)
        
        self.tabs.addTab(summary_widget, "📋 التقرير التنفيذي")
        
    def create_executive_summary_content(self):
        """إنشاء محتوى التقرير التنفيذي"""
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 25px;
            }
        """)
        
        content_layout = QVBoxLayout(content_frame)
        
        # النقاط الرئيسية
        summary_text = QLabel("""
        <h3 style="color: #2c3e50;">🎯 النقاط الرئيسية:</h3>
        
        <p><strong>📈 الأداء العام:</strong></p>
        <ul>
        <li>معدل الموافقة على الطلبات: <span style="color: #27ae60; font-weight: bold;">87%</span></li>
        <li>متوسط وقت الاستجابة: <span style="color: #f39c12; font-weight: bold;">1.2 يوم</span></li>
        <li>رضا الموظفين: <span style="color: #3498db; font-weight: bold;">92%</span></li>
        </ul>
        
        <p><strong>📊 الإحصائيات:</strong></p>
        <ul>
        <li>إجمالي الطلبات هذا الشهر: <strong>245 طلب</strong></li>
        <li>أكثر الأقسام نشاطاً: <strong>قسم التقنية</strong> (18 طلب)</li>
        <li>أكثر أنواع الإجازات طلباً: <strong>الإجازات اليومية</strong> (65%)</li>
        </ul>
        
        <p><strong>⚠️ التحديات والفرص:</strong></p>
        <ul>
        <li>15 موظف لديهم رصيد منخفض (أقل من 5 أيام)</li>
        <li>فترة ذروة متوقعة في ديسمبر (إجازات نهاية العام)</li>
        <li>فرصة تحسين وقت الاستجابة في قسم المالية</li>
        </ul>
        
        <p><strong>🎯 التوصيات:</strong></p>
        <ul>
        <li>مراجعة أرصدة الموظفين ذوي الرصيد المنخفض</li>
        <li>التخطيط المبكر لفترات الذروة</li>
        <li>تدريب مديري الأقسام على إدارة الطلبات</li>
        <li>تحسين عملية الموافقة الإلكترونية</li>
        </ul>
        """)
        
        summary_text.setStyleSheet("""
            QLabel {
                font-size: 14px;
                line-height: 1.6;
                color: #2c3e50;
            }
        """)
        summary_text.setWordWrap(True)
        content_layout.addWidget(summary_text)
        
        return content_frame
        
    def on_period_changed(self):
        """عند تغيير الفترة"""
        # هنا يمكن تحديث البيانات حسب الفترة المختارة
        pass
        
    def refresh_data(self):
        """تحديث البيانات"""
        # إعادة تحميل البيانات
        self.load_departments_data()
        
        # رسالة تأكيد
        QMessageBox.information(self, "تم التحديث", "تم تحديث جميع البيانات بنجاح!")
        
    def export_report(self):
        """تصدير التقرير"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ التقرير", 
            f"analytics_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
            "PDF Files (*.pdf);;Excel Files (*.xlsx)"
        )
        
        if file_path:
            # هنا سيتم إضافة كود التصدير الفعلي
            QMessageBox.information(self, "تم التصدير", f"تم تصدير التقرير إلى:\n{file_path}")
            
    def email_report(self):
        """إرسال التقرير بالبريد الإلكتروني"""
        email, ok = QInputDialog.getText(self, "إرسال التقرير", "أدخل عنوان البريد الإلكتروني:")
        
        if ok and email:
            # هنا سيتم إضافة كود الإرسال الفعلي
            QMessageBox.information(self, "تم الإرسال", f"تم إرسال التقرير إلى:\n{email}")

def test_analytics_dashboard():
    """اختبار لوحة الإحصائيات"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # اختبار نافذة الإحصائيات
    analytics_window = ModernAnalyticsWindow()
    analytics_window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    test_analytics_dashboard()
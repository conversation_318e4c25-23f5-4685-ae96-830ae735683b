#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 تشغيل النظام بنقرة واحدة!
RUN.py - أسهل طريقة لتشغيل النظام
"""

import sys
import os
import subprocess

def run_system():
    """تشغيل النظام مباشرة"""
    
    print("🚀 تشغيل نظام إدارة الإجازات...")
    print("⚡ جاري البحث عن أفضل واجهة متاحة...")
    print()
    
    # قائمة الأنظمة بالترتيب التفضيلي
    systems = [
        ("🚀 المشغل الموحد الذكي", "تشغيل_النظام_المتكامل.py"),
        ("✨ الواجهة الحديثة", "تشغيل_الواجهة_الحديثة.py"),
        ("🔹 الواجهة التقليدية", "run_app.py"),
        ("🎛️ نظام الاختيار الذكي", "اختيار_الواجهة.py"),
        ("🏠 الواجهة الرئيسية", "modern_main_window.py")
    ]
    
    for name, filename in systems:
        if os.path.exists(filename):
            try:
                print(f"✅ تم العثور على {name}")
                print(f"🎯 تشغيل {filename}...")
                print("🎉 النظام يعمل الآن!")
                print()
                print("🔐 معلومات تسجيل الدخول:")
                print("   👤 المستخدم: admin")
                print("   🔑 كلمة المرور: admin123")
                print()
                
                # تشغيل النظام
                subprocess.run([sys.executable, filename])
                
                print("✅ تم إغلاق النظام بنجاح")
                return True
                
            except Exception as e:
                print(f"⚠️ خطأ في تشغيل {name}: {e}")
                continue
    
    print("❌ لم يتم العثور على أي نظام قابل للتشغيل!")
    print("💡 تأكد من وجود الملفات التالية:")
    for name, filename in systems:
        status = "✅ موجود" if os.path.exists(filename) else "❌ غير موجود"
        print(f"   {filename}: {status}")
    
    return False

def main():
    """الدالة الرئيسية"""
    
    print("=" * 60)
    print("🏢 نظام إدارة الإجازات المتكامل")
    print("🚀 تشغيل مباشر وسريع")
    print("=" * 60)
    print()
    
    # تشغيل النظام
    success = run_system()
    
    if not success:
        print()
        print("🔧 حلول مقترحة:")
        print("1. تأكد من وجود جميع الملفات المطلوبة")
        print("2. تحقق من تثبيت PyQt5:")
        print("   pip install PyQt5")
        print("3. جرب تشغيل الملفات منفردة:")
        print("   python run_app.py")
        print("4. راجع ملف README.md للمزيد من التعليمات")
    
    print()
    print("=" * 60)
    print("🎊 شكراً لاستخدام النظام!")
    print("=" * 60)

if __name__ == "__main__":
    main()
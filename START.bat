@echo off
chcp 65001 > nul
title Advanced Vacation Management System

echo.
echo 🎯 Advanced Vacation Management System
echo =====================================
echo.
echo ✨ Features:
echo    📊 Advanced Reports
echo    🔔 Smart Notifications  
echo    📈 Analytics Dashboard
echo    💾 Automatic Backup
echo    👥 User Management
echo.
echo =====================================
echo.

REM Check Python
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found
    echo 💡 Please install Python 3.7+ from: https://python.org
    pause
    exit /b 1
)

echo ✅ Python found
echo.

REM Check main files
if not exist "master_control_panel.py" (
    echo ❌ Main control panel file not found
    echo 💡 Make sure all system files are present
    pause
    exit /b 1
)

echo ✅ System files found
echo.

REM Run the system
echo 🚀 Starting Advanced Vacation Management System...
echo.
python master_control_panel.py

echo.
echo 👋 Thank you for using Advanced Vacation Management System!
pause

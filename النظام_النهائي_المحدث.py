#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
النظام النهائي المحدث مع إدارة الموظفين وأنواع الإجازات والتوجيه العربي
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

def main():
    """الدالة الرئيسية للنظام النهائي المحدث"""
    app = QApplication(sys.argv)
    
    # تطبيق الخط العربي والتوجيه
    font = QFont("Sakkal Majalla", 10)
    app.setFont(font)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("🚀 تشغيل النظام النهائي المحدث...")
    print("=" * 60)
    print("🆕 الميزات الجديدة:")
    print("   👥 إدارة الموظفين مع قوائم منسدلة")
    print("   📋 إدارة أنواع الإجازات")
    print("   🔄 التوجيه العربي الكامل")
    print("   💾 حفظ تلقائي للبيانات")
    print("=" * 60)
    
    try:
        # تحميل المديرين
        from إدارة_الموظفين import get_employee_names
        from إدارة_أنواع_الإجازات import get_vacation_type_names
        
        employee_count = len(get_employee_names())
        vacation_types_count = len(get_vacation_type_names())
        
        print(f"👥 عدد الموظفين المحملين: {employee_count}")
        print(f"📋 عدد أنواع الإجازات: {vacation_types_count}")
        
    except Exception as e:
        print(f"⚠️ تحذير: {e}")
    
    try:
        # استيراد النظام الشامل المحسّن
        from النظام_الشامل_المحسّن import main as run_enhanced_system
        
        print("🔄 تحميل النظام الشامل المحسّن...")
        return run_enhanced_system()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من وجود ملف النظام الشامل المحسّن")
        
        # تجربة النظام البديل
        try:
            from تشغيل_محسّن_المقاسات import main as run_backup_system
            print("🔄 تشغيل النظام البديل...")
            return run_backup_system()
        except ImportError:
            print("❌ فشل في تحميل النظام البديل")
            
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
    
    finally:
        print("\n📞 للمساعدة:")
        print("   🔧 تأكد من وجود جميع الملفات")
        print("   📁 تحقق من مسار المجلد")
        print("   👥 استخدم إدارة الموظفين")
        print("   📋 استخدم إدارة أنواع الإجازات")
        input("\n⏳ اضغط Enter للإغلاق...")

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n⚠️ تم إنهاء البرنامج")
        sys.exit(0)
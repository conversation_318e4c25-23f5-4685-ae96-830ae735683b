#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لنظام إدارة الإجازات
Quick Start for Vacation Management System
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_dependencies():
    """التحقق من المكتبات المطلوبة"""
    required_modules = {
        'PyQt5': 'PyQt5',
        'pandas': 'pandas', 
        'openpyxl': 'openpyxl',
        'sqlite3': 'sqlite3'
    }
    
    missing_modules = []
    
    for module_name, import_name in required_modules.items():
        try:
            __import__(import_name)
            print(f"✅ {module_name}")
        except ImportError:
            print(f"❌ {module_name} غير مثبت")
            missing_modules.append(module_name)
    
    return missing_modules

def install_dependencies(missing_modules):
    """تثبيت المكتبات المفقودة"""
    if not missing_modules:
        return True
        
    print(f"\n🔧 تثبيت المكتبات المفقودة: {', '.join(missing_modules)}")
    
    try:
        # قراءة requirements.txt إذا كان موجوداً
        if os.path.exists('requirements.txt'):
            print("📦 تثبيت من requirements.txt...")
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ تم تثبيت جميع المكتبات بنجاح")
                return True
            else:
                print(f"❌ خطأ في التثبيت: {result.stderr}")
        
        # تثبيت المكتبات واحدة تلو الأخرى
        for module in missing_modules:
            if module == 'sqlite3':
                continue  # sqlite3 مدمج في Python
            
            print(f"تثبيت {module}...")
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', module], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ تم تثبيت {module}")
            else:
                print(f"❌ فشل تثبيت {module}: {result.stderr}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تثبيت المكتبات: {e}")
        return False

def create_sample_data():
    """إنشاء البيانات النموذجية إذا لم تكن موجودة"""
    excel_file = 'نموذج_الرصيد_الابتدائي.xlsx'
    
    if not os.path.exists(excel_file):
        print("📊 إنشاء ملف البيانات النموذجية...")
        try:
            subprocess.run([sys.executable, 'create_sample_excel.py'], 
                         capture_output=True, text=True, check=True)
            print("✅ تم إنشاء ملف البيانات النموذجية")
        except subprocess.CalledProcessError as e:
            print(f"⚠️ لم يتم إنشاء البيانات النموذجية: {e}")
    else:
        print("✅ ملف البيانات النموذجية موجود")

def run_application():
    """تشغيل التطبيق"""
    print("\n🚀 تشغيل نظام إدارة الإجازات...")
    
    # البحث عن ملف التشغيل الرئيسي
    main_files = ['main.py', 'START.py', 'RUN.py', 'run_app.py']
    
    for main_file in main_files:
        if os.path.exists(main_file):
            print(f"📱 تشغيل {main_file}...")
            try:
                subprocess.run([sys.executable, main_file])
                return True
            except Exception as e:
                print(f"❌ خطأ في تشغيل {main_file}: {e}")
                continue
    
    print("❌ لم يتم العثور على ملف التشغيل الرئيسي")
    return False

def main():
    """الدالة الرئيسية"""
    print("🎯 نظام إدارة الإجازات - التشغيل السريع")
    print("=" * 50)
    
    # التحقق من إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    print("\n🔍 التحقق من المكتبات المطلوبة:")
    missing_modules = check_dependencies()
    
    # تثبيت المكتبات المفقودة
    if missing_modules:
        print(f"\n⚠️ المكتبات المفقودة: {', '.join(missing_modules)}")
        response = input("هل تريد تثبيت المكتبات المفقودة؟ (y/n): ").lower()
        
        if response in ['y', 'yes', 'نعم', 'ن']:
            if not install_dependencies(missing_modules):
                print("❌ فشل في تثبيت المكتبات")
                input("اضغط Enter للخروج...")
                return
        else:
            print("⚠️ لا يمكن تشغيل التطبيق بدون المكتبات المطلوبة")
            input("اضغط Enter للخروج...")
            return
    
    # إنشاء البيانات النموذجية
    create_sample_data()
    
    # تشغيل التطبيق
    if not run_application():
        print("\n❌ فشل في تشغيل التطبيق")
        print("💡 تأكد من وجود ملفات التطبيق في نفس المجلد")
    
    print("\n✨ شكراً لاستخدام نظام إدارة الإجازات!")
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")

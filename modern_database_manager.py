#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير قواعد البيانات المتقدم والأدوات الإضافية
"""

import sys
import os
import sqlite3
import pandas as pd
import json
from datetime import datetime, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from modern_dialogs import ModernDialog

class DatabaseManager(QObject):
    """مدير قاعدة البيانات المتقدم"""
    
    operation_completed = pyqtSignal(bool, str)
    progress_updated = pyqtSignal(int, str)
    
    def __init__(self, db_path='vacation_system.db'):
        super().__init__()
        self.db_path = db_path
        
    def create_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            return conn
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return None
            
    def get_database_info(self):
        """الحصول على معلومات قاعدة البيانات"""
        try:
            conn = self.create_connection()
            if not conn:
                return None
                
            cursor = conn.cursor()
            
            # معلومات الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            info = {
                'file_path': self.db_path,
                'file_size': os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
                'tables_count': len(tables),
                'tables': [],
                'last_modified': datetime.fromtimestamp(os.path.getmtime(self.db_path)).strftime('%Y-%m-%d %H:%M:%S') if os.path.exists(self.db_path) else 'غير متاح'
            }
            
            # تفاصيل كل جدول
            for table in tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                row_count = cursor.fetchone()[0]
                
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                info['tables'].append({
                    'name': table_name,
                    'rows': row_count,
                    'columns': len(columns),
                    'structure': columns
                })
                
            conn.close()
            return info
            
        except Exception as e:
            print(f"خطأ في الحصول على معلومات قاعدة البيانات: {e}")
            return None
            
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            conn = self.create_connection()
            if not conn:
                return False, "لا يمكن الاتصال بقاعدة البيانات"
                
            cursor = conn.cursor()
            
            # تحليل الجداول
            self.progress_updated.emit(25, "تحليل الجداول...")
            cursor.execute("ANALYZE")
            
            # إعادة فهرسة
            self.progress_updated.emit(50, "إعادة الفهرسة...")
            cursor.execute("REINDEX")
            
            # تنظيف الملف
            self.progress_updated.emit(75, "تنظيف الملف...")
            cursor.execute("VACUUM")
            
            self.progress_updated.emit(100, "اكتمل التحسين!")
            conn.close()
            
            return True, "تم تحسين قاعدة البيانات بنجاح"
            
        except Exception as e:
            return False, f"خطأ في تحسين قاعدة البيانات: {e}"
            
    def repair_database(self):
        """إصلاح قاعدة البيانات"""
        try:
            conn = self.create_connection()
            if not conn:
                return False, "لا يمكن الاتصال بقاعدة البيانات"
                
            cursor = conn.cursor()
            
            # فحص سلامة قاعدة البيانات
            self.progress_updated.emit(20, "فحص سلامة قاعدة البيانات...")
            cursor.execute("PRAGMA integrity_check")
            result = cursor.fetchone()
            
            if result[0] != 'ok':
                self.progress_updated.emit(40, "إصلاح الأخطاء...")
                # محاولة إصلاح الأخطاء
                cursor.execute("PRAGMA quick_check")
                
            # إعادة بناء الفهارس
            self.progress_updated.emit(60, "إعادة بناء الفهارس...")
            cursor.execute("REINDEX")
            
            # تنظيف الملف
            self.progress_updated.emit(80, "تنظيف الملف...")
            cursor.execute("VACUUM")
            
            self.progress_updated.emit(100, "اكتمل الإصلاح!")
            conn.close()
            
            return True, "تم إصلاح قاعدة البيانات بنجاح"
            
        except Exception as e:
            return False, f"خطأ في إصلاح قاعدة البيانات: {e}"
            
    def export_to_excel(self, output_path):
        """تصدير قاعدة البيانات إلى Excel"""
        try:
            conn = self.create_connection()
            if not conn:
                return False, "لا يمكن الاتصال بقاعدة البيانات"
                
            cursor = conn.cursor()
            
            # الحصول على أسماء الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [table[0] for table in cursor.fetchall()]
            
            self.progress_updated.emit(10, "إنشاء ملف Excel...")
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                total_tables = len(tables)
                
                for i, table in enumerate(tables):
                    progress = int(((i + 1) / total_tables) * 80) + 10
                    self.progress_updated.emit(progress, f"تصدير جدول {table}...")
                    
                    # قراءة البيانات من الجدول
                    df = pd.read_sql_query(f"SELECT * FROM {table}", conn)
                    
                    # كتابة البيانات إلى Excel
                    df.to_excel(writer, sheet_name=table, index=False)
                    
            self.progress_updated.emit(100, "اكتمل التصدير!")
            conn.close()
            
            return True, f"تم تصدير قاعدة البيانات إلى: {output_path}"
            
        except Exception as e:
            return False, f"خطأ في التصدير: {e}"
            
    def import_from_excel(self, input_path):
        """استيراد البيانات من Excel"""
        try:
            conn = self.create_connection()
            if not conn:
                return False, "لا يمكن الاتصال بقاعدة البيانات"
                
            # قراءة ملف Excel
            self.progress_updated.emit(10, "قراءة ملف Excel...")
            excel_file = pd.ExcelFile(input_path)
            sheet_names = excel_file.sheet_names
            
            total_sheets = len(sheet_names)
            
            for i, sheet_name in enumerate(sheet_names):
                progress = int(((i + 1) / total_sheets) * 80) + 10
                self.progress_updated.emit(progress, f"استيراد ورقة {sheet_name}...")
                
                # قراءة البيانات
                df = pd.read_excel(input_path, sheet_name=sheet_name)
                
                # كتابة البيانات إلى قاعدة البيانات
                df.to_sql(sheet_name, conn, if_exists='replace', index=False)
                
            self.progress_updated.emit(100, "اكتمل الاستيراد!")
            conn.close()
            
            return True, f"تم استيراد البيانات من: {input_path}"
            
        except Exception as e:
            return False, f"خطأ في الاستيراد: {e}"
            
    def get_database_statistics(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            conn = self.create_connection()
            if not conn:
                return None
                
            cursor = conn.cursor()
            
            stats = {
                'total_employees': 0,
                'total_requests': 0,
                'approved_requests': 0,
                'pending_requests': 0,
                'rejected_requests': 0,
                'total_vacation_days': 0,
                'average_balance': 0
            }
            
            # إحصائيات الموظفين (إذا وجد الجدول)
            try:
                cursor.execute("SELECT COUNT(*) FROM employees")
                stats['total_employees'] = cursor.fetchone()[0]
            except:
                pass
                
            # إحصائيات الطلبات (إذا وجد الجدول)
            try:
                cursor.execute("SELECT COUNT(*) FROM vacation_requests")
                stats['total_requests'] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM vacation_requests WHERE status='approved'")
                stats['approved_requests'] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM vacation_requests WHERE status='pending'")
                stats['pending_requests'] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM vacation_requests WHERE status='rejected'")
                stats['rejected_requests'] = cursor.fetchone()[0]
                
                cursor.execute("SELECT SUM(days) FROM vacation_requests WHERE status='approved'")
                result = cursor.fetchone()[0]
                stats['total_vacation_days'] = result if result else 0
                
            except:
                pass
                
            # متوسط الرصيد (إذا وجد الجدول)
            try:
                cursor.execute("SELECT AVG(balance) FROM employee_balances")
                result = cursor.fetchone()[0]
                stats['average_balance'] = round(result, 2) if result else 0
            except:
                pass
                
            conn.close()
            return stats
            
        except Exception as e:
            print(f"خطأ في الحصول على الإحصائيات: {e}")
            return None

class ModernDatabaseWindow(ModernDialog):
    """نافذة إدارة قاعدة البيانات المتقدمة"""
    
    def __init__(self):
        super().__init__("🗄️ إدارة قاعدة البيانات المتقدمة", 900, 700)
        self.db_manager = DatabaseManager()
        self.setup_database_content()
        
    def setup_database_content(self):
        """إعداد محتوى إدارة قاعدة البيانات"""
        layout = QVBoxLayout(self.content_area)
        layout.setSpacing(20)
        
        # معلومات قاعدة البيانات
        self.create_database_info(layout)
        
        # إحصائيات قاعدة البيانات
        self.create_database_stats(layout)
        
        # أدوات قاعدة البيانات
        self.create_database_tools(layout)
        
        # شريط التقدم
        self.create_progress_section(layout)
        
        # إعداد الأزرار
        self.add_button("🔧 تحسين قاعدة البيانات", self.optimize_database, "primary")
        self.add_button("🛠️ إصلاح قاعدة البيانات", self.repair_database, "warning")
        self.add_button("📤 تصدير إلى Excel", self.export_database, "success")
        self.add_button("📥 استيراد من Excel", self.import_database, "secondary")
        self.add_button("🔄 تحديث المعلومات", self.refresh_info, "secondary")
        self.add_button("❌ إغلاق", self.accept, "secondary")
        
        # ربط الإشارات
        self.db_manager.operation_completed.connect(self.on_operation_completed)
        self.db_manager.progress_updated.connect(self.on_progress_updated)
        
    def create_database_info(self, layout):
        """إنشاء قسم معلومات قاعدة البيانات"""
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2c3e50, stop: 1 #3498db
                );
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        info_layout = QVBoxLayout(info_frame)
        
        # العنوان
        title_label = QLabel("📊 معلومات قاعدة البيانات")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: white;
            margin-bottom: 15px;
        """)
        info_layout.addWidget(title_label)
        
        # معلومات في شبكة
        info_grid = QGridLayout()
        
        # مسار الملف
        self.create_info_item(info_grid, 0, 0, "📁 مسار الملف:", "file_path_label")
        
        # حجم الملف
        self.create_info_item(info_grid, 0, 1, "💾 حجم الملف:", "file_size_label")
        
        # عدد الجداول
        self.create_info_item(info_grid, 1, 0, "📋 عدد الجداول:", "tables_count_label")
        
        # آخر تعديل
        self.create_info_item(info_grid, 1, 1, "⏰ آخر تعديل:", "last_modified_label")
        
        info_layout.addLayout(info_grid)
        
        # تحميل المعلومات
        self.load_database_info()
        
        layout.addWidget(info_frame)
        
    def create_info_item(self, layout, row, col, label_text, object_name):
        """إنشاء عنصر معلومة"""
        # التسمية
        label = QLabel(label_text)
        label.setStyleSheet("color: white; font-weight: bold; font-size: 14px;")
        layout.addWidget(label, row * 2, col)
        
        # القيمة
        value_label = QLabel("جاري التحميل...")
        value_label.setObjectName(object_name)
        value_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.9);
            font-size: 13px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            padding: 5px 10px;
            margin-top: 5px;
        """)
        layout.addWidget(value_label, row * 2 + 1, col)
        
        setattr(self, object_name, value_label)
        
    def create_database_stats(self, layout):
        """إنشاء قسم إحصائيات قاعدة البيانات"""
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 20px;
            }
        """)
        
        stats_layout = QVBoxLayout(stats_frame)
        
        # العنوان
        stats_title = QLabel("📈 إحصائيات قاعدة البيانات")
        stats_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        stats_layout.addWidget(stats_title)
        
        # بطاقات الإحصائيات
        stats_cards_layout = QHBoxLayout()
        
        # إحصائيات مختلفة
        stats_data = [
            ("👥", "إجمالي الموظفين", "0", "#3498db"),
            ("📝", "إجمالي الطلبات", "0", "#e74c3c"),
            ("✅", "طلبات مقبولة", "0", "#27ae60"),
            ("⏳", "طلبات معلقة", "0", "#f39c12"),
            ("❌", "طلبات مرفوضة", "0", "#e67e22"),
            ("📅", "أيام الإجازات", "0", "#9b59b6")
        ]
        
        self.stats_cards = {}
        
        for icon, title, value, color in stats_data:
            card = self.create_stats_card(icon, title, value, color)
            stats_cards_layout.addWidget(card)
            self.stats_cards[title] = card
            
        stats_layout.addLayout(stats_cards_layout)
        
        # تحميل الإحصائيات
        self.load_database_stats()
        
        layout.addWidget(stats_frame)
        
    def create_stats_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: {color};
                border-radius: 10px;
                padding: 15px;
                min-width: 120px;
                min-height: 80px;
            }}
        """)
        
        card_layout = QVBoxLayout(card)
        card_layout.setSpacing(5)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 24px; color: white;")
        card_layout.addWidget(icon_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")
        card_layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 11px; color: rgba(255, 255, 255, 0.9);")
        title_label.setWordWrap(True)
        card_layout.addWidget(title_label)
        
        # حفظ مرجع لتحديث القيمة
        card.value_label = value_label
        
        return card
        
    def create_database_tools(self, layout):
        """إنشاء قسم أدوات قاعدة البيانات"""
        tools_frame = QFrame()
        tools_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 10px;
                border: 2px solid #dee2e6;
                padding: 20px;
            }
        """)
        
        tools_layout = QVBoxLayout(tools_frame)
        
        # العنوان
        tools_title = QLabel("🔧 أدوات قاعدة البيانات")
        tools_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        tools_layout.addWidget(tools_title)
        
        # أزرار الأدوات
        tools_buttons_layout = QGridLayout()
        
        # معلومات الجداول
        tables_info_btn = self.create_tool_button(
            "📋 معلومات الجداول", 
            "عرض تفاصيل جميع الجداول",
            "#3498db",
            self.show_tables_info
        )
        tools_buttons_layout.addWidget(tables_info_btn, 0, 0)
        
        # فحص سلامة البيانات
        integrity_btn = self.create_tool_button(
            "🔍 فحص السلامة", 
            "فحص سلامة قاعدة البيانات",
            "#27ae60",
            self.check_integrity
        )
        tools_buttons_layout.addWidget(integrity_btn, 0, 1)
        
        # إدارة الفهارس
        indexes_btn = self.create_tool_button(
            "📇 إدارة الفهارس", 
            "عرض وإدارة فهارس قاعدة البيانات",
            "#f39c12",
            self.manage_indexes
        )
        tools_buttons_layout.addWidget(indexes_btn, 1, 0)
        
        # تنفيذ SQL
        sql_btn = self.create_tool_button(
            "💻 تنفيذ SQL", 
            "تنفيذ استعلامات SQL مخصصة",
            "#e74c3c",
            self.execute_sql
        )
        tools_buttons_layout.addWidget(sql_btn, 1, 1)
        
        tools_layout.addLayout(tools_buttons_layout)
        layout.addWidget(tools_frame)
        
    def create_tool_button(self, title, description, color, callback):
        """إنشاء زر أداة"""
        btn = QPushButton()
        btn.setStyleSheet(f"""
            QPushButton {{
                background: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px;
                text-align: left;
                font-weight: bold;
                min-height: 60px;
            }}
            QPushButton:hover {{
                background: rgba(0, 0, 0, 0.1);
            }}
        """)
        
        btn_layout = QVBoxLayout(btn)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: white;")
        btn_layout.addWidget(title_label)
        
        # الوصف
        desc_label = QLabel(description)
        desc_label.setStyleSheet("font-size: 12px; color: rgba(255, 255, 255, 0.8);")
        desc_label.setWordWrap(True)
        btn_layout.addWidget(desc_label)
        
        btn.clicked.connect(callback)
        
        return btn
        
    def create_progress_section(self, layout):
        """إنشاء قسم التقدم"""
        progress_frame = QFrame()
        progress_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 20px;
            }
        """)
        
        progress_layout = QVBoxLayout(progress_frame)
        
        # العنوان
        progress_title = QLabel("⏳ تقدم العملية")
        progress_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        progress_layout.addWidget(progress_title)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #3498db;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                background: white;
            }
            QProgressBar::chunk {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #3498db, stop: 1 #2980b9
                );
                border-radius: 6px;
            }
        """)
        progress_layout.addWidget(self.progress_bar)
        
        # رسالة التقدم
        self.progress_label = QLabel("جاهز لتنفيذ العمليات")
        self.progress_label.setStyleSheet("""
            font-size: 14px;
            color: #2c3e50;
            margin-top: 10px;
        """)
        progress_layout.addWidget(self.progress_label)
        
        layout.addWidget(progress_frame)
        
    def load_database_info(self):
        """تحميل معلومات قاعدة البيانات"""
        info = self.db_manager.get_database_info()
        
        if info:
            self.file_path_label.setText(info['file_path'])
            self.file_size_label.setText(f"{info['file_size'] / 1024:.2f} KB")
            self.tables_count_label.setText(str(info['tables_count']))
            self.last_modified_label.setText(info['last_modified'])
        else:
            self.file_path_label.setText("غير متاح")
            self.file_size_label.setText("غير متاح")
            self.tables_count_label.setText("غير متاح")
            self.last_modified_label.setText("غير متاح")
            
    def load_database_stats(self):
        """تحميل إحصائيات قاعدة البيانات"""
        stats = self.db_manager.get_database_statistics()
        
        if stats:
            self.stats_cards["إجمالي الموظفين"].value_label.setText(str(stats['total_employees']))
            self.stats_cards["إجمالي الطلبات"].value_label.setText(str(stats['total_requests']))
            self.stats_cards["طلبات مقبولة"].value_label.setText(str(stats['approved_requests']))
            self.stats_cards["طلبات معلقة"].value_label.setText(str(stats['pending_requests']))
            self.stats_cards["طلبات مرفوضة"].value_label.setText(str(stats['rejected_requests']))
            self.stats_cards["أيام الإجازات"].value_label.setText(str(stats['total_vacation_days']))
            
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        reply = QMessageBox.question(
            self, "تأكيد", 
            "هل تريد تحسين قاعدة البيانات؟\n\nهذه العملية قد تستغرق بعض الوقت.",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # تشغيل التحسين في خيط منفصل
            self.run_database_operation(self.db_manager.optimize_database)
            
    def repair_database(self):
        """إصلاح قاعدة البيانات"""
        reply = QMessageBox.question(
            self, "تأكيد", 
            "هل تريد إصلاح قاعدة البيانات؟\n\nهذه العملية قد تستغرق بعض الوقت.",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.run_database_operation(self.db_manager.repair_database)
            
    def export_database(self):
        """تصدير قاعدة البيانات"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ تصدير قاعدة البيانات", 
            f"database_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            "Excel Files (*.xlsx)"
        )
        
        if file_path:
            self.run_database_operation(lambda: self.db_manager.export_to_excel(file_path))
            
    def import_database(self):
        """استيراد قاعدة البيانات"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار ملف للاستيراد", "",
            "Excel Files (*.xlsx);;All Files (*)"
        )
        
        if file_path:
            reply = QMessageBox.warning(
                self, "تحذير", 
                "تحذير: سيتم استبدال البيانات الموجودة!\n\nهل تريد المتابعة؟",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.run_database_operation(lambda: self.db_manager.import_from_excel(file_path))
                
    def refresh_info(self):
        """تحديث المعلومات"""
        self.load_database_info()
        self.load_database_stats()
        QMessageBox.information(self, "تم", "تم تحديث المعلومات")
        
    def show_tables_info(self):
        """عرض معلومات الجداول"""
        info = self.db_manager.get_database_info()
        
        if info and info['tables']:
            tables_info = "📋 معلومات الجداول:\n\n"
            
            for table in info['tables']:
                tables_info += f"🔹 {table['name']}:\n"
                tables_info += f"   • عدد الصفوف: {table['rows']}\n"
                tables_info += f"   • عدد الأعمدة: {table['columns']}\n\n"
                
            QMessageBox.information(self, "معلومات الجداول", tables_info)
        else:
            QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على معلومات الجداول")
            
    def check_integrity(self):
        """فحص سلامة قاعدة البيانات"""
        try:
            conn = self.db_manager.create_connection()
            if conn:
                cursor = conn.cursor()
                cursor.execute("PRAGMA integrity_check")
                result = cursor.fetchone()
                conn.close()
                
                if result[0] == 'ok':
                    QMessageBox.information(self, "فحص السلامة", "✅ قاعدة البيانات سليمة")
                else:
                    QMessageBox.warning(self, "فحص السلامة", f"⚠️ مشاكل في قاعدة البيانات:\n{result[0]}")
            else:
                QMessageBox.critical(self, "خطأ", "لا يمكن الاتصال بقاعدة البيانات")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فحص السلامة:\n{str(e)}")
            
    def manage_indexes(self):
        """إدارة الفهارس"""
        QMessageBox.information(self, "إدارة الفهارس", "ستتم إضافة إدارة الفهارس قريباً")
        
    def execute_sql(self):
        """تنفيذ SQL"""
        QMessageBox.information(self, "تنفيذ SQL", "ستتم إضافة واجهة تنفيذ SQL قريباً")
        
    def run_database_operation(self, operation):
        """تشغيل عملية قاعدة البيانات"""
        # هنا يجب تشغيل العملية في خيط منفصل
        # لكن للبساطة سنقوم بتشغيلها مباشرة
        self.progress_bar.setValue(0)
        self.progress_label.setText("جاري تنفيذ العملية...")
        
        QApplication.processEvents()
        
        try:
            success, message = operation()
            self.operation_completed.emit(success, message)
        except Exception as e:
            self.operation_completed.emit(False, str(e))
            
    def on_progress_updated(self, value, message):
        """تحديث التقدم"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)
        QApplication.processEvents()
        
    def on_operation_completed(self, success, message):
        """عند اكتمال العملية"""
        if success:
            QMessageBox.information(self, "نجح", message)
            self.refresh_info()
        else:
            QMessageBox.critical(self, "خطأ", message)
            
        self.progress_bar.setValue(0)
        self.progress_label.setText("جاهز لتنفيذ العمليات")

def test_database_manager():
    """اختبار مدير قاعدة البيانات"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # اختبار نافذة إدارة قاعدة البيانات
    db_window = ModernDatabaseWindow()
    db_window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    test_database_manager()
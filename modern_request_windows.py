#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نوافذ الطلبات المتقدمة للواجهة الحديثة
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime, timedelta
from modern_dialogs import ModernDialog

class ModernHourlyRequestWindow(ModernDialog):
    """نافذة طلب الإجازة الساعية المتقدمة"""
    
    def __init__(self):
        super().__init__("⏱️ طلب إجازة ساعية متقدم", 800, 650)
        self.setup_hourly_content()
        
    def setup_hourly_content(self):
        """إعداد محتوى النافذة"""
        layout = QVBoxLayout(self.content_area)
        layout.setSpacing(20)
        
        # معلومات النافذة
        self.create_header_section(layout)
        
        # نموذج البيانات
        self.create_form_section(layout)
        
        # حاسبة الإجازة الساعية
        self.create_calculator_section(layout)
        
        # معاينة النتائج
        self.create_preview_section(layout)
        
        # إعداد الأزرار
        self.add_button("🧮 حساب", self.calculate_hours, "secondary")
        self.add_button("💾 حفظ الطلب", self.save_request, "primary")
        self.add_button("❌ إلغاء", self.reject, "secondary")
        
    def create_header_section(self, layout):
        """إنشاء قسم العنوان"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #f39c12, stop: 1 #e67e22
                );
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        header_layout = QVBoxLayout(header_frame)
        
        title_label = QLabel("⏱️ طلب إجازة ساعية مع حاسبة متقدمة")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: white;
            background: transparent;
        """)
        header_layout.addWidget(title_label)
        
        desc_label = QLabel("احسب إجازتك الساعية بدقة وسهولة")
        desc_label.setStyleSheet("""
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            background: transparent;
        """)
        header_layout.addWidget(desc_label)
        
        layout.addWidget(header_frame)
        
    def create_form_section(self, layout):
        """إنشاء قسم النموذج"""
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 25px;
            }
        """)
        
        form_layout = QGridLayout(form_frame)
        form_layout.setSpacing(15)
        
        # الحقول الأساسية
        self.create_form_field(form_layout, 0, "👤 اسم الموظف:", "employee_name", "أدخل الاسم الكامل...")
        self.create_form_field(form_layout, 1, "🆔 رقم الموظف:", "employee_id", "رقم الموظف...")
        self.create_form_field(form_layout, 2, "🏢 القسم:", "department", "اسم القسم...")
        
        # التاريخ والوقت
        date_label = QLabel("📅 تاريخ الإجازة:")
        date_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        form_layout.addWidget(date_label, 3, 0)
        
        self.date_edit = QDateEdit(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setStyleSheet(self.get_input_style())
        form_layout.addWidget(self.date_edit, 3, 1)
        
        layout.addWidget(form_frame)
        
    def create_calculator_section(self, layout):
        """إنشاء قسم الحاسبة"""
        calc_frame = QFrame()
        calc_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #9b59b6, stop: 1 #8e44ad
                );
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        calc_layout = QVBoxLayout(calc_frame)
        
        # عنوان الحاسبة
        calc_title = QLabel("🧮 حاسبة الإجازة الساعية")
        calc_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
            background: transparent;
            margin-bottom: 15px;
        """)
        calc_layout.addWidget(calc_title)
        
        # خيارات الحساب
        options_layout = QGridLayout()
        
        # طريقة الحساب
        method_label = QLabel("📊 طريقة الحساب:")
        method_label.setStyleSheet("color: white; font-weight: bold;")
        options_layout.addWidget(method_label, 0, 0)
        
        self.calc_method = QComboBox()
        self.calc_method.addItems([
            "حساب بالساعات المحددة",
            "حساب من وقت إلى وقت",
            "حساب بنسبة من اليوم"
        ])
        self.calc_method.setStyleSheet("""
            QComboBox {
                background: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        self.calc_method.currentTextChanged.connect(self.on_method_changed)
        options_layout.addWidget(self.calc_method, 0, 1)
        
        # الساعات المحددة
        hours_label = QLabel("🕐 عدد الساعات:")
        hours_label.setStyleSheet("color: white; font-weight: bold;")
        options_layout.addWidget(hours_label, 1, 0)
        
        self.hours_spinbox = QDoubleSpinBox()
        self.hours_spinbox.setRange(0.5, 24.0)
        self.hours_spinbox.setSingleStep(0.5)
        self.hours_spinbox.setValue(4.0)
        self.hours_spinbox.setSuffix(" ساعة")
        self.hours_spinbox.setStyleSheet("""
            QDoubleSpinBox {
                background: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        options_layout.addWidget(self.hours_spinbox, 1, 1)
        
        # من وقت إلى وقت
        from_time_label = QLabel("🕘 من الساعة:")
        from_time_label.setStyleSheet("color: white; font-weight: bold;")
        options_layout.addWidget(from_time_label, 2, 0)
        
        self.from_time = QTimeEdit(QTime(9, 0))
        self.from_time.setDisplayFormat("hh:mm")
        self.from_time.setStyleSheet("""
            QTimeEdit {
                background: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        self.from_time.setEnabled(False)
        options_layout.addWidget(self.from_time, 2, 1)
        
        to_time_label = QLabel("🕕 إلى الساعة:")
        to_time_label.setStyleSheet("color: white; font-weight: bold;")
        options_layout.addWidget(to_time_label, 3, 0)
        
        self.to_time = QTimeEdit(QTime(13, 0))
        self.to_time.setDisplayFormat("hh:mm")
        self.to_time.setStyleSheet("""
            QTimeEdit {
                background: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        self.to_time.setEnabled(False)
        options_layout.addWidget(self.to_time, 3, 1)
        
        # نسبة من اليوم
        percentage_label = QLabel("📊 نسبة من اليوم:")
        percentage_label.setStyleSheet("color: white; font-weight: bold;")
        options_layout.addWidget(percentage_label, 4, 0)
        
        self.percentage_spinbox = QDoubleSpinBox()
        self.percentage_spinbox.setRange(1.0, 100.0)
        self.percentage_spinbox.setSingleStep(5.0)
        self.percentage_spinbox.setValue(50.0)
        self.percentage_spinbox.setSuffix("%")
        self.percentage_spinbox.setStyleSheet("""
            QDoubleSpinBox {
                background: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        self.percentage_spinbox.setEnabled(False)
        options_layout.addWidget(self.percentage_spinbox, 4, 1)
        
        calc_layout.addLayout(options_layout)
        layout.addWidget(calc_frame)
        
    def create_preview_section(self, layout):
        """إنشاء قسم المعاينة"""
        preview_frame = QFrame()
        preview_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 20px;
            }
        """)
        
        preview_layout = QVBoxLayout(preview_frame)
        
        preview_title = QLabel("📋 معاينة النتائج")
        preview_title.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 16px;")
        preview_layout.addWidget(preview_title)
        
        # جدول النتائج
        self.results_table = QTableWidget()
        self.results_table.setRowCount(4)
        self.results_table.setColumnCount(2)
        self.results_table.setHorizontalHeaderLabels(["العنصر", "القيمة"])
        self.results_table.setStyleSheet("""
            QTableWidget {
                background: #f8f9fa;
                border: 1px solid #e0e0e0;
                border-radius: 5px;
                gridline-color: #e0e0e0;
            }
            QHeaderView::section {
                background: #e9ecef;
                border: 1px solid #dee2e6;
                padding: 8px;
                font-weight: bold;
            }
        """)
        
        # بيانات افتراضية
        self.update_results_preview()
        
        self.results_table.resizeColumnsToContents()
        self.results_table.horizontalHeader().setStretchLastSection(True)
        preview_layout.addWidget(self.results_table)
        
        layout.addWidget(preview_frame)
        
    def create_form_field(self, layout, row, label_text, object_name, placeholder):
        """إنشاء حقل نموذج"""
        label = QLabel(label_text)
        label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        layout.addWidget(label, row, 0)
        
        edit = QLineEdit()
        edit.setObjectName(object_name)
        edit.setPlaceholderText(placeholder)
        edit.setMinimumHeight(40)
        edit.setMaximumHeight(50)
        edit.setStyleSheet(self.get_input_style())
        layout.addWidget(edit, row, 1)
        
        setattr(self, object_name, edit)
        
    def get_input_style(self):
        """الحصول على نمط حقول الإدخال"""
        return """
            QLineEdit, QDateEdit {
                background: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
                min-height: 40px;
            }
            QLineEdit:focus, QDateEdit:focus {
                border: 2px solid #3498db;
            }
        """
        
    def on_method_changed(self):
        """عند تغيير طريقة الحساب"""
        method = self.calc_method.currentText()
        
        # تفعيل/تعطيل الحقول حسب الطريقة
        if "ساعات المحددة" in method:
            self.hours_spinbox.setEnabled(True)
            self.from_time.setEnabled(False)
            self.to_time.setEnabled(False)
            self.percentage_spinbox.setEnabled(False)
        elif "وقت إلى وقت" in method:
            self.hours_spinbox.setEnabled(False)
            self.from_time.setEnabled(True)
            self.to_time.setEnabled(True)
            self.percentage_spinbox.setEnabled(False)
        elif "نسبة" in method:
            self.hours_spinbox.setEnabled(False)
            self.from_time.setEnabled(False)
            self.to_time.setEnabled(False)
            self.percentage_spinbox.setEnabled(True)
            
        # تحديث المعاينة
        self.update_results_preview()
        
    def calculate_hours(self):
        """حساب الساعات"""
        method = self.calc_method.currentText()
        
        if "ساعات المحددة" in method:
            hours = self.hours_spinbox.value()
        elif "وقت إلى وقت" in method:
            from_time = self.from_time.time()
            to_time = self.to_time.time()
            # حساب الفرق بالساعات
            from_minutes = from_time.hour() * 60 + from_time.minute()
            to_minutes = to_time.hour() * 60 + to_time.minute()
            hours = (to_minutes - from_minutes) / 60.0
            if hours < 0:
                hours += 24  # في حالة عبور منتصف الليل
        elif "نسبة" in method:
            percentage = self.percentage_spinbox.value()
            hours = (8 * percentage) / 100.0  # افتراض 8 ساعات يوم عمل
            
        # تحديث قيمة الساعات
        self.hours_spinbox.setValue(hours)
        self.update_results_preview()
        
        QMessageBox.information(self, "تم الحساب", f"تم حساب {hours:.2f} ساعة بنجاح!")
        
    def update_results_preview(self):
        """تحديث معاينة النتائج"""
        method = self.calc_method.currentText()
        
        if "ساعات المحددة" in method:
            hours = self.hours_spinbox.value()
        elif "وقت إلى وقت" in method:
            from_time = self.from_time.time()
            to_time = self.to_time.time()
            from_minutes = from_time.hour() * 60 + from_time.minute()
            to_minutes = to_time.hour() * 60 + to_time.minute()
            hours = abs(to_minutes - from_minutes) / 60.0
        elif "نسبة" in method:
            percentage = self.percentage_spinbox.value()
            hours = (8 * percentage) / 100.0
        else:
            hours = 0
            
        # تحويل الساعات إلى أيام (8 ساعات = يوم واحد)
        days_equivalent = hours / 8.0
        
        # تحديث الجدول
        items = [
            ("طريقة الحساب", method),
            ("عدد الساعات", f"{hours:.2f} ساعة"),
            ("المعادل بالأيام", f"{days_equivalent:.3f} يوم"),
            ("تاريخ الإجازة", self.date_edit.date().toString("yyyy-MM-dd"))
        ]
        
        for row, (label, value) in enumerate(items):
            self.results_table.setItem(row, 0, QTableWidgetItem(label))
            self.results_table.setItem(row, 1, QTableWidgetItem(value))
            
    def save_request(self):
        """حفظ طلب الإجازة"""
        # التحقق من البيانات
        if not self.employee_name.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الموظف")
            return
            
        # الحصول على البيانات
        hours = self.hours_spinbox.value()
        days_equivalent = hours / 8.0
        
        # رسالة التأكيد
        msg = QMessageBox.question(
            self, 
            "تأكيد الحفظ", 
            f"هل تريد حفظ طلب الإجازة الساعية؟\n\n"
            f"الموظف: {self.employee_name.text()}\n"
            f"عدد الساعات: {hours:.2f} ساعة\n"
            f"المعادل: {days_equivalent:.3f} يوم\n"
            f"التاريخ: {self.date_edit.date().toString('yyyy-MM-dd')}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )
        
        if msg == QMessageBox.Yes:
            # هنا سيتم إضافة كود الحفظ الفعلي
            QMessageBox.information(self, "نجح", "تم حفظ طلب الإجازة الساعية بنجاح!")
            self.accept()

class ModernAddVacationWindow(ModernDialog):
    """نافذة إدراج الإجازات الإضافية"""
    
    def __init__(self):
        super().__init__("➕ إدراج إجازات إضافية", 750, 600)
        self.setup_add_vacation_content()
        
    def setup_add_vacation_content(self):
        """إعداد محتوى النافذة"""
        layout = QVBoxLayout(self.content_area)
        layout.setSpacing(20)
        
        # معلومات النافذة
        self.create_info_section(layout)
        
        # نموذج البيانات
        self.create_vacation_form(layout)
        
        # قسم الأسباب المعتادة
        self.create_reasons_section(layout)
        
        # إعداد الأزرار
        self.add_button("💾 إدراج الإجازة", self.add_vacation, "primary")
        self.add_button("❌ إلغاء", self.reject, "secondary")
        
    def create_info_section(self, layout):
        """إنشاء قسم المعلومات"""
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #27ae60, stop: 1 #229954
                );
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        info_layout = QVBoxLayout(info_frame)
        
        title_label = QLabel("➕ إدراج أيام إجازة إضافية للموظف")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: white;
            background: transparent;
        """)
        info_layout.addWidget(title_label)
        
        desc_label = QLabel("أضف أيام إجازة إضافية للموظفين كمكافأة أو تعويض")
        desc_label.setStyleSheet("""
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            background: transparent;
        """)
        info_layout.addWidget(desc_label)
        
        layout.addWidget(info_frame)
        
    def create_vacation_form(self, layout):
        """إنشاء نموذج الإدراج"""
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 10px;
                border: 2px solid #e0e0e0;
                padding: 25px;
            }
        """)
        
        form_layout = QGridLayout(form_frame)
        form_layout.setSpacing(15)
        
        # الحقول
        self.create_form_field(form_layout, 0, "👤 اسم الموظف:", "employee_name", "أدخل الاسم...")
        self.create_form_field(form_layout, 1, "🆔 رقم الموظف:", "employee_id", "رقم الموظف...")
        
        # عدد الأيام
        days_label = QLabel("📅 عدد الأيام المضافة:")
        days_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        form_layout.addWidget(days_label, 2, 0)
        
        self.days_spinbox = QSpinBox()
        self.days_spinbox.setRange(1, 100)
        self.days_spinbox.setValue(5)
        self.days_spinbox.setSuffix(" يوم")
        self.days_spinbox.setStyleSheet("""
            QSpinBox {
                background: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
            }
        """)
        form_layout.addWidget(self.days_spinbox, 2, 1)
        
        # تاريخ الإدراج
        date_label = QLabel("📅 تاريخ الإدراج:")
        date_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        form_layout.addWidget(date_label, 3, 0)
        
        self.date_edit = QDateEdit(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setStyleSheet("""
            QDateEdit {
                background: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
            }
        """)
        form_layout.addWidget(self.date_edit, 3, 1)
        
        # السبب
        reason_label = QLabel("📝 سبب الإدراج:")
        reason_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        form_layout.addWidget(reason_label, 4, 0)
        
        self.reason_text = QTextEdit()
        self.reason_text.setPlaceholderText("أدخل سبب إدراج الإجازة الإضافية...")
        self.reason_text.setMinimumHeight(80)
        self.reason_text.setMaximumHeight(120)
        self.reason_text.setStyleSheet("""
            QTextEdit {
                background: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
                min-height: 80px;
            }
        """)
        form_layout.addWidget(self.reason_text, 4, 1)
        
        layout.addWidget(form_frame)
        
    def create_reasons_section(self, layout):
        """إنشاء قسم الأسباب المعتادة"""
        reasons_frame = QFrame()
        reasons_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border-radius: 10px;
                border: 2px solid #e9ecef;
                padding: 20px;
            }
        """)
        
        reasons_layout = QVBoxLayout(reasons_frame)
        
        reasons_title = QLabel("💡 أسباب شائعة للإدراج")
        reasons_title.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 16px;")
        reasons_layout.addWidget(reasons_title)
        
        # أزرار الأسباب السريعة
        buttons_layout = QGridLayout()
        
        common_reasons = [
            "🏆 مكافأة أداء ممتاز",
            "⚡ تعويض عمل إضافي",
            "🎯 مكافأة إنجاز مشروع",
            "🎉 مكافأة سنوية",
            "💼 تعويض عمل في إجازة",
            "🌟 مكافأة خدمة متميزة"
        ]
        
        for i, reason in enumerate(common_reasons):
            btn = QPushButton(reason)
            btn.setStyleSheet("""
                QPushButton {
                    background: white;
                    border: 2px solid #e0e0e0;
                    border-radius: 8px;
                    padding: 10px;
                    text-align: left;
                    font-size: 13px;
                }
                QPushButton:hover {
                    border: 2px solid #3498db;
                    color: #3498db;
                }
            """)
            btn.clicked.connect(lambda checked, text=reason: self.reason_text.setText(text))
            buttons_layout.addWidget(btn, i // 2, i % 2)
            
        reasons_layout.addLayout(buttons_layout)
        layout.addWidget(reasons_frame)
        
    def create_form_field(self, layout, row, label_text, object_name, placeholder):
        """إنشاء حقل نموذج"""
        label = QLabel(label_text)
        label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        layout.addWidget(label, row, 0)
        
        edit = QLineEdit()
        edit.setObjectName(object_name)
        edit.setPlaceholderText(placeholder)
        edit.setMinimumHeight(40)
        edit.setMaximumHeight(50)
        edit.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
                min-height: 40px;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
        """)
        layout.addWidget(edit, row, 1)
        
        setattr(self, object_name, edit)
        
    def add_vacation(self):
        """إدراج الإجازة"""
        # التحقق من البيانات
        if not self.employee_name.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الموظف")
            return
            
        if not self.reason_text.toPlainText().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سبب الإدراج")
            return
            
        # رسالة التأكيد
        msg = QMessageBox.question(
            self,
            "تأكيد الإدراج",
            f"هل تريد إدراج الإجازة الإضافية؟\n\n"
            f"الموظف: {self.employee_name.text()}\n"
            f"عدد الأيام: {self.days_spinbox.value()} يوم\n"
            f"التاريخ: {self.date_edit.date().toString('yyyy-MM-dd')}\n"
            f"السبب: {self.reason_text.toPlainText()[:50]}...",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )
        
        if msg == QMessageBox.Yes:
            # هنا سيتم إضافة كود الحفظ الفعلي
            QMessageBox.information(self, "نجح", 
                                   f"تم إدراج {self.days_spinbox.value()} يوم للموظف بنجاح!")
            self.accept()

def test_request_windows():
    """اختبار نوافذ الطلبات"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # اختبار نافذة الإجازة الساعية
    hourly_window = ModernHourlyRequestWindow()
    hourly_window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    test_request_windows()
# نظام إدارة الإجازات - دليل التشغيل السريع

## 🎯 نظرة عامة
نظام شامل لإدارة إجازات الموظفين مع واجهة مستخدم حديثة وقاعدة بيانات متقدمة.

## 🚀 التشغيل السريع

### الطريقة الأولى: استخدام الملف التنفيذي (الأسهل)
```bash
# انقر مرتين على الملف
تشغيل_سريع.bat
```

### الطريقة الثانية: استخدام Python مباشرة
```bash
python quick_start.py
```

### الطريقة الثالثة: تشغيل التطبيق الرئيسي
```bash
python main.py
```

## 📋 المتطلبات
- Python 3.7 أو أحدث
- المكتبات المطلوبة (سيتم تثبيتها تلقائياً):
  - PyQt5
  - pandas
  - openpyxl
  - sqlite3 (مدمج في Python)

## 🔧 الإعداد الأولي

### 1. تثبيت المكتبات
```bash
pip install -r requirements.txt
```

### 2. إنشاء البيانات النموذجية
```bash
python create_sample_excel.py
```

### 3. تشغيل النظام
```bash
python main.py
```

## 👤 تسجيل الدخول الافتراضي
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## 🗂️ هيكل الملفات

### الملفات الأساسية
- `database.py` - إدارة قاعدة البيانات
- `main.py` - التطبيق الرئيسي
- `create_sample_excel.py` - إنشاء البيانات النموذجية
- `quick_start.py` - التشغيل السريع مع التحقق من المتطلبات

### ملفات الواجهة
- `main_window.py` - النافذة الرئيسية
- `modern_*.py` - الواجهات الحديثة والمتقدمة

### ملفات البيانات
- `vacation_system.db` - قاعدة البيانات الرئيسية
- `نموذج_الرصيد_الابتدائي.xlsx` - البيانات النموذجية

## 🎮 كيفية الاستخدام

### 1. استيراد الرصيد الابتدائي
- اذهب إلى قائمة "البيانات"
- اختر "استيراد الرصيد الابتدائي"
- حدد ملف Excel المحتوي على بيانات الموظفين

### 2. إضافة طلب إجازة يومية
- اذهب إلى "طلبات الإجازة"
- اختر "إجازة يومية"
- املأ البيانات المطلوبة

### 3. إضافة طلب إجازة ساعية
- اذهب إلى "طلبات الإجازة"
- اختر "إجازة ساعية"
- حدد عدد الساعات والتاريخ

### 4. عرض رصيد الموظف
- اذهب إلى "الاستعلامات"
- اختر "رصيد الموظف"
- ابحث عن الموظف المطلوب

### 5. توليد التقارير
- اذهب إلى "التقارير"
- اختر نوع التقرير المطلوب
- حدد الفترة الزمنية

## 🔍 استكشاف الأخطاء

### مشكلة: لا يعمل التطبيق
**الحل:**
1. تأكد من تثبيت Python 3.7+
2. تأكد من تثبيت جميع المكتبات المطلوبة
3. استخدم `quick_start.py` للتحقق التلقائي

### مشكلة: خطأ في استيراد البيانات
**الحل:**
1. تأكد من صحة تنسيق ملف Excel
2. تأكد من وجود الأعمدة المطلوبة:
   - الاسم واللقب
   - الرتبة
   - عدد الأيام
   - التاريخ

### مشكلة: لا تظهر الواجهة
**الحل:**
1. تأكد من تثبيت PyQt5 بشكل صحيح
2. جرب تشغيل التطبيق من سطر الأوامر
3. تحقق من رسائل الخطأ

## 📊 قاعدة البيانات

### الجداول الرئيسية
- `users` - المستخدمين
- `initial_balance` - الرصيد الابتدائي
- `daily_requests` - طلبات الإجازة اليومية
- `hourly_requests` - طلبات الإجازة الساعية
- `added_vacations` - الإجازات المدرجة
- `archived_data` - البيانات المؤرشفة

## 🔒 الأمان
- تشفير كلمات المرور
- صلاحيات المستخدمين
- تسجيل العمليات
- نسخ احتياطية تلقائية

## 📞 الدعم الفني
في حالة وجود مشاكل:
1. تحقق من ملف `دليل_التشغيل_السريع.md`
2. استخدم `quick_start.py` للتشخيص التلقائي
3. تحقق من ملفات السجل

## 🎉 ميزات إضافية
- واجهة مستخدم حديثة ومتجاوبة
- دعم اللغة العربية بالكامل
- تقارير تفصيلية وإحصائيات
- نظام بحث متقدم
- أرشفة البيانات
- نسخ احتياطية

---
**تم تطوير النظام بواسطة:** فريق تطوير نظام إدارة الإجازات  
**التاريخ:** 2024  
**الإصدار:** 1.0
